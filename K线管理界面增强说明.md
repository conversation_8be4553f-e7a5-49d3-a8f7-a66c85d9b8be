# K线管理界面增强功能说明

## 概述
对K线管理界面的"测试最大涨跌幅"功能进行了增强，新增了两项数据记录功能，并修改了数据保存逻辑。

## 新增功能

### 1. 安全拉升时间 (safe_rise_duration)
- **定义**: 从当前组的end_time到下一组的start_time之间的K线周期数量
- **单位**: K线周期数（与period计算方式相同）
- **计算方式**: 下一组开始索引 - 当前组结束索引

### 2. 最大涨幅 (max_rise)
- **定义**: 从当前组的最低价到下一组start_time最高价的涨幅
- **计算公式**: (下一组开始最高价 - 当前组最低价) / 当前组最低价
- **表示方式**: 小数形式（如0.052表示5.2%的涨幅）
- **数据来源**: 使用临时存储的最低价，避免重复查找提高效率

## 修改的数据保存逻辑

### 原逻辑
- 找到满足反弹条件的K线后，立即保存该组数据

### 新逻辑
- 找到满足反弹条件的K线后，不立即保存
- 将数据标记为"待完善"状态
- 继续遍历K线，寻找下一组数据
- 当找到下一组数据时，计算上一组的安全拉升时间和最大涨幅
- 完善上一组数据后保存到结果列表
- 最后一组数据的安全拉升时间和最大涨幅设为None

## CSV文件字段更新

### 新的字段顺序
```
start_time, min_price_time, end_time, type, drawdown, max_rise, rebound,
fall_duration, rebound_duration, period, safe_rise_duration
```

### 字段说明
- `start_time`: 开始时间
- `min_price_time`: 最低价时间
- `end_time`: 结束时间（反弹成功时间）
- `type`: 情况类型（1-4）
- `drawdown`: 最大跌幅
- `max_rise`: **新增** 最大涨幅（放在rebound前面）
- `rebound`: 反弹幅度
- `fall_duration`: 下跌时长
- `rebound_duration`: 反弹时长
- `period`: 总周期
- `safe_rise_duration`: **新增** 安全拉升时间（放在最后）

## 示例数据对比

### 原数据示例
```
start_time: 2019/09/09 19:24
end_time: 2019/09/13 05:21
rebound: 0.051569609
```

### 增强后数据示例
```
start_time: 2019/09/09 19:24
end_time: 2019/09/13 05:21
rebound: 0.051569609
safe_rise_duration: 4  (假设为4个15分钟周期)
max_rise: 0.052  (假设从最低价到下一组开始涨了5.2%)
```

## 日志输出增强

### 新增日志信息
- 每组数据完成时：显示完整的一行日志信息，包含所有计算结果
- 最后一组数据：也能计算安全拉升时间和最大涨幅，使用剩余K线数据

### 日志示例
```
[1] 2019/09/09 19:24 ~ 2019/09/13 05:21 类型1 跌幅: -0.56% 反弹: 5.16% 最大涨幅: 7.18% 时长: 4917周期 安全拉升: 4周期
[2] 2019/09/13 06:16 ~ 2019/09/20 00:31 类型1 跌幅: -0.88% 反弹: 6.08% 最大涨幅: 4.59% 时长: 9735周期 安全拉升: 12周期
[3] 2019/09/20 04:02 ~ 2019/09/25 03:05 类型1 跌幅: -1.92% 反弹: 5.12% 最大涨幅: 3.45% 时长: 7143周期 安全拉升: 156周期
```

## 技术实现细节

### 核心修改点
1. 引入`pending_result`变量存储待完善的数据
2. 安全拉升时间使用索引差值计算（与period相同方式）
3. 最大涨幅使用下一组开始时的最高价而非开盘价
4. 使用临时存储的最低价提高计算效率，避免重复查找
5. 调整CSV字段顺序：max_rise在rebound前，safe_rise_duration在最后

### 兼容性
- 保持原有功能不变
- 新增字段不影响现有数据处理逻辑
- 支持所有K线周期的时间计算

## 使用方法
1. 在K线管理界面中点击"测试"按钮
2. 输入反弹/回调百分比（如5表示5%）
3. 系统将自动计算并保存增强后的数据到CSV文件
4. 文件保存在`kline_test_results`文件夹中

## 注意事项
- 最后一组数据也能计算`safe_rise_duration`和`max_rise`，使用剩余的K线数据
- 安全拉升时间：从最后一组结束到数据末尾的K线数量
- 最大涨幅：从最后一组最低价到剩余K线中最高价的涨幅
- 时间计算基于K线周期，确保数据的准确性
- 新功能完全向后兼容，不影响现有工作流程
