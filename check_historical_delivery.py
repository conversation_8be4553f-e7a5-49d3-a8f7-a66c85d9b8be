#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查币安历史交割合约的脚本
"""

import os
import json
from binance.um_futures import UMFutures
from dotenv import load_dotenv
from datetime import datetime
import requests

def check_historical_delivery_contracts():
    """检查历史交割合约信息"""
    
    # 加载环境变量
    load_dotenv('1.env')
    API_KEY = os.getenv('API_KEY')
    SECRET_KEY = os.getenv('SECRET_KEY')
    
    # 创建客户端
    client = UMFutures(key=API_KEY, secret=SECRET_KEY)
    
    try:
        print("=== 检查历史交割合约信息 ===\n")
        
        # 测试主要币种的历史交割价格
        test_pairs = ['BTCUSDT', 'ETHUSDT']
        
        for pair in test_pairs:
            print(f"查询 {pair} 的历史交割价格...")
            try:
                # 直接调用delivery_price API获取历史交割信息
                url = f"https://fapi.binance.com/futures/data/delivery-price?pair={pair}"
                response = requests.get(url)

                if response.status_code == 200:
                    delivery_data = response.json()
                else:
                    print(f"  API调用失败，状态码: {response.status_code}")
                    delivery_data = None
                
                if delivery_data:
                    print(f"  发现 {len(delivery_data)} 个历史交割记录:")
                    for i, record in enumerate(delivery_data):
                        delivery_time = record['deliveryTime']
                        delivery_price = record['deliveryPrice']
                        
                        # 转换时间戳为可读格式
                        dt = datetime.fromtimestamp(delivery_time/1000)
                        date_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                        
                        # 根据交割时间推断合约名称
                        year = dt.year
                        month = dt.month
                        day = dt.day
                        
                        # 币安季度合约通常在每季度最后一个周五交割
                        if month in [3, 6, 9, 12]:
                            quarter_name = f"{str(year)[2:]}{month:02d}{day:02d}"
                            estimated_symbol = f"{pair}_{quarter_name}"
                        else:
                            estimated_symbol = f"{pair}_未知"
                        
                        print(f"    {i+1}. 交割时间: {date_str}")
                        print(f"       交割价格: {delivery_price}")
                        print(f"       推测合约: {estimated_symbol}")
                        print()
                else:
                    print(f"  {pair} 没有历史交割记录")
                    
            except Exception as e:
                print(f"  查询 {pair} 失败: {str(e)}")
            
            print("-" * 50)
        
        # 尝试直接查询可能的历史合约名称
        print("\n=== 尝试查询可能的历史合约 ===")
        
        # 生成可能的历史合约名称
        possible_contracts = [
            'BTCUSDT_241227',  # 2024年12月
            'ETHUSDT_241227',
            'BTCUSDT_250328',  # 2025年3月
            'ETHUSDT_250328',
            'BTCUSDT_240927',  # 2024年9月
            'ETHUSDT_240927',
            'BTCUSDT_240628',  # 2024年6月
            'ETHUSDT_240628',
        ]
        
        # 获取当前所有交易对信息
        print("获取当前交易所信息...")
        info = client.exchange_info()
        all_symbols = [s['symbol'] for s in info['symbols']]
        
        print(f"当前交易所共有 {len(all_symbols)} 个交易对")
        
        # 检查历史合约是否还在交易所信息中
        historical_contracts_found = []
        for contract in possible_contracts:
            if contract in all_symbols:
                # 找到对应的详细信息
                for symbol_info in info['symbols']:
                    if symbol_info['symbol'] == contract:
                        historical_contracts_found.append({
                            'symbol': contract,
                            'status': symbol_info['status'],
                            'contractType': symbol_info['contractType'],
                            'deliveryDate': symbol_info.get('deliveryDate', 0)
                        })
                        break
        
        if historical_contracts_found:
            print(f"\n在交易所信息中发现 {len(historical_contracts_found)} 个历史合约:")
            for contract in historical_contracts_found:
                symbol = contract['symbol']
                status = contract['status']
                contract_type = contract['contractType']
                delivery_date = contract['deliveryDate']
                
                if delivery_date and delivery_date > 0:
                    dt = datetime.fromtimestamp(delivery_date/1000)
                    delivery_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    delivery_str = "未知"
                
                print(f"  {symbol} (状态: {status}, 类型: {contract_type}, 交割时间: {delivery_str})")
        else:
            print("\n在当前交易所信息中未发现历史合约")
        
        # 检查是否有其他状态的合约
        print(f"\n=== 按状态分类的所有USDT合约 ===")
        status_counts = {}
        expired_contracts = []
        
        for symbol_info in info['symbols']:
            if 'USDT' in symbol_info['symbol']:
                status = symbol_info['status']
                status_counts[status] = status_counts.get(status, 0) + 1
                
                # 如果不是TRADING状态，可能是历史合约
                if status != 'TRADING':
                    expired_contracts.append({
                        'symbol': symbol_info['symbol'],
                        'status': status,
                        'contractType': symbol_info['contractType'],
                        'deliveryDate': symbol_info.get('deliveryDate', 0)
                    })
        
        print("各状态合约数量:")
        for status, count in status_counts.items():
            print(f"  {status}: {count} 个")
        
        if expired_contracts:
            print(f"\n发现 {len(expired_contracts)} 个非TRADING状态的合约:")
            for contract in expired_contracts[:20]:  # 只显示前20个
                symbol = contract['symbol']
                status = contract['status']
                contract_type = contract['contractType']
                delivery_date = contract['deliveryDate']
                
                if delivery_date and delivery_date > 0:
                    dt = datetime.fromtimestamp(delivery_date/1000)
                    delivery_str = dt.strftime('%Y-%m-%d')
                else:
                    delivery_str = "未知"
                
                print(f"  {symbol} (状态: {status}, 类型: {contract_type}, 交割: {delivery_str})")
        
        print(f"\n=== 总结 ===")
        print("1. 币安提供历史交割价格API，可以查询过往季度合约的交割价格")
        print("2. 但历史合约本身可能不会保留在exchange_info中")
        print("3. 只有当前和未来的合约会在exchange_info中显示")
        print("4. 要获取历史合约数据，需要使用delivery_price API和历史K线API")
        
    except Exception as e:
        print(f"检查过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_historical_delivery_contracts()
