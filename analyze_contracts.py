#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析币安期货合约类型的脚本
"""

import os
import json
from binance.um_futures import UMFutures
from dotenv import load_dotenv
from collections import defaultdict

def analyze_contract_types():
    """分析币安期货API返回的合约类型"""
    
    # 加载环境变量
    load_dotenv('1.env')
    API_KEY = os.getenv('API_KEY')
    SECRET_KEY = os.getenv('SECRET_KEY')
    
    # 创建客户端
    client = UMFutures(key=API_KEY, secret=SECRET_KEY)
    
    try:
        # 获取交易所信息
        print("正在获取交易所信息...")
        info = client.exchange_info()
        
        # 统计合约类型
        contract_types = defaultdict(int)
        usdt_contracts = defaultdict(list)
        all_contracts = defaultdict(list)

        print(f"总共获取到 {len(info['symbols'])} 个交易对")
        print("\n=== 分析所有合约类型 ===")

        for symbol_info in info['symbols']:
            contract_type = symbol_info.get('contractType', 'UNKNOWN')
            contract_types[contract_type] += 1

            # 记录所有合约的详细信息
            contract_data = {
                'symbol': symbol_info['symbol'],
                'status': symbol_info['status'],
                'deliveryDate': symbol_info.get('deliveryDate', 0),
                'onboardDate': symbol_info.get('onboardDate', 0),
                'baseAsset': symbol_info.get('baseAsset', ''),
                'quoteAsset': symbol_info.get('quoteAsset', '')
            }
            all_contracts[contract_type].append(contract_data)

            # 如果是USDT合约，记录详细信息
            if symbol_info['symbol'].endswith('USDT'):
                usdt_contracts[contract_type].append(contract_data)
        
        # 打印合约类型统计
        print("所有合约类型统计:")
        for contract_type, count in contract_types.items():
            print(f"  {contract_type}: {count} 个")
        
        print(f"\n=== USDT合约详细分析 ===")
        for contract_type, symbols in usdt_contracts.items():
            print(f"\n{contract_type} 合约 ({len(symbols)} 个):")
            
            # 显示前10个作为示例
            for i, symbol_data in enumerate(symbols[:10]):
                symbol = symbol_data['symbol']
                status = symbol_data['status']
                delivery_date = symbol_data['deliveryDate']
                
                if delivery_date and delivery_date > 0:
                    from datetime import datetime
                    delivery_str = datetime.fromtimestamp(delivery_date/1000).strftime('%Y-%m-%d')
                    print(f"  {symbol} (状态: {status}, 交割日期: {delivery_str})")
                else:
                    print(f"  {symbol} (状态: {status})")
            
            if len(symbols) > 10:
                print(f"  ... 还有 {len(symbols) - 10} 个")
        
        # 分析所有合约类型
        print(f"\n=== 所有合约类型详细分析 ===")
        for contract_type, contracts in all_contracts.items():
            print(f"\n{contract_type} 合约 ({len(contracts)} 个):")

            # 显示前5个作为示例
            for i, contract in enumerate(contracts[:5]):
                symbol = contract['symbol']
                status = contract['status']
                base_asset = contract['baseAsset']
                quote_asset = contract['quoteAsset']
                delivery_date = contract['deliveryDate']

                if delivery_date and delivery_date > 0:
                    from datetime import datetime
                    delivery_str = datetime.fromtimestamp(delivery_date/1000).strftime('%Y-%m-%d')
                    print(f"  {symbol} ({base_asset}/{quote_asset}, 状态: {status}, 交割: {delivery_str})")
                else:
                    print(f"  {symbol} ({base_asset}/{quote_asset}, 状态: {status})")

            if len(contracts) > 5:
                print(f"  ... 还有 {len(contracts) - 5} 个")

        # 检查是否有交割合约
        delivery_contracts = []
        for contract_type, contracts in all_contracts.items():
            if contract_type not in ['PERPETUAL', '']:
                delivery_contracts.extend(contracts)

        print(f"\n=== 交割合约分析 ===")
        if delivery_contracts:
            print(f"发现 {len(delivery_contracts)} 个交割合约:")
            for contract in delivery_contracts:
                symbol = contract['symbol']
                status = contract['status']
                base_asset = contract['baseAsset']
                quote_asset = contract['quoteAsset']
                delivery_date = contract['deliveryDate']

                if delivery_date and delivery_date > 0:
                    from datetime import datetime
                    delivery_str = datetime.fromtimestamp(delivery_date/1000).strftime('%Y-%m-%d %H:%M:%S')
                    print(f"  {symbol} ({base_asset}/{quote_asset}, 状态: {status}, 交割时间: {delivery_str})")
                else:
                    print(f"  {symbol} ({base_asset}/{quote_asset}, 状态: {status}, 无交割时间)")
        else:
            print("未发现交割合约")
        
        # 保存详细数据到文件
        from datetime import datetime
        output_data = {
            'contract_type_summary': dict(contract_types),
            'all_contracts_by_type': {k: v for k, v in all_contracts.items()},
            'usdt_contracts_by_type': {k: v for k, v in usdt_contracts.items()},
            'analysis_time': str(datetime.now())
        }
        
        with open('contract_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n详细分析结果已保存到 contract_analysis.json")
        
    except Exception as e:
        print(f"分析过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_contract_types()
