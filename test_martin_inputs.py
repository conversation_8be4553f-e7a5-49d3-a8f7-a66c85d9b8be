#!/usr/bin/env python3
"""
测试马丁策略输入验证和新控件的简单脚本
"""

import tkinter as tk
from tkinter import ttk, StringVar, IntVar, Frame, Label, Entry, Button, Toplevel, LEFT, RIGHT, X, BOTH, W

class TestMartinInputs:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("测试马丁策略输入验证")
        self.root.geometry("400x300")
        
        # 创建测试按钮
        test_btn = Button(self.root, text="打开马丁策略参数设置", command=self.open_martin_test_dialog)
        test_btn.pack(pady=50)
        
    def open_martin_test_dialog(self):
        """弹出马丁策略测试参数设置窗口"""
        dialog = Toplevel(self.root)
        dialog.title("马丁策略测试参数设置")
        dialog.geometry("500x800")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 创建滚动框架
        main_frame = Frame(dialog)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # 数字输入验证函数
        def validate_number(value):
            """只允许输入数字和小数点，且最多只能有一个小数点"""
            if value == "":  # 允许空值
                return True

            # 检查是否只包含数字和小数点
            if not all(c.isdigit() or c == '.' for c in value):
                return False

            # 检查小数点数量不超过1个
            if value.count('.') > 1:
                return False

            return True

        # 注册验证函数
        vcmd = (dialog.register(validate_number), '%P')
        
        # 初始资金设置
        capital_frame = Frame(main_frame)
        capital_frame.pack(fill=X, pady=5)
        Label(capital_frame, text="初始资金:", font=("微软雅黑", 10, "bold")).pack(side=LEFT)
        initial_capital_var = StringVar(value="10000")
        Entry(capital_frame, textvariable=initial_capital_var, width=15, validate='key', validatecommand=vcmd).pack(side=LEFT, padx=10)
        
        # 分隔线
        ttk.Separator(main_frame, orient='horizontal').pack(fill=X, pady=10)
        
        # 马丁策略参数设置
        Label(main_frame, text="马丁策略参数设置:", font=("微软雅黑", 10, "bold")).pack(anchor=W, pady=(0, 5))
        
        # 存储各级别参数的变量
        martin_params = {}
        
        # 创建1%-5%的参数设置（简化测试）
        for percent in range(1, 6):
            param_frame = Frame(main_frame)
            param_frame.pack(fill=X, pady=2)
            
            # 跌幅标签
            Label(param_frame, text=f"{percent}%跌幅:", width=8, anchor=W).pack(side=LEFT)
            
            # 加仓金额
            Label(param_frame, text="加仓金额:").pack(side=LEFT, padx=(10, 5))
            add_amount_var = StringVar(value=str(1000 * percent))  # 默认值递增
            Entry(param_frame, textvariable=add_amount_var, width=10, validate='key', validatecommand=vcmd).pack(side=LEFT, padx=(0, 10))
            
            # 止盈涨幅
            Label(param_frame, text="止盈涨幅:").pack(side=LEFT, padx=(10, 5))
            profit_percent_var = StringVar(value="1.0")  # 默认1%止盈
            Entry(param_frame, textvariable=profit_percent_var, width=8, validate='key', validatecommand=vcmd).pack(side=LEFT, padx=(0, 5))
            Label(param_frame, text="%").pack(side=LEFT)
            
            # 保存变量引用
            martin_params[percent] = {
                'add_amount': add_amount_var,
                'profit_percent': profit_percent_var
            }
        
        # 分隔线
        ttk.Separator(main_frame, orient='horizontal').pack(fill=X, pady=10)
        
        # 策略选项设置
        options_frame = Frame(main_frame)
        options_frame.pack(fill=X, pady=5)
        
        # 第一行选项
        options_row1 = Frame(options_frame)
        options_row1.pack(fill=X, pady=2)
        
        # 爆仓停止选项
        stop_on_liquidation_var = IntVar(value=1)  # 默认选中
        ttk.Checkbutton(options_row1, text="爆仓时停止测试", variable=stop_on_liquidation_var).pack(side=LEFT)
        
        # 开启复利选项
        compound_interest_var = IntVar(value=0)  # 默认不选中
        ttk.Checkbutton(options_row1, text="开启复利", variable=compound_interest_var).pack(side=LEFT, padx=(20, 0))
        
        # 第二行选项
        options_row2 = Frame(options_frame)
        options_row2.pack(fill=X, pady=2)
        
        # 无视最小名义价值选项
        ignore_min_notional_var = IntVar(value=0)  # 默认不选中
        ttk.Checkbutton(options_row2, text="无视最小名义价值", variable=ignore_min_notional_var).pack(side=LEFT)
        
        # 按钮框架
        button_frame = Frame(main_frame)
        button_frame.pack(fill=X, pady=10)
        
        def on_confirm():
            try:
                initial_capital = float(initial_capital_var.get())
                
                # 收集所有马丁参数
                martin_config = {}
                for percent, vars_dict in martin_params.items():
                    add_amount = float(vars_dict['add_amount'].get())
                    profit_percent = float(vars_dict['profit_percent'].get())
                    martin_config[percent] = {
                        'add_amount': add_amount,
                        'profit_percent': profit_percent
                    }
                
                stop_on_liquidation = bool(stop_on_liquidation_var.get())
                compound_interest = bool(compound_interest_var.get())
                ignore_min_notional = bool(ignore_min_notional_var.get())
                
                print(f"初始资金: {initial_capital}")
                print(f"爆仓停止: {stop_on_liquidation}")
                print(f"开启复利: {compound_interest}")
                print(f"无视最小名义价值: {ignore_min_notional}")
                print("马丁参数:")
                for percent, config in martin_config.items():
                    print(f"  {percent}%跌幅: 加仓{config['add_amount']}, 止盈{config['profit_percent']}%")
                
                dialog.destroy()
            except Exception as e:
                print(f"输入无效: {e}")
        
        def on_cancel():
            dialog.destroy()
        
        Button(button_frame, text="确定", command=on_confirm).pack(side=LEFT, padx=5)
        Button(button_frame, text="取消", command=on_cancel).pack(side=LEFT, padx=5)
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = TestMartinInputs()
    app.run()
