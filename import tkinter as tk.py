import tkinter as tk
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.pyplot as plt
import matplotlib as mpl
import ctypes
import numpy as np
import random

# 设置Windows缩放系统API，启用dpi感知
try:
    ctypes.windll.shcore.SetProcessDpiAwareness(1)
except:
    pass

class DPITestApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("DPI感知测试程序")
        self.root.geometry("800x600")

        # 1. 为Tkinter启用DPI感知（适用于Windows）
        # if hasattr(tk, 'set_dpi_awareness'):
        #     tk.set_dpi_awareness(1)  # 1=系统感知，2=Per-Monitor V2
        # self.root.tk.call('tk', 'scaling', self.root.tk.call('tk', 'scaling'))  # 同步缩放因子

        # 2. 配置Matplotlib的DPI，匹配系统缩放
        self.scale_factor = self.root.tk.call('tk', 'scaling')  # 获取缩放因子（1.25 for 125%）
        print(self.scale_factor)
        # mpl.rcParams['figure.dpi'] = 100 * self.scale_factor  # 调整DPI
        # mpl.rcParams['figure.max_open_warning'] = 0  # 可选：关闭警告

        self.setup_ui()
        self.create_chart()

    def setup_ui(self):
        # 顶部尺寸信息显示区域
        info_frame = tk.Frame(self.root, height=60, bg='lightyellow', relief=tk.SUNKEN, bd=1)
        info_frame.pack(fill=tk.X, padx=5, pady=2)
        info_frame.pack_propagate(False)

        # 创建信息标签
        self.info_label = tk.Label(info_frame, text="尺寸信息加载中...",
                                  bg='lightyellow', font=('Consolas', 9),
                                  justify=tk.LEFT, anchor='w')
        self.info_label.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 顶部按钮区域
        button_frame = tk.Frame(self.root, height=50, bg='lightgray')
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        button_frame.pack_propagate(False)

        # 刷新图表按钮
        refresh_btn = tk.Button(button_frame, text="刷新图表", command=self.create_chart,
                               bg='lightblue', font=('Arial', 10))
        refresh_btn.pack(side=tk.LEFT, padx=5, pady=10)

        # 两个没用的按钮
        dummy_btn1 = tk.Button(button_frame, text="按钮1", command=lambda: print("按钮1被点击"),
                              bg='lightgreen', font=('Arial', 10))
        dummy_btn1.pack(side=tk.LEFT, padx=5, pady=10)

        dummy_btn2 = tk.Button(button_frame, text="按钮2", command=lambda: print("按钮2被点击"),
                              bg='lightyellow', font=('Arial', 10))
        dummy_btn2.pack(side=tk.LEFT, padx=5, pady=10)

        # 主内容区域
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 左侧图表区域
        self.chart_frame = tk.Frame(main_frame, width=1000, bg='white', relief=tk.SUNKEN, bd=2)
        self.chart_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=(0, 5))

        # 右侧功能区域
        function_frame = tk.Frame(main_frame, width=200, bg='lightgray', relief=tk.RAISED, bd=2)
        function_frame.pack(side=tk.RIGHT, fill=tk.Y)
        function_frame.pack_propagate(False)

        # 功能区域标题
        title_label = tk.Label(function_frame, text="功能区域", font=('Arial', 12, 'bold'),
                              bg='lightgray')
        title_label.pack(pady=10)

        # 一些没用的控件
        tk.Label(function_frame, text="参数设置:", bg='lightgray', font=('Arial', 10)).pack(pady=5)

        # 滑块
        self.scale_var = tk.DoubleVar(value=50)
        scale = tk.Scale(function_frame, from_=0, to=100, orient=tk.HORIZONTAL,
                        variable=self.scale_var, bg='lightgray')
        scale.pack(pady=5, padx=10, fill=tk.X)

        # 复选框
        self.check_var = tk.BooleanVar()
        check = tk.Checkbutton(function_frame, text="启用选项", variable=self.check_var,
                              bg='lightgray')
        check.pack(pady=5)

        # 单选按钮
        self.radio_var = tk.StringVar(value="选项1")
        radio1 = tk.Radiobutton(function_frame, text="选项1", variable=self.radio_var,
                               value="选项1", bg='lightgray')
        radio1.pack(pady=2)

        radio2 = tk.Radiobutton(function_frame, text="选项2", variable=self.radio_var,
                               value="选项2", bg='lightgray')
        radio2.pack(pady=2)

        # 文本框
        tk.Label(function_frame, text="输入框:", bg='lightgray', font=('Arial', 10)).pack(pady=(10, 5))
        self.entry = tk.Entry(function_frame, width=20)
        self.entry.pack(pady=5, padx=10)

        # 列表框
        tk.Label(function_frame, text="列表:", bg='lightgray', font=('Arial', 10)).pack(pady=(10, 5))
        listbox = tk.Listbox(function_frame, height=4)
        listbox.pack(pady=5, padx=10, fill=tk.X)
        for i in range(1, 6):
            listbox.insert(tk.END, f"项目 {i}")

        # 按钮
        action_btn = tk.Button(function_frame, text="执行操作",
                              command=lambda: print(f"执行操作: {self.entry.get()}"),
                              bg='lightcoral')
        action_btn.pack(pady=10)

    def create_chart(self):
        if hasattr(self, 'canvas'):
            self.canvas.get_tk_widget().destroy()

        # 创建Matplotlib图表
        self.fig = plt.Figure(figsize=(6, 1), dpi=100)
        self.ax = self.fig.add_subplot(111)
        self.generate_chart_data()

        # 将图表嵌入Tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.chart_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        self.canvas.draw()

        # 延迟更新尺寸信息，确保界面完全渲染
        self.root.after(100, self.update_size_info)

    def generate_chart_data(self):
        # 生成随机数据
        x = np.linspace(0, 10, 50)
        y1 = np.sin(x) + np.random.normal(0, 0.1, 50)
        y2 = np.cos(x) + np.random.normal(0, 0.1, 50)

        self.ax.clear()
        self.ax.plot(x, y1, label='Sin波形', color='blue', linewidth=2)
        self.ax.plot(x, y2, label='Cos波形', color='red', linewidth=2)
        self.ax.set_title(f"DPI感知图表 (缩放因子: {self.scale_factor:.2f})", fontsize=14)
        self.ax.set_xlabel("X轴", fontsize=12)
        self.ax.set_ylabel("Y轴", fontsize=12)
        self.ax.legend()
        self.ax.grid(True, alpha=0.3)

        # 添加一些随机散点
        scatter_x = np.random.uniform(0, 10, 20)
        scatter_y = np.random.uniform(-2, 2, 20)
        self.ax.scatter(scatter_x, scatter_y, alpha=0.6, s=30, color='green')

    def refresh_chart(self):
        print("刷新图表...")
        self.generate_chart_data()
        self.canvas.draw()
        self.update_size_info()
        print("图表刷新完成")

    def update_size_info(self):
        """更新尺寸信息显示"""
        try:
            # 获取窗口大小
            window_width = self.root.winfo_width()
            window_height = self.root.winfo_height()

            # 获取图表区域大小
            chart_width = self.chart_frame.winfo_width()
            chart_height = self.chart_frame.winfo_height()

            # 获取功能区大小
            function_frame = None
            for child in self.root.winfo_children():
                if isinstance(child, tk.Frame):
                    for subchild in child.winfo_children():
                        if isinstance(subchild, tk.Frame) and subchild.cget('bg') == 'lightgray' and subchild.cget('width') == 200:
                            function_frame = subchild
                            break
                    if function_frame:
                        break

            function_width = function_frame.winfo_width() if function_frame else 0
            function_height = function_frame.winfo_height() if function_frame else 0

            # 获取matplotlib图表实际大小
            canvas_widget = self.canvas.get_tk_widget()
            canvas_width = canvas_widget.winfo_width()
            canvas_height = canvas_widget.winfo_height()

            # 获取matplotlib figure的DPI和figsize
            fig_dpi = self.fig.get_dpi()
            fig_width, fig_height = self.fig.get_size_inches()

            # 构建信息文本
            info_text = (
                f"窗口: {window_width}×{window_height} | "
                f"图表区: {chart_width}×{chart_height} | "
                f"Canvas: {canvas_width}×{canvas_height} | "
                f"功能区: {function_width}×{function_height} | "
                f"Fig: {fig_width:.1f}×{fig_height:.1f}英寸@{fig_dpi}DPI | "
                f"缩放: {self.scale_factor:.2f}"
            )

            self.info_label.config(text=info_text)

        except Exception as e:
            self.info_label.config(text=f"尺寸信息获取失败: {str(e)}")

        # 定期更新尺寸信息
        self.root.after(1000, self.update_size_info)

    def run(self):
        self.root.mainloop()

# 创建并运行应用
if __name__ == "__main__":
    app = DPITestApp()
    app.run()