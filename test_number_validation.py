#!/usr/bin/env python3
"""
测试数字输入验证功能的简单脚本
"""

import tkinter as tk
from tkinter import StringVar, Frame, Label, Entry, Button

class TestNumberValidation:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("测试数字输入验证")
        self.root.geometry("400x300")
        
        # 数字输入验证函数
        def validate_number(value):
            """只允许输入数字和小数点，且最多只能有一个小数点"""
            if value == "":  # 允许空值
                return True
            
            # 检查是否只包含数字和小数点
            if not all(c.isdigit() or c == '.' for c in value):
                return False
            
            # 检查小数点数量不超过1个
            if value.count('.') > 1:
                return False
            
            return True
        
        # 注册验证函数
        vcmd = (self.root.register(validate_number), '%P')
        
        # 创建测试输入框
        main_frame = Frame(self.root)
        main_frame.pack(pady=20, padx=20, fill='both', expand=True)
        
        # 测试说明
        Label(main_frame, text="数字输入验证测试", font=("微软雅黑", 14, "bold")).pack(pady=10)
        Label(main_frame, text="以下输入框只能输入数字和小数点（最多一个小数点）", 
              font=("微软雅黑", 10)).pack(pady=5)
        Label(main_frame, text="尝试输入字母、符号等，应该无法输入", 
              font=("微软雅黑", 10)).pack(pady=5)
        
        # 初始资金测试
        capital_frame = Frame(main_frame)
        capital_frame.pack(fill='x', pady=10)
        Label(capital_frame, text="初始资金:", width=10, anchor='w').pack(side='left')
        self.capital_var = StringVar(value="10000")
        Entry(capital_frame, textvariable=self.capital_var, width=15, 
              validate='key', validatecommand=vcmd).pack(side='left', padx=10)
        
        # 加仓金额测试
        amount_frame = Frame(main_frame)
        amount_frame.pack(fill='x', pady=10)
        Label(amount_frame, text="加仓金额:", width=10, anchor='w').pack(side='left')
        self.amount_var = StringVar(value="1000")
        Entry(amount_frame, textvariable=self.amount_var, width=15, 
              validate='key', validatecommand=vcmd).pack(side='left', padx=10)
        
        # 止盈涨幅测试
        profit_frame = Frame(main_frame)
        profit_frame.pack(fill='x', pady=10)
        Label(profit_frame, text="止盈涨幅:", width=10, anchor='w').pack(side='left')
        self.profit_var = StringVar(value="1.5")
        Entry(profit_frame, textvariable=self.profit_var, width=15, 
              validate='key', validatecommand=vcmd).pack(side='left', padx=10)
        Label(profit_frame, text="%").pack(side='left')
        
        # 显示当前值按钮
        def show_values():
            print(f"初始资金: '{self.capital_var.get()}'")
            print(f"加仓金额: '{self.amount_var.get()}'")
            print(f"止盈涨幅: '{self.profit_var.get()}'")
            print("---")
        
        Button(main_frame, text="显示当前值", command=show_values).pack(pady=20)
        
        # 测试提示
        test_frame = Frame(main_frame)
        test_frame.pack(fill='x', pady=10)
        Label(test_frame, text="测试项目:", font=("微软雅黑", 10, "bold")).pack(anchor='w')
        Label(test_frame, text="✓ 数字 (0-9) - 应该可以输入").pack(anchor='w')
        Label(test_frame, text="✓ 小数点 (.) - 应该可以输入一个").pack(anchor='w')
        Label(test_frame, text="✗ 字母 (a-z, A-Z) - 应该无法输入").pack(anchor='w')
        Label(test_frame, text="✗ 符号 (-, +, *, /, etc.) - 应该无法输入").pack(anchor='w')
        Label(test_frame, text="✗ 多个小数点 (..) - 应该无法输入").pack(anchor='w')
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = TestNumberValidation()
    app.run()
