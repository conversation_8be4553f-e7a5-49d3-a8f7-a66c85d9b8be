import sys
import time
import os
import traceback
import logging
from datetime import datetime
from binance.um_futures import UMFutures
from dotenv import load_dotenv

# ========== 参数配置 ==========
SYMBOL = 'BTCUSDT'               # 交易对
INIT_QUANTITY = 0.002            # 初始下单数量
INIT_PRICE = 60000               # 初始下单价格（限价买入）
MARTINGALE_MULTIPLIER = 1.2        # 马丁加倍因子
MAX_ATTEMPTS = 5                 # 最多尝试挂单次数
ORDER_WAIT_TIME = 600000             # 单个订单挂单等待成交时间（秒）
PROFIT_PERCENT = 0.05            # 平仓目标（成交价格上浮 5%）
LOOP_DELAY = 5                   # 每次循环延时（秒）

# ========== 日志设置 ==========
logger = logging.getLogger()
logger.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

# 文件处理器
file_handler = logging.FileHandler('martingale_log.txt', mode='a')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

# 控制台处理器，实时输出到终端
stream_handler = logging.StreamHandler(sys.stdout)
stream_handler.setFormatter(formatter)
logger.addHandler(stream_handler)

# ========== 辅助函数 ==========
def sync_time(um_futures_client):
    """同步服务器时间，设置客户端时间偏移，避免 -1021 错误"""
    try:
        server_time = int(um_futures_client.time()["serverTime"])
        local_time = int(time.time() * 1000)
        offset = server_time - local_time
        um_futures_client.timestamp_offset = offset
        logging.info(f"服务器时间：{server_time}，本地时间：{local_time}，时间偏移：{offset}ms")
    except Exception as e:
        logging.error(f"同步时间出错：{e}")

def place_order(um_futures_client, symbol, side, quantity, price, extra_params=None):
    """
    下限价订单（开仓单）
    extra_params 可用于传入 positionSide 等额外参数
    """
    params = {
        'symbol': symbol,
        'side': side,
        'type': 'LIMIT',
        'timeInForce': 'GTC',
        'quantity': quantity,
        'price': price
    }
    if extra_params:
        params.update(extra_params)
    try:
        result = um_futures_client.new_order(**params)
        logging.info(f"下单成功：{params}，返回：{result}")
        return result.get('orderId')
    except Exception as e:
        logging.error("下单错误：" + str(e))
        return None

def monitor_order(um_futures_client, symbol, order_id, wait_time=ORDER_WAIT_TIME):
    """
    持续轮询该订单，确认是否成交
    如果订单状态为 FILLED 则返回订单信息，否则返回 None
    """
    start_time = time.time()
    while time.time() - start_time < wait_time:
        try:
            order_info = um_futures_client.query_order(symbol=symbol, orderId=order_id)
            status = order_info.get('status', '')
            logging.info(f"订单 {order_id} 状态：{status}")
            if status == 'FILLED':
                return order_info
        except Exception as e:
            logging.error("查询订单状态出错：" + str(e))
        time.sleep(1)
    logging.info(f"订单 {order_id} 在 {wait_time} 秒内未成交")
    return None

def cancel_order(um_futures_client, symbol, order_id):
    """尝试取消未成交订单"""
    try:
        result = um_futures_client.cancel_order(symbol=symbol, orderId=order_id)
        logging.info(f"取消订单 {order_id} 成功：{result}")
    except Exception as e:
        logging.error("取消订单出错：" + str(e))

def place_close_order(um_futures_client, symbol, side, quantity, close_price):
    """
    下平仓单（反向开仓实现平仓）
    注：平仓单的下单类型和参数可按实际需求改为 TAKE_PROFIT 或 STOP_MARKET 等
    """
    params = {
        'symbol': symbol,
        'side': side,
        'type': 'LIMIT',
        'timeInForce': 'GTC',
        'quantity': quantity,
        'price': close_price
    }
    try:
        result = um_futures_client.new_order(**params)
        logging.info(f"平仓单下单成功：{params}，返回：{result}")
        return result
    except Exception as e:
        logging.error("平仓单下单出错：" + str(e))
        return None

# ========== 核心策略 ==========
def martingale_strategy(um_futures_client):
    """
    马丁格尔策略的核心逻辑：
      1. 尝试下单并等待成交；
      2. 若未成交则取消订单，加倍数量，并可能调整下单价格后再试；
      3. 若订单成交，则下平仓单（或根据成交情况调整价格）
    """
    attempt = 1
    quantity = INIT_QUANTITY
    price = INIT_PRICE
    order_filled = None

    while attempt <= MAX_ATTEMPTS and order_filled is None:
        logging.info(f"第 {attempt} 次挂单：数量={quantity}, 价格={price}")
        # 开多：side 为 BUY，且账户为双向持仓时需指定 positionSide
        order_id = place_order(um_futures_client, SYMBOL, side='BUY', quantity=quantity, price=price, extra_params={'positionSide': 'LONG'})
        if order_id is None:
            logging.error("下单失败，尝试下一次")
            attempt += 1
            continue

        order_info = monitor_order(um_futures_client, SYMBOL, order_id)
        if order_info:
            logging.info(f"订单成交：{order_info}")
            order_filled = order_info
            break
        else:
            cancel_order(um_futures_client, SYMBOL, order_id)
            # 马丁格尔：加倍挂单数量，并适当调整价格（例如降 0.5% 来提高成交概率）
            quantity *= MARTINGALE_MULTIPLIER
            price = round(price * 0.995, 2)
            logging.info(f"订单未成交，更新参数：新数量={quantity}, 新价格={price}")
            attempt += 1

    if order_filled:
        # 取成交均价（若返回中无avgPrice则默认用下单价格）
        filled_price = float(order_filled.get('avgPrice', price))
        # 设定平仓价：例如以成交价上浮 PROFIT_PERCENT
        target_price = round(filled_price * (1 + PROFIT_PERCENT), 2)
        logging.info(f"成交价为 {filled_price}，目标平仓价：{target_price}")
        # 平仓：此处简单用反向下单（如果原订单为 BUY 则平仓时 SELL）
        close_result = place_close_order(um_futures_client, SYMBOL, side='SELL', quantity=order_filled.get('origQty', quantity), close_price=target_price)
        return order_filled, close_result
    else:
        logging.info("多次尝试后订单均未成交")
        return None, None

# ========== 持续循环主程序 ==========
def main():
    load_dotenv('1.env')
    API_KEY = os.getenv('API_KEY')
    SECRET_KEY = os.getenv('SECRET_KEY')
    um_futures_client = UMFutures(key=API_KEY, secret=SECRET_KEY)
    
    # 同步时间
    sync_time(um_futures_client)

    logging.info("开始持续监控与下单执行程序。")

    while True:
        try:
            logging.info("启动一个马丁策略循环")
            order_filled, close_result = martingale_strategy(um_futures_client)
            if order_filled:
                logging.info("订单成交并平仓成功，等待一段时间后继续下一轮策略")
                time.sleep(30)
            else:
                logging.info("本轮无成交，下轮重试")
                time.sleep(LOOP_DELAY)
        except Exception as e:
            logging.error("主循环发生错误：" + traceback.format_exc())
            time.sleep(5)
            continue

if __name__ == '__main__':
    main()
