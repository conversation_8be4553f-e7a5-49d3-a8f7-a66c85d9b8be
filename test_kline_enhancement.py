#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试K线管理界面的增强功能
验证安全拉升时间和最大涨幅的计算逻辑
"""

import pandas as pd
from datetime import datetime, timedelta

def calculate_time_difference(start_time, end_time, interval):
    """计算两个时间点之间的时间差，返回对应K线周期的数量"""
    try:
        time_diff = end_time - start_time
        total_minutes = time_diff.total_seconds() / 60
        
        # 根据K线周期计算对应的周期数量
        if interval.endswith('m'):  # 分钟K线
            minutes_per_kline = int(interval[:-1])
            return int(total_minutes / minutes_per_kline)
        elif interval.endswith('h'):  # 小时K线
            hours_per_kline = int(interval[:-1])
            return int(total_minutes / (hours_per_kline * 60))
        elif interval == '1d':  # 日线
            return int(total_minutes / (24 * 60))
        elif interval == '1w':  # 周线
            return int(total_minutes / (7 * 24 * 60))
        else:  # 默认15分钟
            return int(total_minutes / 15)
    except Exception as e:
        return 0

def test_time_calculation():
    """测试时间差计算功能"""
    print("=== 测试时间差计算功能 ===")
    
    # 测试数据
    test_cases = [
        {
            'start': '2019-09-13 05:21:00',
            'end': '2019-09-13 06:16:00',
            'interval': '15m',
            'expected': 'about 3-4 periods'
        },
        {
            'start': '2019-09-11 14:52:00',
            'end': '2019-09-13 06:16:00',
            'interval': '15m',
            'expected': 'about 148 periods'
        }
    ]
    
    for i, case in enumerate(test_cases):
        start_time = pd.to_datetime(case['start'])
        end_time = pd.to_datetime(case['end'])
        interval = case['interval']
        
        result = calculate_time_difference(start_time, end_time, interval)
        time_diff = end_time - start_time
        
        print(f"测试案例 {i+1}:")
        print(f"  开始时间: {case['start']}")
        print(f"  结束时间: {case['end']}")
        print(f"  时间间隔: {time_diff}")
        print(f"  K线周期: {interval}")
        print(f"  计算结果: {result} 个周期")
        print(f"  预期结果: {case['expected']}")
        print()

def simulate_enhanced_logic():
    """模拟增强后的逻辑"""
    print("=== 模拟增强后的逻辑 ===")
    
    # 模拟数据：根据您提供的示例
    mock_data = [
        {
            'group': 1,
            'start_time': '2019-09-09 19:24:00',
            'min_price_time': '2019-09-11 14:52:00',
            'end_time': '2019-09-13 05:21:00',
            'min_price': 100.0,  # 假设价格
            'next_start_time': '2019-09-13 06:16:00',
            'next_start_price': 105.2  # 假设价格
        },
        {
            'group': 2,
            'start_time': '2019-09-13 06:16:00',
            'min_price_time': '2019-09-19 11:08:00',
            'end_time': '2019-09-20 00:31:00',
            'min_price': 98.0,  # 假设价格
            'next_start_time': '2019-09-20 04:02:00',
            'next_start_price': 102.5  # 假设价格
        }
    ]
    
    interval = '15m'
    
    for i, data in enumerate(mock_data):
        print(f"处理第 {data['group']} 组数据:")
        print(f"  基本信息: {data['start_time']} ~ {data['end_time']}")
        
        if i < len(mock_data) - 1:  # 不是最后一组
            # 计算安全拉升时间
            end_time = pd.to_datetime(data['end_time'])
            next_start_time = pd.to_datetime(data['next_start_time'])
            safe_rise_duration = calculate_time_difference(end_time, next_start_time, interval)
            
            # 计算最大涨幅
            min_price = data['min_price']
            next_start_price = data['next_start_price']
            max_rise = (next_start_price - min_price) / min_price
            
            print(f"  安全拉升时间: {safe_rise_duration} 个{interval}周期")
            print(f"  最大涨幅: {max_rise*100:.2f}%")
            print(f"  (从最低价 {min_price} 到下一组开始价 {next_start_price})")
        else:
            print(f"  最后一组，无法计算安全拉升时间和最大涨幅")
        print()

if __name__ == "__main__":
    test_time_calculation()
    simulate_enhanced_logic()
    print("测试完成！")
