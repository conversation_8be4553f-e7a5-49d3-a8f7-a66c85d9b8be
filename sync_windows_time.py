import os
import subprocess
import ctypes
import sys

def is_admin():
    """检查脚本是否以管理员权限运行"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def sync_windows_time(time_server="ptbtime1.ptb.de"):
    """
    同步Windows时间到指定的时间服务器
    
    参数:
    time_server: 时间服务器地址，默认为德国物理技术研究所的原子钟服务器
    """
    print(f"正在将Windows时间同步到服务器: {time_server}")
    
    try:
        # 停止Windows时间服务
        subprocess.run(["net", "stop", "w32time"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        print("已停止Windows时间服务")
        
        # 重新启动时间服务
        subprocess.run(["net", "start", "w32time"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        print("已重新启动Windows时间服务")
        
        # 设置Windows时间服务类型为NTP
        subprocess.run(["w32tm", "/config", "/syncfromflags:manual", f"/manualpeerlist:{time_server}", "/update"], 
                      check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        print(f"已设置时间服务器为: {time_server}")
        
        # 重新同步时间
        subprocess.run(["w32tm", "/resync", "/force"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        print("时间同步成功完成！")
        
        return True
    
    except subprocess.CalledProcessError as e:
        print(f"时间同步失败: {e}")
        print(f"错误输出: {e.stderr.decode('gbk') if e.stderr else '无'}")
        return False

def main():
    # 默认服务器
    time_server = "ptbtime1.ptb.de"
    
    # 如果有命令行参数，使用指定的服务器
    if len(sys.argv) > 1:
        time_server = sys.argv[1]
    
    # 检查是否以管理员权限运行
    if not is_admin():
        print("此脚本需要管理员权限才能同步时间。")
        print("请右键点击脚本，选择'以管理员身份运行'。")
        input("按Enter键退出...")
        sys.exit(1)
    
    # 同步时间
    success = sync_windows_time(time_server)
    
    if success:
        print("\n时间同步完成。您的Windows系统时间已与时间服务器同步。")
    else:
        print("\n时间同步失败。请检查您是否有足够的权限或网络连接是否正常。")
    
    input("按Enter键退出...")

if __name__ == "__main__":
    main() 