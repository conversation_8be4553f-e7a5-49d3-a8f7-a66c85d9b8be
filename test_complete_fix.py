#!/usr/bin/env python3
"""
完整的窗口切换问题修复测试
验证独立窗口解决方案和主程序操控性
"""

import tkinter as tk
from tkinter import ttk, StringVar, IntVar, Frame, Label, Entry, Button, Toplevel, LEFT, RIGHT, X, BOTH, W
import threading
import time

class TestCompleteFix:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("主程序 - 完整修复测试")
        self.root.geometry("800x600")
        
        # 用于跟踪独立窗口
        self._martin_dialog = None
        self._test_dialog = None
        self._config_dialog = None
        self._margin_dialog = None
        self._leverage_dialog = None
        
        # 模拟后台任务
        self.counter = 0
        self.is_running = True
        
        self.create_ui()
        self.start_background_task()
        
    def create_ui(self):
        main_frame = Frame(self.root, bg='#f5f5f5')
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        Label(main_frame, text="🔧 完整窗口修复测试", 
              font=("微软雅黑", 18, "bold"), bg='#f5f5f5', fg='#2c3e50').pack(pady=(0, 10))
        
        # 后台任务状态显示
        self.status_frame = Frame(main_frame, bg='#ecf0f1', relief='raised', bd=1)
        self.status_frame.pack(fill=X, pady=(0, 20))
        
        Label(self.status_frame, text="📊 后台任务状态（模拟K线更新）", 
              font=("微软雅黑", 12, "bold"), bg='#ecf0f1').pack(pady=(10, 5))
        
        self.counter_label = Label(self.status_frame, text="计数器: 0", 
                                  font=("微软雅黑", 14), bg='#ecf0f1', fg='#27ae60')
        self.counter_label.pack(pady=(0, 10))
        
        # 测试说明
        info_frame = Frame(main_frame, bg='white', relief='raised', bd=1)
        info_frame.pack(fill=X, pady=(0, 20))
        
        Label(info_frame, text="✅ 修复效果验证：", 
              font=("微软雅黑", 12, "bold"), bg='white', fg='#e74c3c').pack(anchor=W, padx=15, pady=(10, 5))
        
        effects = [
            "• 每个对话框都是独立的任务栏窗口",
            "• 主程序在对话框打开时不可操控（防止多开）",
            "• 后台任务（如K线更新）继续运行",
            "• 可以单独点击任务栏图标切换窗口",
            "• 解决'显示桌面'后无法恢复的问题"
        ]
        
        for effect in effects:
            Label(info_frame, text=effect, font=("微软雅黑", 10), 
                  bg='white', fg='#2c3e50').pack(anchor=W, padx=30, pady=1)
        
        Label(info_frame, text="", bg='white').pack(pady=5)
        
        # 测试按钮
        button_frame = Frame(main_frame, bg='#f5f5f5')
        button_frame.pack(fill=X, pady=20)
        
        # 主要功能按钮
        main_buttons = [
            ("马丁策略参数设置", self.open_martin_window, "#27ae60"),
            ("测试最大涨跌幅", self.open_test_window, "#3498db"),
            ("交易配置", self.open_config_window, "#f39c12")
        ]
        
        for text, command, color in main_buttons:
            btn = Button(button_frame, text=text, command=command,
                        font=("微软雅黑", 11, "bold"), bg=color, fg="white",
                        width=20, height=2, relief='flat')
            btn.pack(pady=5)
        
        # 辅助功能按钮
        aux_frame = Frame(main_frame, bg='#f5f5f5')
        aux_frame.pack(fill=X, pady=(10, 0))
        
        aux_buttons = [
            ("切换保证金模式", self.open_margin_window, "#9b59b6"),
            ("调整杠杆倍数", self.open_leverage_window, "#e67e22")
        ]
        
        for text, command, color in aux_buttons:
            btn = Button(aux_frame, text=text, command=command,
                        font=("微软雅黑", 10), bg=color, fg="white",
                        width=18, height=1, relief='flat')
            btn.pack(pady=3)
        
    def start_background_task(self):
        """启动后台任务，模拟K线更新等功能"""
        def background_worker():
            while self.is_running:
                try:
                    self.counter += 1
                    # 更新UI（线程安全）
                    self.root.after(0, lambda: self.counter_label.config(
                        text=f"计数器: {self.counter} (后台任务正常运行)"))
                    time.sleep(1)
                except:
                    break
        
        threading.Thread(target=background_worker, daemon=True).start()
        
    def center_window_on_screen(self, window):
        """将窗口居中显示在屏幕中央"""
        window.update_idletasks()
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        window_width = window.winfo_reqwidth()
        window_height = window.winfo_reqheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
    def open_martin_window(self):
        """打开马丁策略独立窗口"""
        if self._martin_dialog and self._martin_dialog.winfo_exists():
            self._martin_dialog.lift()
            self._martin_dialog.focus_set()
            return
            
        window = Toplevel()
        window.title("马丁策略参数设置")
        window.geometry("500x600")
        
        try:
            window.iconbitmap(default='')
        except:
            pass
        window.attributes('-topmost', False)
        window.resizable(True, True)
        
        self._martin_dialog = window
        
        def on_close():
            self._martin_dialog = None
            # 恢复主程序操控
            self.root.grab_set()
            self.root.after(100, lambda: self.root.grab_release())
            window.destroy()
        window.protocol("WM_DELETE_WINDOW", on_close)
        
        self.center_window_on_screen(window)
        
        # 使主程序不可操控
        self.root.grab_release()
        window.grab_set()
        
        # 创建内容
        Label(window, text="🎯 马丁策略参数设置", 
              font=("微软雅黑", 16, "bold"), fg="#27ae60").pack(pady=20)
        Label(window, text="独立任务栏窗口 - 主程序不可操控", 
              font=("微软雅黑", 12)).pack(pady=10)
        
        for i in range(6):
            frame = Frame(window)
            frame.pack(fill=X, padx=20, pady=5)
            Label(frame, text=f"参数 {i+1}:", width=10, anchor=W).pack(side=LEFT)
            Entry(frame, width=20).pack(side=LEFT, padx=10)
        
        Button(window, text="确定", command=on_close, 
               bg="#27ae60", fg="white", width=15).pack(pady=20)
        
    def open_test_window(self):
        """打开测试参数独立窗口"""
        if self._test_dialog and self._test_dialog.winfo_exists():
            self._test_dialog.lift()
            self._test_dialog.focus_set()
            return
            
        window = Toplevel()
        window.title("测试最大涨跌幅参数设置")
        window.geometry("350x200")
        
        try:
            window.iconbitmap(default='')
        except:
            pass
        window.attributes('-topmost', False)
        window.resizable(True, True)
        
        self._test_dialog = window
        
        def on_close():
            self._test_dialog = None
            self.root.grab_set()
            self.root.after(100, lambda: self.root.grab_release())
            window.destroy()
        window.protocol("WM_DELETE_WINDOW", on_close)
        
        self.center_window_on_screen(window)
        
        # 使主程序不可操控
        self.root.grab_release()
        window.grab_set()
        
        Label(window, text="📈 测试参数设置", 
              font=("微软雅黑", 14, "bold"), fg="#3498db").pack(pady=20)
        Label(window, text="请输入反弹/回调百分比:").pack(pady=10)
        
        entry_frame = Frame(window)
        entry_frame.pack(pady=10)
        percent_var = StringVar(value="5")
        Entry(entry_frame, textvariable=percent_var, width=10).pack(side=LEFT, padx=5)
        Label(entry_frame, text="%").pack(side=LEFT)
        
        Button(window, text="开始测试", command=on_close, 
               bg="#3498db", fg="white", width=15).pack(pady=20)
        
    def open_config_window(self):
        """打开配置独立窗口"""
        if self._config_dialog and self._config_dialog.winfo_exists():
            self._config_dialog.lift()
            self._config_dialog.focus_set()
            return
            
        window = Toplevel()
        window.title("交易配置")
        window.geometry("400x300")
        
        try:
            window.iconbitmap(default='')
        except:
            pass
        window.attributes('-topmost', False)
        window.resizable(True, True)
        
        self._config_dialog = window
        
        def on_close():
            self._config_dialog = None
            self.root.grab_set()
            self.root.after(100, lambda: self.root.grab_release())
            window.destroy()
        window.protocol("WM_DELETE_WINDOW", on_close)
        
        self.center_window_on_screen(window)
        
        # 使主程序不可操控
        self.root.grab_release()
        window.grab_set()
        
        Label(window, text="⚙️ 交易配置", 
              font=("微软雅黑", 14, "bold"), fg="#f39c12").pack(pady=20)
        Label(window, text="独立任务栏窗口", font=("微软雅黑", 12)).pack(pady=10)
        
        Button(window, text="保存配置", command=on_close, 
               bg="#f39c12", fg="white", width=15).pack(pady=20)
        
    def open_margin_window(self):
        """打开保证金模式独立窗口"""
        if self._margin_dialog and self._margin_dialog.winfo_exists():
            self._margin_dialog.lift()
            self._margin_dialog.focus_set()
            return
            
        window = Toplevel()
        window.title("切换保证金模式")
        window.geometry("300x150")
        
        try:
            window.iconbitmap(default='')
        except:
            pass
        window.attributes('-topmost', False)
        window.resizable(True, True)
        
        self._margin_dialog = window
        
        def on_close():
            self._margin_dialog = None
            self.root.grab_set()
            self.root.after(100, lambda: self.root.grab_release())
            window.destroy()
        window.protocol("WM_DELETE_WINDOW", on_close)
        
        self.center_window_on_screen(window)
        
        # 使主程序不可操控
        self.root.grab_release()
        window.grab_set()
        
        Label(window, text="💰 保证金模式", 
              font=("微软雅黑", 14, "bold"), fg="#9b59b6").pack(pady=20)
        
        mode_var = StringVar(value="全仓")
        ttk.Radiobutton(window, text="全仓", value="全仓", variable=mode_var).pack(pady=5)
        ttk.Radiobutton(window, text="逐仓", value="逐仓", variable=mode_var).pack(pady=5)
        
        Button(window, text="确定", command=on_close, 
               bg="#9b59b6", fg="white", width=10).pack(pady=15)
        
    def open_leverage_window(self):
        """打开杠杆调整独立窗口"""
        if self._leverage_dialog and self._leverage_dialog.winfo_exists():
            self._leverage_dialog.lift()
            self._leverage_dialog.focus_set()
            return
            
        window = Toplevel()
        window.title("调整杠杆倍数")
        window.geometry("300x200")
        
        try:
            window.iconbitmap(default='')
        except:
            pass
        window.attributes('-topmost', False)
        window.resizable(True, True)
        
        self._leverage_dialog = window
        
        def on_close():
            self._leverage_dialog = None
            self.root.grab_set()
            self.root.after(100, lambda: self.root.grab_release())
            window.destroy()
        window.protocol("WM_DELETE_WINDOW", on_close)
        
        self.center_window_on_screen(window)
        
        # 使主程序不可操控
        self.root.grab_release()
        window.grab_set()
        
        Label(window, text="⚡ 杠杆倍数", 
              font=("微软雅黑", 14, "bold"), fg="#e67e22").pack(pady=20)
        
        leverage_var = IntVar(value=10)
        scale = ttk.Scale(window, from_=1, to=125, variable=leverage_var, orient='horizontal')
        scale.pack(fill=X, padx=20, pady=10)
        
        leverage_label = Label(window, text="当前杠杆: 10x")
        leverage_label.pack(pady=5)
        
        def update_label(*args):
            leverage_label.config(text=f"当前杠杆: {leverage_var.get()}x")
        leverage_var.trace('w', update_label)
        
        Button(window, text="确定", command=on_close,
               bg="#e67e22", fg="white", width=10).pack(pady=15)

    def start_background_task(self):
        """启动后台任务，模拟K线更新等功能"""
        def background_worker():
            while self.is_running:
                try:
                    self.counter += 1
                    # 更新UI（线程安全）
                    self.root.after(0, lambda: self.counter_label.config(
                        text=f"计数器: {self.counter} (后台任务正常运行)"))
                    time.sleep(1)
                except:
                    break

        threading.Thread(target=background_worker, daemon=True).start()

    def run(self):
        try:
            self.root.mainloop()
        finally:
            self.is_running = False
            
    def __del__(self):
        self.is_running = False

if __name__ == "__main__":
    print("启动完整修复测试程序...")
    print("观察：")
    print("1. 后台计数器持续运行（模拟K线更新）")
    print("2. 打开对话框后主程序不可点击（防止多开）")
    print("3. 每个对话框都有独立的任务栏图标")
    print("4. 可以通过任务栏单独切换窗口")
    app = TestCompleteFix()
    app.run()
