#!/usr/bin/env python3
"""
测试美化后的对话框UI
展示保证金模式和杠杆倍数对话框的新UI设计
"""

import tkinter as tk
from tkinter import ttk, StringVar, IntVar, Frame, Label, Entry, Button, Toplevel, LEFT, RIGHT, X, BOTH, W, messagebox
import threading
import time

class MockClient:
    """模拟客户端"""
    def change_margin_type(self, **kwargs):
        time.sleep(0.5)
        return True
        
    def change_leverage(self, **kwargs):
        time.sleep(0.5)
        return True

class TestBeautifulDialogs:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("美化对话框UI测试")
        self.root.geometry("700x500")
        self.root.configure(bg='#f5f5f5')
        
        # 模拟属性
        self.client = MockClient()
        self.current_margin_type = "CROSSED"
        self.current_leverage = 10
        self.current_valid_symbol = "BTCUSDT"
        
        # 窗口引用
        self._margin_dialog = None
        self._leverage_dialog = None
        
        self.create_ui()
        
    def create_ui(self):
        main_frame = Frame(self.root, bg='#f5f5f5')
        main_frame.pack(fill=BOTH, expand=True, padx=30, pady=30)
        
        # 标题
        title_frame = Frame(main_frame, bg='#f5f5f5')
        title_frame.pack(fill=X, pady=(0, 30))
        
        Label(title_frame, text="🎨 美化对话框UI展示", 
              font=("微软雅黑", 20, "bold"), bg='#f5f5f5', fg='#2c3e50').pack()
        Label(title_frame, text="全新设计的保证金模式和杠杆倍数对话框", 
              font=("微软雅黑", 12), bg='#f5f5f5', fg='#7f8c8d').pack(pady=(5, 0))
        
        # 当前状态显示
        status_frame = Frame(main_frame, bg='white', relief='raised', bd=2)
        status_frame.pack(fill=X, pady=(0, 30))
        
        Label(status_frame, text="📊 当前交易状态", 
              font=("微软雅黑", 14, "bold"), bg='white', fg='#34495e').pack(pady=(15, 10))
        
        status_info = Frame(status_frame, bg='white')
        status_info.pack(fill=X, padx=20, pady=(0, 15))
        
        # 状态信息网格
        info_items = [
            ("交易对:", self.current_valid_symbol, "#3498db"),
            ("保证金模式:", "全仓" if self.current_margin_type == "CROSSED" else "逐仓", "#9b59b6"),
            ("当前杠杆:", f"{self.current_leverage}x", "#e67e22")
        ]
        
        for i, (label, value, color) in enumerate(info_items):
            item_frame = Frame(status_info, bg='white')
            item_frame.pack(fill=X, pady=2)
            
            Label(item_frame, text=label, font=("微软雅黑", 11), 
                  bg='white', fg='#2c3e50', width=12, anchor=W).pack(side=LEFT)
            Label(item_frame, text=value, font=("微软雅黑", 11, "bold"), 
                  bg='white', fg=color).pack(side=LEFT, padx=(10, 0))
        
        # 功能说明
        feature_frame = Frame(main_frame, bg='#ecf0f1', relief='raised', bd=1)
        feature_frame.pack(fill=X, pady=(0, 30))
        
        Label(feature_frame, text="✨ UI改进特点", 
              font=("微软雅黑", 12, "bold"), bg='#ecf0f1', fg='#e74c3c').pack(anchor=W, padx=15, pady=(15, 10))
        
        features = [
            "🎯 现代化的视觉设计，使用渐变色和图标",
            "📱 清晰的信息层次，状态显示更直观",
            "🎨 动态颜色变化，杠杆风险等级可视化",
            "💡 友好的用户提示和说明文字",
            "🔧 统一的按钮样式和交互体验"
        ]
        
        for feature in features:
            Label(feature_frame, text=feature, font=("微软雅黑", 10), 
                  bg='#ecf0f1', fg='#2c3e50').pack(anchor=W, padx=30, pady=2)
        
        Label(feature_frame, text="", bg='#ecf0f1').pack(pady=10)
        
        # 测试按钮区域
        button_frame = Frame(main_frame, bg='#f5f5f5')
        button_frame.pack(fill=X, pady=20)
        
        # 主要测试按钮
        main_buttons = [
            ("💰 切换保证金模式", self.change_margin_mode, "#9b59b6", "测试美化后的保证金模式对话框"),
            ("⚡ 调整杠杆倍数", self.change_leverage, "#e67e22", "测试美化后的杠杆倍数对话框")
        ]
        
        for text, command, color, desc in main_buttons:
            btn_container = Frame(button_frame, bg='#f5f5f5')
            btn_container.pack(fill=X, pady=8)
            
            btn = Button(btn_container, text=text, command=command,
                        font=("微软雅黑", 12, "bold"), bg=color, fg="white",
                        width=25, height=2, relief='flat', cursor='hand2')
            btn.pack()
            
            Label(btn_container, text=desc, font=("微软雅黑", 9), 
                  bg='#f5f5f5', fg='#7f8c8d').pack(pady=(3, 0))
            
            # 添加悬停效果
            def on_enter(e, btn=btn, color=color):
                darker_colors = {
                    "#9b59b6": "#8e44ad",
                    "#e67e22": "#d35400"
                }
                btn.config(bg=darker_colors.get(color, color))
            
            def on_leave(e, btn=btn, color=color):
                btn.config(bg=color)
                
            btn.bind("<Enter>", on_enter)
            btn.bind("<Leave>", on_leave)
        
    def center_dialog_on_screen(self, dialog):
        """将对话框居中显示在屏幕中央"""
        dialog.update_idletasks()
        screen_width = dialog.winfo_screenwidth()
        screen_height = dialog.winfo_screenheight()
        
        geometry = dialog.geometry()
        if 'x' in geometry and '+' in geometry:
            size_part = geometry.split('+')[0]
            if 'x' in size_part:
                dialog_width, dialog_height = map(int, size_part.split('x'))
            else:
                dialog_width = dialog.winfo_reqwidth()
                dialog_height = dialog.winfo_reqheight()
        else:
            dialog_width = dialog.winfo_reqwidth()
            dialog_height = dialog.winfo_reqheight()
        
        x = (screen_width - dialog_width) // 2
        y = (screen_height - dialog_height) // 2
        dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
        
    def change_margin_mode(self):
        """美化后的保证金模式对话框"""
        symbol = self.current_valid_symbol
        
        if self._margin_dialog and self._margin_dialog.winfo_exists():
            self._margin_dialog.lift()
            self._margin_dialog.focus_set()
            return
            
        dialog = Toplevel()
        dialog.title("切换保证金模式")
        dialog.geometry("400x280")
        
        try:
            dialog.iconbitmap(default='')
        except:
            pass
        dialog.attributes('-topmost', False)
        dialog.resizable(True, True)
        
        self._margin_dialog = dialog
        
        def on_dialog_close():
            self._margin_dialog = None
            self.root.grab_set()
            self.root.after(100, lambda: self.root.grab_release())
            dialog.destroy()
            
        dialog.protocol("WM_DELETE_WINDOW", on_dialog_close)
        self.center_dialog_on_screen(dialog)
        
        self.root.grab_release()
        dialog.grab_set()
        
        # 创建美化的UI
        main_frame = Frame(dialog, bg='#f8f9fa')
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        Label(main_frame, text="💰 保证金模式设置", 
              font=("微软雅黑", 16, "bold"), bg='#f8f9fa', fg="#9b59b6").pack(pady=(0, 20))
        
        # 当前状态
        current_mode_text = "全仓" if self.current_margin_type == "CROSSED" else "逐仓"
        Label(main_frame, text=f"当前模式: {current_mode_text}", 
              font=("微软雅黑", 12), bg='#f8f9fa', fg="#7f8c8d").pack(pady=(0, 15))
        
        # 选择区域
        selection_frame = Frame(main_frame, bg='white', relief='raised', bd=1)
        selection_frame.pack(fill=X, pady=(0, 20))
        
        Label(selection_frame, text="选择新的保证金模式：", 
              font=("微软雅黑", 12, "bold"), bg='white').pack(anchor=W, padx=15, pady=(15, 10))
        
        mode_var = StringVar(value=self.current_margin_type)
        
        radio_frame = Frame(selection_frame, bg='white')
        radio_frame.pack(fill=X, padx=15, pady=(0, 15))
        
        # 全仓选项
        crossed_frame = Frame(radio_frame, bg='white')
        crossed_frame.pack(fill=X, pady=5)
        crossed_radio = ttk.Radiobutton(crossed_frame, text="全仓模式", value="CROSSED", variable=mode_var)
        crossed_radio.pack(side=LEFT)
        Label(crossed_frame, text="- 所有仓位共享保证金", 
              font=("微软雅黑", 9), bg='white', fg='#7f8c8d').pack(side=LEFT, padx=(10, 0))
        
        # 逐仓选项
        isolated_frame = Frame(radio_frame, bg='white')
        isolated_frame.pack(fill=X, pady=5)
        isolated_radio = ttk.Radiobutton(isolated_frame, text="逐仓模式", value="ISOLATED", variable=mode_var)
        isolated_radio.pack(side=LEFT)
        Label(isolated_frame, text="- 每个仓位独立保证金", 
              font=("微软雅黑", 9), bg='white', fg='#7f8c8d').pack(side=LEFT, padx=(10, 0))
        
        def apply_change():
            new_mode = mode_var.get()
            if new_mode != self.current_margin_type:
                print(f"切换保证金模式: {self.current_margin_type} -> {new_mode}")
                self.current_margin_type = new_mode
                messagebox.showinfo("成功", f"已切换到{'全仓' if new_mode == 'CROSSED' else '逐仓'}模式")
            on_dialog_close()
        
        # 按钮区域
        button_frame = Frame(main_frame, bg='#f8f9fa')
        button_frame.pack(pady=20)
        
        Button(button_frame, text="确定", command=apply_change, 
               bg="#9b59b6", fg="white", width=12, height=1,
               font=("微软雅黑", 10, "bold"), relief='flat').pack(side=LEFT, padx=5)
        Button(button_frame, text="取消", command=on_dialog_close, 
               bg="#95a5a6", fg="white", width=12, height=1,
               font=("微软雅黑", 10, "bold"), relief='flat').pack(side=LEFT, padx=5)
        
    def change_leverage(self):
        """美化后的杠杆倍数对话框"""
        if self._leverage_dialog and self._leverage_dialog.winfo_exists():
            self._leverage_dialog.lift()
            self._leverage_dialog.focus_set()
            return
            
        dialog = Toplevel()
        dialog.title("调整杠杆倍数")
        dialog.geometry("450x350")
        
        try:
            dialog.iconbitmap(default='')
        except:
            pass
        dialog.attributes('-topmost', False)
        dialog.resizable(True, True)
        
        self._leverage_dialog = dialog
        
        def on_dialog_close():
            self._leverage_dialog = None
            self.root.grab_set()
            self.root.after(100, lambda: self.root.grab_release())
            dialog.destroy()
            
        dialog.protocol("WM_DELETE_WINDOW", on_dialog_close)
        self.center_dialog_on_screen(dialog)
        
        self.root.grab_release()
        dialog.grab_set()
        
        # 创建美化的UI
        main_frame = Frame(dialog, bg='#f8f9fa')
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        Label(main_frame, text="⚡ 杠杆倍数调整", 
              font=("微软雅黑", 16, "bold"), bg='#f8f9fa', fg="#e67e22").pack(pady=(0, 20))
        
        # 当前状态显示
        status_frame = Frame(main_frame, bg='#ecf0f1', relief='raised', bd=1)
        status_frame.pack(fill=X, pady=(0, 20))
        
        Label(status_frame, text="当前状态", 
              font=("微软雅黑", 12, "bold"), bg='#ecf0f1').pack(pady=(10, 5))
        Label(status_frame, text=f"当前杠杆: {self.current_leverage}x", 
              font=("微软雅黑", 11), bg='#ecf0f1', fg="#2c3e50").pack(pady=2)
        Label(status_frame, text="最大杠杆: 125x", 
              font=("微软雅黑", 11), bg='#ecf0f1', fg="#2c3e50").pack(pady=(2, 10))
        
        # 调整区域
        adjustment_frame = Frame(main_frame, bg='white', relief='raised', bd=1)
        adjustment_frame.pack(fill=X, pady=(0, 20))
        
        Label(adjustment_frame, text="调整杠杆倍数：", 
              font=("微软雅黑", 12, "bold"), bg='white').pack(anchor=W, padx=15, pady=(15, 10))
        
        # 滑块区域
        scale_frame = Frame(adjustment_frame, bg='white')
        scale_frame.pack(fill=X, padx=15, pady=(0, 15))
        
        leverage_var = IntVar(value=self.current_leverage)
        scale = ttk.Scale(scale_frame, from_=1, to=125, 
                        variable=leverage_var, orient='horizontal')
        scale.set(self.current_leverage)
        scale.pack(fill=X, pady=10)
        
        # 显示选择的杠杆值
        leverage_label = Label(scale_frame, text=f"选择杠杆: {self.current_leverage}x", 
                             font=("微软雅黑", 12, "bold"), bg='white', fg="#e67e22")
        leverage_label.pack(pady=5)
        
        # 风险提示标签
        risk_label = Label(scale_frame, text="风险等级: 低", 
                          font=("微软雅黑", 10), bg='white', fg="#27ae60")
        risk_label.pack(pady=2)
        
        def update_label(*args):
            current_val = int(leverage_var.get())
            leverage_label.config(text=f"选择杠杆: {current_val}x")
            
            # 根据杠杆大小改变颜色和风险提示
            if current_val <= 10:
                color = "#27ae60"  # 绿色 - 低风险
                risk_text = "风险等级: 低"
                risk_color = "#27ae60"
            elif current_val <= 50:
                color = "#f39c12"  # 橙色 - 中风险
                risk_text = "风险等级: 中"
                risk_color = "#f39c12"
            else:
                color = "#e74c3c"  # 红色 - 高风险
                risk_text = "风险等级: 高"
                risk_color = "#e74c3c"
                
            leverage_label.config(fg=color)
            risk_label.config(text=risk_text, fg=risk_color)
        
        leverage_var.trace('w', update_label)
        
        def apply_leverage():
            new_leverage = int(leverage_var.get())
            if new_leverage != self.current_leverage:
                print(f"调整杠杆倍数: {self.current_leverage}x -> {new_leverage}x")
                self.current_leverage = new_leverage
                messagebox.showinfo("成功", f"杠杆已调整为{new_leverage}x")
            on_dialog_close()
        
        # 按钮区域
        button_frame = Frame(main_frame, bg='#f8f9fa')
        button_frame.pack(pady=20)
        
        confirm_btn = Button(button_frame, text="确定", command=apply_leverage, 
                           bg="#e67e22", fg="white", width=12, height=1,
                           font=("微软雅黑", 10, "bold"), relief='flat', cursor='hand2')
        confirm_btn.pack(side=LEFT, padx=5)
        
        cancel_btn = Button(button_frame, text="取消", command=on_dialog_close, 
                          bg="#95a5a6", fg="white", width=12, height=1,
                          font=("微软雅黑", 10, "bold"), relief='flat', cursor='hand2')
        cancel_btn.pack(side=LEFT, padx=5)
        
        # 按钮悬停效果
        def on_btn_enter(e, btn, color):
            darker = {"#e67e22": "#d35400", "#95a5a6": "#7f8c8d"}
            btn.config(bg=darker.get(color, color))
        def on_btn_leave(e, btn, color):
            btn.config(bg=color)
            
        confirm_btn.bind("<Enter>", lambda e: on_btn_enter(e, confirm_btn, "#e67e22"))
        confirm_btn.bind("<Leave>", lambda e: on_btn_leave(e, confirm_btn, "#e67e22"))
        cancel_btn.bind("<Enter>", lambda e: on_btn_enter(e, cancel_btn, "#95a5a6"))
        cancel_btn.bind("<Leave>", lambda e: on_btn_leave(e, cancel_btn, "#95a5a6"))
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    print("启动美化对话框UI测试...")
    print("展示全新设计的保证金模式和杠杆倍数对话框")
    app = TestBeautifulDialogs()
    app.run()
