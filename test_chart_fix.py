#!/usr/bin/env python3
"""
测试图表修复的脚本
验证K线图表刷新时不会出现1.25倍扩张的bug
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_chart_fix():
    """测试图表修复"""
    try:
        # 导入必要的模块
        from ma18 import KlinePage
        from binance.client import Client
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("K线图表修复测试")
        root.geometry("1200x800")
        
        # 创建一个简单的客户端（用于测试）
        class MockClient:
            def klines(self, **kwargs):
                # 返回空数据，避免API调用
                return []
        
        client = MockClient()
        
        # 创建K线页面
        def switch_page_callback(page_name):
            print(f"切换到页面: {page_name}")
        
        kline_page = KlinePage(root, switch_page_callback, client, default_symbol="BTCUSDT")
        kline_page.pack(fill=tk.BOTH, expand=True)
        
        # 添加测试说明
        info_frame = tk.Frame(root, bg='lightyellow', height=100)
        info_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)
        info_frame.pack_propagate(False)

        info_label = tk.Label(info_frame,
                             text="测试说明：多次点击'刷新图表'按钮，观察图表是否会越来越大。\n"
                                  "如果修复成功，图表大小应该保持不变。\n"
                                  "修复原理：create_chart只调用一次创建图表，refresh_chart调用load_kline_data进行重绘。\n"
                                  "同时测试K线管理界面的修复效果。",
                             bg='lightyellow', font=('Arial', 9),
                             justify=tk.LEFT)
        info_label.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加测试按钮
        test_frame = tk.Frame(root, bg='lightgray', height=50)
        test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=5, pady=5)
        test_frame.pack_propagate(False)
        
        def test_multiple_refresh():
            """测试多次刷新"""
            for i in range(5):
                print(f"第 {i+1} 次刷新...")
                kline_page.refresh_chart()
                root.update()
            messagebox.showinfo("测试完成", "已完成5次连续刷新测试")
        
        tk.Button(test_frame, text="连续刷新5次", 
                 command=test_multiple_refresh,
                 bg='lightcoral', font=('Arial', 10)).pack(side=tk.LEFT, padx=10, pady=10)
        
        tk.Button(test_frame, text="单次刷新",
                 command=kline_page.refresh_chart,
                 bg='lightblue', font=('Arial', 10)).pack(side=tk.LEFT, padx=10, pady=10)

        def test_manager_interface():
            """测试K线管理界面"""
            try:
                from ma18 import KlineDataManagerPage
                manager_window = tk.Toplevel(root)
                manager_window.title("K线管理界面测试")
                manager_window.geometry("1000x600")

                def dummy_switch(page):
                    print(f"切换到页面: {page}")

                manager_page = KlineDataManagerPage(manager_window, dummy_switch, client)
                manager_page.pack(fill=tk.BOTH, expand=True)

                # 添加测试按钮
                test_manager_frame = tk.Frame(manager_window, bg='lightcyan', height=40)
                test_manager_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=5, pady=5)
                test_manager_frame.pack_propagate(False)

                tk.Button(test_manager_frame, text="测试管理界面刷新",
                         command=manager_page.generate_chart,
                         bg='lightgreen', font=('Arial', 9)).pack(side=tk.LEFT, padx=5, pady=5)

                tk.Label(test_manager_frame, text="测试K线管理界面的图表刷新功能",
                        bg='lightcyan', font=('Arial', 9)).pack(side=tk.LEFT, padx=10, pady=5)

            except Exception as e:
                print(f"测试K线管理界面失败: {str(e)}")
                import traceback
                traceback.print_exc()

        tk.Button(test_frame, text="测试管理界面",
                 command=test_manager_interface,
                 bg='lightgreen', font=('Arial', 10)).pack(side=tk.LEFT, padx=10, pady=10)

        # 运行测试
        print("开始K线图表修复测试...")
        root.mainloop()
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_chart_fix()
