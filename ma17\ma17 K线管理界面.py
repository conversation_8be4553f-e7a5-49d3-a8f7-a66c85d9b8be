import sys
import time
import os
import json
import threading
import traceback
import logging
import csv
from datetime import datetime, timedelta
from decimal import Decimal, ROUND_HALF_EVEN
from tkinter import Tk, Frame, Button, Text, END, Scrollbar, VERTICAL, RIGHT, Y, LEFT, BOTH, TOP, Label, Entry, StringVar, ttk, messagebox, X, Toplevel, IntVar, Listbox, BOTTOM, SUNKEN, W, E, CENTER, DISABLED, NORMAL
from binance.um_futures import UMFutures
from dotenv import load_dotenv
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import mplfinance as mpf
import pandas as pd
import matplotlib.font_manager as fm
import numpy as np
import math
from matplotlib.ticker import FuncFormatter
import matplotlib.dates as mdates
from matplotlib.ticker import MaxNLocator
import ntplib
import subprocess
import ctypes
import matplotlib.patches as patches
from matplotlib.font_manager import FontProperties
import calendar

# 添加全局时间管理类
class TimeManager:
    def __init__(self):
        self.time_labels = []
        self.countdown_labels = {}  # 存储倒计时标签和对应的K线间隔及收盘时间
        self.running = False
        self.time_update_thread = None
        
    def add_time_label(self, label):
        """添加需要更新时间的标签"""
        if label not in self.time_labels:
            self.time_labels.append(label)
            # 立即更新一次
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            label.after(0, lambda: label.config(text=f" {current_time}"))
    
    def remove_time_label(self, label):
        """移除标签"""
        try:
            if label in self.time_labels:
                self.time_labels.remove(label)
        except Exception as e:
            logging.error(f"移除时间标签失败: {str(e)}")
    
    def add_countdown_label(self, label, interval):
        """添加K线倒计时标签
        label: 显示倒计时的标签
        interval: K线间隔，例如 '1m', '5m', '1h', '4h', '1d' 等
        """
        # 清空现有的所有倒计时标签
        self.countdown_labels = {}
        # 添加新标签
        self.countdown_labels[label] = {
            'interval': interval,
        }
        # 立即更新一次
        self.update_countdown_label(label)
    
    def remove_countdown_label(self):
        """移除倒计时标签"""
        try:
            # 直接清空所有标签
            self.countdown_labels = {}
        except Exception as e:
            logging.error(f"移除倒计时标签失败: {str(e)}")
    
    def update_countdown_label(self, label):
        """更新倒计时标签"""
        try:
            if label not in self.countdown_labels:
                return
                
            # 检查标签是否有效（matplotlib Text对象没有winfo_exists方法）
            try:
                # 尝试获取文本，如果失败说明对象无效
                current_text = label.get_text()
            except:
                # 如果标签无效，移除它
                self.remove_countdown_label()
                return
                
            label_info = self.countdown_labels[label]
            interval = label_info['interval']
            current_time = datetime.now()
            next_kline_time = self._calculate_next_kline_time(current_time, interval)
            
            # 计算剩余时间
            time_diff = next_kline_time - current_time
            total_seconds = int(time_diff.total_seconds())
            # print(current_time,total_seconds)
            
            # 确保不显示负数
            if total_seconds < 0:
                total_seconds = 0
                
            # 格式化显示
            if total_seconds <= 3600:  # 1小时以内
                countdown_text = f"{total_seconds // 60:02d}:{total_seconds % 60:02d}"
            elif total_seconds <= 86400:  # 24小时以内
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                seconds = total_seconds % 60
                countdown_text = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
            else:  # 24小时以上
                days = total_seconds // 86400
                hours = (total_seconds % 86400) // 3600
                countdown_text = f"{days}D:{hours:02d}h"
            
            # 如果文本包含换行符，说明是组合标签
            if '\n' in current_text:
                # 分割文本，保留第一行（价格），更新第二行（倒计时）
                price_text = current_text.split('\n')[0]
                new_text = f"{price_text}\n{countdown_text}"
                label.set_text(new_text)
            else:
                # 单独的倒计时标签
                label.set_text(countdown_text)
            
        except Exception as e:
            logging.error(f"更新倒计时失败: {str(e)}")
            
            # 尝试保持原有价格文本
            try:
                current_text = label.get_text()
                if '\n' in current_text:
                    price_text = current_text.split('\n')[0]
                    label.set_text(f"{price_text}\n--:--")
                else:
                    label.set_text("--:--")
            except:
                # 如果无法获取原有文本，直接设置为错误提示
                label.set_text("--:--")
    
    def _calculate_next_kline_time(self, base_time, interval):
        """计算下一个K线的开始时间"""
        if interval.endswith('m'):
            minutes = int(interval[:-1])
            current_minute = base_time.minute
            current_second = base_time.second
            elapsed_seconds = current_minute * 60 + current_second
            elapsed_intervals = elapsed_seconds // (minutes * 60)
            next_kline_time = base_time.replace(
                minute=((elapsed_intervals + 1) * minutes) % 60,
                second=0,
                microsecond=0
            )
            if next_kline_time <= base_time:
                next_kline_time = next_kline_time + timedelta(hours=1)
                
        elif interval.endswith('h'):
            hours = int(interval[:-1])
            current_hour = base_time.hour
            elapsed_intervals = current_hour // hours
            next_kline_time = base_time.replace(
                hour=((elapsed_intervals + 1) * hours) % 24,
                minute=0,
                second=0,
                microsecond=0
            )
            if next_kline_time <= base_time:
                next_kline_time = next_kline_time + timedelta(days=1)
                
        elif interval == '1d':
            next_kline_time = (base_time + timedelta(days=1)).replace(
                hour=0,
                minute=0,
                second=0,
                microsecond=0
            )
        elif interval == '1w':
            # 下一个周K线的开始时间（每周一08:00）
            weekday = base_time.weekday()
            base = base_time.replace(hour=8, minute=0, second=0, microsecond=0) - timedelta(days=weekday)
            if base_time < base:
                next_kline_time = base
            else:
                weeks = ((base_time - base).days // 7) + 1
                next_kline_time = base + timedelta(weeks=weeks)
        else:
            # 默认情况，直接加一分钟
            next_kline_time = base_time + timedelta(minutes=1)
            
        return next_kline_time
    
    def start(self):
        """启动时间更新"""
        if not self.running:
            self.running = True
            self.time_update_thread = threading.Thread(target=self.time_update_loop, daemon=True)
            self.time_update_thread.start()
            
    def stop(self):
        """停止时间更新线程"""
        self.running = False
        if self.time_update_thread:
            self.time_update_thread.join(timeout=1)
            self.time_update_thread = None
    
    def time_update_loop(self):
        """时间更新循环"""
        while self.running:
            try:
                # 更新系统时间
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                # 更新所有时间标签（tkinter组件）
                for label in self.time_labels[:]:  # 使用切片创建副本进行迭代
                    try:
                        if hasattr(label, 'winfo_exists') and label.winfo_exists():
                            # 捕获当前循环变量值
                            current_label = label
                            label.after(0, lambda l=current_label: l.config(text=f" {current_time}"))
                        else:
                            self.time_labels.remove(label)
                    except Exception as e:
                        logging.error(f"更新时间标签失败: {str(e)}")
                        self.time_labels.remove(label)
                
                # 更新所有倒计时标签（可能是matplotlib Text对象）
                # for label in list(self.countdown_labels.keys()):
                #     try:
                #         # 对于matplotlib Text对象，直接尝试调用update_countdown_label
                #         # 异常处理已经在方法内部
                #         current_label = label
                #         if hasattr(label, 'after'):  # tkinter对象
                #             label.after(0, lambda l=current_label: self.update_countdown_label(l))
                #         else:  # matplotlib Text对象或其他
                #             self.update_countdown_label(current_label)
                #     except Exception as e:
                #         logging.error(f"更新倒计时标签循环中失败: {str(e)}")
                #         self.remove_countdown_label()
                        
            except Exception as e:
                logging.error(f"时间更新失败: {str(e)}")
            
            # 每秒更新一次
            time.sleep(1)

def start_time_update(widget):
    """添加时间标签到更新列表"""
    time_manager.add_time_label(widget)

# 检查管理员权限并尝试提升权限
def is_admin():
    """检查程序是否以管理员权限运行"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

# 如果不是以管理员权限运行，则尝试重新以管理员权限启动
# if not is_admin():
#     try:
#         # 使用 shell32.dll 的 ShellExecuteW 函数以管理员权限重新启动程序
#         ctypes.windll.shell32.ShellExecuteW(
#             None, "runas", sys.executable, " ".join(sys.argv), None, 1
#         )
#         # 退出当前程序
#         sys.exit(0)
#     except Exception as e:
#         print(f"获取管理员权限失败: {str(e)}")
#         # 继续执行，但可能在需要管理员权限的操作时会失败

# ===================== 参数配置 ======================
SYMBOL = 'BTCUSDT'               # 交易对
INIT_QUANTITY = 0.002            # 初始下单数量（保留，下单功能暂未集成）
INIT_PRICE = 60000               # 初始下单价格
# 以下参数主要用于 API 数据更新循环
LOOP_DELAY = 5                 # 每次循环延时（秒）

# ===================== 文件目录与初始化 ======================
ROOT_DIR = os.getcwd()
CONFIG_FILE = os.path.join(ROOT_DIR, 'config.json')
CURRENT_ORDERS_FILE = os.path.join(ROOT_DIR, 'current_orders.json')
POSITIONS_FILE = os.path.join(ROOT_DIR, 'positions.json')
HISTORY_ORDERS_FILE = os.path.join(ROOT_DIR, 'history_orders.json')
LOG_FILE = os.path.join(ROOT_DIR, 'martingale_log.txt')
KLINE_DATA_DIR = os.path.join(ROOT_DIR, 'kline_data')  # K线数据存储目录

# 确保K线数据目录存在
if not os.path.exists(KLINE_DATA_DIR):
    os.makedirs(KLINE_DATA_DIR)

# SymbolConfig 类 - 用于管理交易对配置信息
class SymbolConfig:
    def __init__(self):
        self.config_dir = "symbol_configs"
        self.config_file = os.path.join(ROOT_DIR, self.config_dir, "symbol_configs.json")
        self.ensure_config_dir()
        self.configs = self.load_configs()
    
    def ensure_config_dir(self):
        """确保配置目录存在"""
        config_path = os.path.join(ROOT_DIR, self.config_dir)
        if not os.path.exists(config_path):
            os.makedirs(config_path)
            logging.info(f"创建交易对配置目录: {config_path}")
    
    def load_configs(self):
        """加载所有交易对配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                logging.error(f"加载交易对配置文件失败: {str(e)}")
                return {}
        else:
            return {}
    
    def save_configs(self):
        """保存所有交易对配置"""
        try:
            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(self.configs, f, indent=2, ensure_ascii=False)
            logging.info("交易对配置文件已保存")
        except Exception as e:
            logging.error(f"保存交易对配置文件失败: {str(e)}")
    
    def get_symbol_config(self, symbol):
        """获取特定交易对的配置"""
        if symbol in self.configs:
            return self.configs[symbol]
        else:
            # 返回默认配置
            return {
                "price_precision": 2,  # 价格精度（小数位数）
                "quantity_precision": 3,  # 数量精度
                "min_quantity": 0.001,  # 最小下单数量
                "leverage": 1,  # 当前杠杆
                "max_leverage": 20,  # 最大杠杆
                "margin_type": "CROSSED",  # 全仓模式
                "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
    
    def update_symbol_config(self, symbol, config_data):
        """更新特定交易对的配置"""
        if symbol not in self.configs:
            self.configs[symbol] = {}
        
        # 更新配置
        for key, value in config_data.items():
            self.configs[symbol][key] = value
        
        # 记录更新时间
        self.configs[symbol]["last_updated"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 保存到文件
        self.save_configs()
        return self.configs[symbol]
    
    def update_symbol_precision(self, symbol, exchange_info=None, client=None):
        """从交易所信息更新交易对精度信息"""
        try:
            # 如果没有提供exchange_info，则尝试使用client获取
            if exchange_info is None and client is not None:
                logging.info(f"正在获取交易规则信息...")
                exchange_info = client.exchange_info()
                logging.info(f"已获取交易规则信息")
            
            if exchange_info is None:
                logging.error("无法获取交易所信息")
                return False
            
            # 记录数据结构，便于调试
            logging.debug(f"exchangeInfo 数据结构: {json.dumps(exchange_info, indent=2)}")
                
            found = False
            for symbol_info in exchange_info.get('symbols', []):
                if symbol_info.get('symbol') == symbol:
                    found = True
                    config_data = {}
                    
                    # 获取系统精度 - 这是我们需要优先使用的
                    config_data['price_precision'] = symbol_info.get('pricePrecision', 2)
                    config_data['quantity_precision'] = symbol_info.get('quantityPrecision', 3)
                    
                    # 获取价格精度和数量精度 - 这里我们依然保存计算出的精度，但不用于显示
                    for filter_item in symbol_info.get('filters', []):
                        # 价格过滤器
                        if filter_item.get('filterType') == 'PRICE_FILTER':
                            tick_size = float(filter_item.get('tickSize', '0.00001'))
                            calculated_price_precision = self.calculate_precision(tick_size)
                            config_data['calculated_price_precision'] = calculated_price_precision
                            config_data['min_price'] = float(filter_item.get('minPrice', '0'))
                            config_data['max_price'] = float(filter_item.get('maxPrice', '0'))
                            config_data['tick_size'] = tick_size
                        
                        # 数量过滤器
                        if filter_item.get('filterType') == 'LOT_SIZE':
                            step_size = float(filter_item.get('stepSize', '0.00001'))
                            calculated_quantity_precision = self.calculate_precision(step_size)
                            config_data['calculated_quantity_precision'] = calculated_quantity_precision
                            config_data['min_quantity'] = float(filter_item.get('minQty', '0'))
                            config_data['max_quantity'] = float(filter_item.get('maxQty', '0'))
                            config_data['step_size'] = step_size
                        
                        # 最小名义价值
                        if filter_item.get('filterType') == 'MIN_NOTIONAL':
                            config_data['min_notional'] = float(filter_item.get('notional', '0'))
                    
                    # 记录交易对的合约类型
                    config_data['contract_type'] = symbol_info.get('contractType', '')
                    
                    # 记录交易对状态
                    config_data['status'] = symbol_info.get('status', '')
                    
                    # 尝试更新最大杠杆信息
                    if client is not None:
                        try:
                            brackets = client.leverage_brackets(symbol=symbol)
                            if brackets and len(brackets) > 0:
                                max_leverage = brackets[0]['brackets'][0]['initialLeverage']
                                config_data['max_leverage'] = max_leverage
                                logging.info(f"{symbol} 最大杠杆: {max_leverage}x")
                        except Exception as e:
                            logging.error(f"获取最大杠杆信息失败: {str(e)}")
                    
                    # 更新配置
                    self.update_symbol_config(symbol, config_data)
                    logging.info(f"更新 {symbol} 交易规则配置成功: 价格精度={config_data.get('price_precision', 'N/A')}, "
                                f"数量精度={config_data.get('quantity_precision', 'N/A')}, "
                                f"最小下单量={config_data.get('min_quantity', 'N/A')}")
                    return True
            
            if not found:
                logging.warning(f"未找到 {symbol} 的交易所信息")
            return found
        
        except Exception as e:
            logging.error(f"更新精度信息失败: {str(e)}")
            traceback.print_exc()
            return False
    
    def update_leverage_margin(self, symbol, leverage, margin_type):
        """更新杠杆和保证金模式"""
        config_data = {
            "leverage": leverage,
            "margin_type": margin_type
        }
        self.update_symbol_config(symbol, config_data)
        logging.info(f"更新 {symbol} 杠杆={leverage}, 保证金模式={margin_type}")
    
    @staticmethod
    def calculate_precision(step_size):
        """根据步长计算精度（小数位数）"""
        step_str = f"{step_size:.20f}".rstrip('0')
        if '.' in step_str:
            return len(step_str) - step_str.index('.') - 1
        return 0
    
    def format_price(self, symbol, price):
        """根据交易对配置格式化价格"""
        try:
            config = self.get_symbol_config(symbol)
            precision = config.get('price_precision', 2)
            return f"{float(price):.{precision}f}"
        except Exception as e:
            logging.error(f"格式化价格出错: {str(e)}")
            return str(price)
    
    def format_quantity(self, symbol, quantity):
        """根据交易对配置格式化数量"""
        try:
            config = self.get_symbol_config(symbol)
            precision = config.get('quantity_precision', 3)
            return f"{float(quantity):.{precision}f}"
        except Exception as e:
            logging.error(f"格式化数量出错: {str(e)}")
            return str(quantity)

def init_json_file(file_path, default_data):
    """如果文件不存在或解析出错，则初始化为默认数据"""
    try:
        with open(file_path, 'r') as f:
            json.load(f)
    except Exception:
        with open(file_path, 'w') as f:
            json.dump(default_data, f, indent=4, ensure_ascii=False)

# 默认配置（这里仅作为示例，现阶段不使用策略下单）
default_config = {
    "enable_strategy": False,
    "preset_orders": []
}

init_json_file(CONFIG_FILE, default_config)
init_json_file(CURRENT_ORDERS_FILE, [])        # 当前挂单记录（设为列表）
init_json_file(POSITIONS_FILE, [])             # 当前仓位记录（列表）
init_json_file(HISTORY_ORDERS_FILE, [])        # 历史订单记录

# ===================== 日志设置 ======================
logger = logging.getLogger()
logger.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler = logging.FileHandler(LOG_FILE, mode='a', encoding='utf-8')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)
stream_handler = logging.StreamHandler(sys.stdout)
stream_handler.setFormatter(formatter)
logger.addHandler(stream_handler)

# ===================== API 相关函数 ======================
def sync_time(client):
    """同步服务器时间，设置客户端时间偏移"""
    try:
        server_time = int(client.time()["serverTime"])
        local_time = int(time.time() * 1000)
        offset = server_time - local_time
        client.timestamp_offset = offset
        logging.info(f"服务器时间：{server_time}，本地时间：{local_time}，时间偏移：{offset}ms")
    except Exception as e:
        logging.error(f"同步时间出错：{e}")

def place_order(client, symbol, side, quantity, price, extra_params=None):
    """下限价订单（开仓）——保留接口，暂未在后台调用"""
    params = {
        'symbol': symbol,
        'side': side,
        'type': 'LIMIT',
        'timeInForce': 'GTC',
        'quantity': quantity,
        'price': price
    }
    if extra_params:
        params.update(extra_params)
    try:
        result = client.new_order(**params)
        logging.info(f"下单成功：{params}，返回：{result}")
        return result.get('orderId')
    except Exception as e:
        logging.error("下单错误：" + str(e))
        return None

def monitor_order(client, symbol, order_id, wait_time=60):
    """轮询检测订单是否成交（接口示例）"""
    start_time = time.time()
    while time.time() - start_time < wait_time:
        try:
            order_info = client.query_order(symbol=symbol, orderId=order_id)
            status = order_info.get('status', '')
            logging.info(f"订单 {order_id} 状态：{status}")
            if status == 'FILLED':
                return order_info
        except Exception as e:
            logging.error("查询订单状态出错：" + str(e))
        time.sleep(1)
    logging.info(f"订单 {order_id} 在 {wait_time} 秒内未成交")
    return None

def cancel_order(client, symbol, order_id):
    """取消未成交订单"""
    try:
        result = client.cancel_order(symbol=symbol, orderId=order_id)
        logging.info(f"取消订单 {order_id} 成功：{result}")
    except Exception as e:
        logging.error("取消订单出错：" + str(e))

def place_close_order(client, symbol, side, quantity, close_price):
    """下平仓单（平仓接口示例）"""
    params = {
        'symbol': symbol,
        'side': side,
        'type': 'LIMIT',
        'timeInForce': 'GTC',
        'quantity': quantity,
        'price': close_price
    }
    try:
        result = client.new_order(**params)
        logging.info(f"平仓单下单成功：{params}，返回：{result}")
        return result
    except Exception as e:
        logging.error("平仓单下单出错：" + str(e))
        return None

# ===================== 本地数据读写函数 ======================
def update_current_orders_from_api(client):
    """调用 API 获取当前挂单并写入 CURRENT_ORDERS_FILE"""
    try:
        # 调用 API 获取当前挂单，使用 get_open_orders 接口
        orders = client.get_orders()  # 改为使用 get_open_orders 并传入 SYMBOL
        with open(CURRENT_ORDERS_FILE, 'w', encoding='utf-8') as f:
            json.dump(orders, f, indent=4, ensure_ascii=False)
        # logging.info("当前挂单数据已更新。")
    except Exception as e:
        logging.error("更新当前挂单数据出错: " + str(e))

def update_positions_from_api(client):
    """调用 API 获取账户信息，并写入 POSITIONS_FILE（取 positions 字段）"""
    try:
        account_info = client.account()
        positions = account_info.get("positions", [])
        with open(POSITIONS_FILE, 'w', encoding='utf-8') as f:
            json.dump(positions, f, indent=4, ensure_ascii=False)
        # logging.info("账户仓位数据已更新。")
    except Exception as e:
        logging.error("更新仓位数据出错: " + str(e))

# ===================== 图形界面 ======================
class MainPage(Frame):
    """主界面：包含四个按钮切换到各个功能页面"""
    def __init__(self, master, switch_page_callback, client):
        super().__init__(master)
        self.switch_page_callback = switch_page_callback
        self.client = client
        self.create_widgets()

        # 调用全局时间更新函数
        start_time_update(self.time_label)
        
    def create_widgets(self):
        self.pack(fill=BOTH, expand=True)
        Label(self, text="主菜单", font=("宋体", 18)).pack(pady=20)
        
        # 系统时间显示
        self.time_label = Label(self, text="加载中...", font=("微软雅黑", 10), fg="black")
        self.time_label.pack(pady=2)
        
        # 功能按钮
        Button(self, text="查看日志", width=20,
               command=lambda: self.switch_page_callback("log")).pack(pady=10)
        Button(self, text="查看当前挂单", width=20,
               command=lambda: self.switch_page_callback("orders")).pack(pady=10)
        Button(self, text="查看当前仓位", width=20,
               command=lambda: self.switch_page_callback("positions")).pack(pady=10)
        Button(self, text="K线图表", width=20,
               command=lambda: self.switch_page_callback("kline")).pack(pady=10)
        Button(self, text="K线数据管理", width=20,
               command=lambda: self.switch_page_callback("kline_data")).pack(pady=10)
        
        # 时间同步按钮
        Button(self, text="同步系统时间", width=20, fg="blue", bg="#e6e6e6",
               command=self.sync_time_with_ntplib).pack(pady=10)
    
    def sync_time_with_ntplib(self):
        """使用ntplib同步系统时间"""
        if sync_time_with_ntplib(self.client):
            messagebox.showinfo("同步成功", "使用ntplib成功同步系统时间和交易所时间")
            # 立即更新时间显示
            if hasattr(self, 'time_label') and self.time_label.winfo_exists():
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.time_label.config(text=f" {current_time}")
        else:
            messagebox.showerror("同步失败", "同步系统时间失败，请检查网络连接或尝试手动同步")

class LogPage(Frame):
    """日志显示页面：显示 martingale_log.txt 文件内容，提供手动刷新"""
    def __init__(self, master, switch_page_callback):
        super().__init__(master)
        self.switch_page_callback = switch_page_callback
        self.create_widgets()
        self.refresh_log()
        
    def create_widgets(self):
        self.pack(fill=BOTH, expand=True)
        top_frame = Frame(self)
        top_frame.pack(fill="x", pady=5)
        Button(top_frame, text="返回主菜单", command=lambda: self.switch_page_callback("main")).pack(side="left", padx=5)
        Button(top_frame, text="刷新", command=self.refresh_log).pack(side="left", padx=5)
        self.text = Text(self, wrap='word')
        self.text.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar = Scrollbar(self, orient=VERTICAL, command=self.text.yview)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.text.config(yscrollcommand=scrollbar.set)
        
    def refresh_log(self):
        try:
            with open(LOG_FILE, 'r', encoding='utf-8') as f:
                data = f.read()
        except Exception as e:
            data = f"读取日志出错: {e}"
        self.text.delete("1.0", END)
        self.text.insert(END, data)

class OrdersPage(Frame):
    """当前挂单页面：将 current_orders.json 格式化后显示易读信息"""
    def __init__(self, master, switch_page_callback, client):
        super().__init__(master)
        self.switch_page_callback = switch_page_callback
        self.client = client
        self.create_widgets()
        self.refresh_orders()
        self._orders_update_job = None
        
    def create_widgets(self):
        self.pack(fill=BOTH, expand=True)
        top_frame = Frame(self)
        top_frame.pack(fill="x", pady=5)
        Button(top_frame, text="返回主菜单", command=lambda: self.switch_page_callback("main")).pack(side="left", padx=5)
        Button(top_frame, text="刷新", command=self.refresh_orders).pack(side="left", padx=5)
        self.text = Text(self, wrap='word')
        self.text.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar = Scrollbar(self, orient=VERTICAL, command=self.text.yview)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.text.config(yscrollcommand=scrollbar.set)
        
    def refresh_orders(self):
        try:
            with open(CURRENT_ORDERS_FILE, 'r', encoding='utf-8') as f:
                orders = json.load(f)
            display_text = ""
            if orders:
                # 假设 orders 为列表或字典格式，下面分别处理
                if isinstance(orders, list):
                    for order in orders:
                        # 这里按订单内部字段格式化输出
                        order_id = order.get("orderId", "未知")
                        symbol = order.get("symbol", "")
                        side = order.get("side", "")
                        order_type = order.get("type", "")
                        quantity = order.get("quantity", "")
                        price = order.get("price", "")
                        update_time = order.get("update_time", "")
                        display_text += (f"订单编号: {order_id}\n"
                                         f"  交易对: {symbol}\n"
                                         f"  类型: {order_type} / {side}\n"
                                         f"  数量: {quantity}\n"
                                         f"  价格: {price}\n"
                                         f"  更新时间: {update_time}\n"
                                         "--------------------------\n")
                elif isinstance(orders, dict):
                    for order_id, info in orders.items():
                        params = info.get("order_params", {})
                        symbol = params.get("symbol", "")
                        side = params.get("side", "")
                        order_type = params.get("type", "")
                        quantity = params.get("quantity", "")
                        price = params.get("price", "")
                        update_time = info.get("update_time", "")
                        display_text += (f"订单编号: {order_id}\n"
                                         f"  交易对: {symbol}\n"
                                         f"  类型: {order_type} / {side}\n"
                                         f"  数量: {quantity}\n"
                                         f"  价格: {price}\n"
                                         f"  更新时间: {update_time}\n"
                                         "--------------------------\n")
            else:
                display_text = "当前无挂单记录。"
        except Exception as e:
            display_text = f"读取挂单数据出错: {e}"
        self.text.delete("1.0", END)
        self.text.insert(END, display_text)

    def start_orders_update(self):
        self.stop_orders_update()
        self._orders_update_job = self.after(2000, self._orders_update_loop)

    def stop_orders_update(self):
        if self._orders_update_job:
            self.after_cancel(self._orders_update_job)
            self._orders_update_job = None

    def _orders_update_loop(self):
        update_current_orders_from_api(self.client)
        self.refresh_orders()
        self._orders_update_job = self.after(2000, self._orders_update_loop)

class PositionsPage(Frame):
    """当前仓位页面：格式化显示 positions.json 中的数据"""
    def __init__(self, master, switch_page_callback, client):
        super().__init__(master)
        self.switch_page_callback = switch_page_callback
        self.client = client
        self.create_widgets()
        self.refresh_positions()
        self._positions_update_job = None
        
    def create_widgets(self):
        self.pack(fill=BOTH, expand=True)
        top_frame = Frame(self)
        top_frame.pack(fill="x", pady=5)
        Button(top_frame, text="返回主菜单", command=lambda: self.switch_page_callback("main")).pack(side="left", padx=5)
        Button(top_frame, text="刷新", command=self.refresh_positions).pack(side="left", padx=5)
        self.text = Text(self, wrap='word')
        self.text.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar = Scrollbar(self, orient=VERTICAL, command=self.text.yview)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.text.config(yscrollcommand=scrollbar.set)
    
    def refresh_positions(self):
        try:
            with open(POSITIONS_FILE, 'r', encoding='utf-8') as f:
                positions = json.load(f)
            display_text = ""
            if positions:
                # 假设 positions 为列表，按每个仓位条目格式化输出
                for pos in positions:
                    symbol = pos.get("symbol", "")
                    positionAmt = pos.get("positionAmt", "")
                    entryPrice = pos.get("entryPrice", "")
                    unrealizedProfit = pos.get("unrealizedProfit", "")
                    display_text += (f"交易对: {symbol}\n"
                                     f"  持仓量: {positionAmt}\n"
                                     f"  未实现盈亏: {unrealizedProfit}\n"
                                     "--------------------------\n")
            else:
                display_text = "当前无仓位记录。"
        except Exception as e:
            display_text = f"读取仓位数据出错: {e}"
        self.text.delete("1.0", END)
        self.text.insert(END, display_text)

    def start_positions_update(self):
        self.stop_positions_update()
        self._positions_update_job = self.after(2000, self._positions_update_loop)

    def stop_positions_update(self):
        if self._positions_update_job:
            self.after_cancel(self._positions_update_job)
            self._positions_update_job = None

    def _positions_update_loop(self):
        update_positions_from_api(self.client)
        self.refresh_positions()
        self._positions_update_job = self.after(2000, self._positions_update_loop)

class KlinePage(Frame):
    """K线界面"""
    def __init__(self, master, switch_page_callback, client):
        super().__init__(master)
        self.switch_page_callback = switch_page_callback
        self.client = client
        self.symbols = []  # 存储所有交易对
        
        # 初始化配置管理器
        self.symbol_config_manager = SymbolConfig()
        
        # 初始化变量
        self.current_valid_symbol = SYMBOL  # 当前有效的交易对
        
        # 先加载交易对
        self.load_trading_symbols()
        if not self.symbols:  # 如果加载失败，使用默认值
            self.symbols = [SYMBOL]
            
        # 创建界面
        self.create_widgets()
        
        # 更新保证金和杠杆信息
        self.update_margin_leverage_info()
        
        # 加载K线数据
        self.load_kline_data()
        
        # 设置自动刷新为开启状态并启动
        self.auto_refresh_var.set(1) 
        self.toggle_auto_refresh()
        
        # 不再在此处启动时间更新，已移至工具栏中

    def load_trading_symbols(self):
        """加载所有可交易的交易对"""
        try:
            # 获取交易所信息
            info = self.client.exchange_info()
            # 获取所有可交易的交易对
            all_symbols = []
            for symbol_info in info['symbols']:
                if (symbol_info['status'] == 'TRADING' and 
                    symbol_info['contractType'] == 'PERPETUAL' and
                    symbol_info['symbol'].endswith('USDT')):  # 只获取USDT合约
                    all_symbols.append(symbol_info['symbol'])
            
            # 对交易对进行排序，优先显示主流币种
            priority_coins = ['BTC', 'ETH', 'SOL', 'BNB', 'XRP', 'DOT', 'ADA']
            
            def get_sort_key(symbol):
                # 移除USDT后缀
                base_coin = symbol.replace('USDT', '')
                # 如果是优先币种，返回其在列表中的索引
                if base_coin in priority_coins:
                    return (0, priority_coins.index(base_coin))
                # 其他币种按字母顺序排序
                return (1, base_coin)
            
            self.symbols = sorted(all_symbols, key=get_sort_key)
            logging.info(f"加载了 {len(self.symbols)} 个交易对")
            
        except Exception as e:
            logging.error(f"加载交易对失败: {str(e)}")
            self.symbols = []  # 加载失败时设为空列表

    def filter_symbols(self, pattern):
        """根据输入过滤交易对"""
        pattern = pattern.upper()
        self.filtered_symbols = [s for s in self.symbols if pattern in s]
        self.update_symbol_listbox()

    def update_symbol_listbox(self):
        """更新交易对列表显示"""
        self.symbol_listbox.delete(0, 'end')
        for symbol in self.filtered_symbols[:10]:  # 最多显示10个选项
            self.symbol_listbox.insert('end', symbol)
        
        # 如果有匹配项，显示列表框
        if self.filtered_symbols:
            self.symbol_listbox.place(x=self.symbol_entry.winfo_x(),
                                    y=self.symbol_entry.winfo_y() + self.symbol_entry.winfo_height(),
                                    width=self.symbol_entry.winfo_width())
        else:
            self.symbol_listbox.place_forget()

    def on_symbol_select(self, event=None):
        """当用户从下拉列表中选择一个交易对时"""
        self.on_symbol_change()
        self.symbol_combobox.selection_clear()  # 清除选择，避免重复触发
        
    def on_symbol_focus(self, event=None):
        """当交易对选择框获得焦点时"""
        # 优先显示主流币种
        priority_coins = ['BTC', 'ETH', 'SOL', 'BNB', 'XRP', 'DOT', 'ADA']
        def get_sort_key(symbol):
            base_coin = symbol.replace('USDT', '')
            if base_coin in priority_coins:
                return (0, priority_coins.index(base_coin))
            return (1, base_coin)
        
        sorted_symbols = sorted(self.symbols, key=get_sort_key)
        self.symbol_combobox['values'] = sorted_symbols
        
        # 展开下拉列表
        if not self.symbol_combobox.winfo_ismapped():
            self.symbol_combobox.event_generate('<Down>')  # 展开下拉列表
            
    def on_symbol_var_change(self, *args):
        """当输入内容变化时触发"""
        current_text = self.symbol_var.get().upper()
        
        # 如果输入框为空，显示所有交易对（常用在前）
        if not current_text:
            # 优先显示主流币种
            priority_coins = ['BTC', 'ETH', 'SOL', 'BNB', 'XRP', 'DOT', 'ADA']
            def get_sort_key(symbol):
                base_coin = symbol.replace('USDT', '')
                if base_coin in priority_coins:
                    return (0, priority_coins.index(base_coin))
                return (1, base_coin)
            
            sorted_symbols = sorted(self.symbols, key=get_sort_key)
            self.symbol_combobox['values'] = sorted_symbols
        else:
            # 有输入内容时，只显示匹配的交易对（不区分大小写）
            filtered_symbols = [s for s in self.symbols if current_text in s.upper()]
            self.symbol_combobox['values'] = filtered_symbols
        
        # 如果有匹配结果且列表未展开，则展开列表
        if self.symbol_combobox['values'] and not self.symbol_combobox.winfo_ismapped():
            self.symbol_combobox.event_generate('<Down>')
        
        # 保持输入框焦点，光标在末尾
        self.after(10, lambda: self.symbol_combobox.icursor('end'))
        self.after(10, lambda: self.symbol_combobox.focus_set())

    def on_symbol_change(self, event=None):
        """交易对变化时更新币种单位显示和保证金信息"""
        try: 
            symbol = self.symbol_var.get().strip().upper()
            if not symbol.endswith('USDT'):
                symbol = f"{symbol}USDT"
            self.symbol_var.set(symbol)  # 更新为标准格式
            
            # 更新当前有效交易对
            self.current_valid_symbol = symbol
            
            # 更新币种单位显示
            coin = symbol.replace("USDT", "")
            self.coin_unit_label.config(text=coin)
            
            # 从API获取交易规则信息并更新配置
            try:
                # 获取交易所信息并更新交易对精度等信息
                self.symbol_config_manager.update_symbol_precision(symbol, client=self.client)
                logging.info(f"已更新{symbol}交易规则配置")
                
                # 清空并重置价格和数量输入框
                self.price_var.set("")
                self.amount_var.set("")
                
                # 重新应用输入验证
                vcmd_price = (self.register(self.validate_price_input), '%P')
                self.price_entry.config(validate="key", validatecommand=vcmd_price)
                
                vcmd_amount = (self.register(self.validate_amount_input), '%P')
                self.amount_entry.config(validate="key", validatecommand=vcmd_amount)
                
            except Exception as e:
                logging.error(f"更新交易对配置失败: {str(e)}")
            
            # 更新保证金和杠杆信息
            self.update_margin_leverage_info()
            
            # 刷新K线图
            self.load_kline_data()
        except Exception as e:
            logging.error(f"更新交易对信息失败: {str(e)}")

    def on_symbol_return(self, event=None):
        """当用户按下回车键时"""
        if self.symbol_combobox['values']:
            # 如果没有选中项但有匹配结果，选择第一个
            if not self.symbol_combobox.get() in self.symbol_combobox['values']:
                self.symbol_var.set(self.symbol_combobox['values'][0])
            self.on_symbol_change()
        return 'break'

    def create_widgets(self):
        """创建界面元素"""
        self.pack(fill=BOTH, expand=True)
        
        # 创建顶部栏
        top_frame = Frame(self)
        top_frame.pack(fill="x", pady=5)
        
        # 返回按钮
        Button(top_frame, text="返回主菜单", 
               command=lambda: self.switch_page_callback("main")).pack(side="left", padx=5)
        
        # 同步时间按钮
        Button(top_frame, text="同步时间", fg="blue", bg="#e6e6e6", 
               command=self.sync_time_with_ntplib).pack(side="left", padx=5)
        
        # 添加查看配置按钮
        Button(top_frame, text="查看配置", 
               command=self.display_symbol_config).pack(side="left", padx=5)
        
        # 添加测试交易规则按钮
        Button(top_frame, text="测试交易规则", 
               command=self.test_exchange_info).pack(side="left", padx=5)
        
        # 指标选择
        self.indicator_var = StringVar(value="MA")
        indicator_menu = ttk.Combobox(top_frame, textvariable=self.indicator_var, 
                                    values=["None", "MA", "BOLL"], width=6)
        indicator_menu.pack(side="left", padx=5)
        # 绑定指标选择变化事件
        self.indicator_var.trace('w', self.on_indicator_change)
        
        # 交易对选择下拉框
        self.symbol_var = StringVar(value=SYMBOL)
        self.symbol_combobox = ttk.Combobox(top_frame, textvariable=self.symbol_var, 
                                          values=self.symbols, width=12)
        self.symbol_combobox.pack(side="left", padx=5)
        
        # 绑定交易对选择框事件
        self.symbol_var.trace('w', self.on_symbol_var_change)  # 监听变量变化
        self.symbol_combobox.bind('<<ComboboxSelected>>', self.on_symbol_select)
        self.symbol_combobox.bind('<FocusIn>', self.on_symbol_focus)
        self.symbol_combobox.bind('<Return>', self.on_symbol_return)
        
        # K线周期选择
        self.interval_var = StringVar(value="15m")
        interval_options = ["1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w"]
        interval_menu = ttk.Combobox(top_frame, textvariable=self.interval_var, 
                                   values=interval_options, width=5)
        interval_menu.pack(side="left", padx=5)
        # 绑定K线周期变化事件
        self.interval_var.trace('w', self.on_interval_change)
        
        Button(top_frame, text="刷新图表", command=self.load_kline_data).pack(side="left", padx=5)
        
        # 自动刷新设置
        refresh_frame = Frame(top_frame)
        refresh_frame.pack(side="left", padx=10)
        self.auto_refresh_var = IntVar(value=1) # Default to on
        self.auto_refresh_checkbtn = ttk.Checkbutton(refresh_frame, text="自动刷新", 
                                                  variable=self.auto_refresh_var,
                                                  command=self.toggle_auto_refresh)
        self.auto_refresh_checkbtn.pack(side="left")
        
        # K线刷新控制 (价格刷新固定为0.1s)
        Label(refresh_frame, text="K线刷新(秒):").pack(side="left") 
        self.active_kline_refresh_s = 1.0 # 当前激活的K线刷新间隔，秒
        self.kline_refresh_interval_var = StringVar(value=str(self.active_kline_refresh_s)) 
        kline_refresh_interval_entry = Entry(refresh_frame, textvariable=self.kline_refresh_interval_var, width=3)
        kline_refresh_interval_entry.pack(side="left")
        # 绑定输入完成事件
        kline_refresh_interval_entry.bind("<Return>", self.handle_kline_interval_input_finished)
        kline_refresh_interval_entry.bind("<FocusOut>", self.handle_kline_interval_input_finished)
        
        # 主体部分使用水平分割
        main_frame = Frame(self)
        main_frame.pack(fill=BOTH, expand=True)

        # 左侧K线图区域
        self.chart_frame = Frame(main_frame)
        self.chart_frame.pack(side=LEFT, fill=BOTH, expand=True)

        # 右侧交易面板
        trade_frame = Frame(main_frame, width=300, bg='#f0f0f0')
        trade_frame.pack(side=RIGHT, fill=Y, padx=10, pady=5)
        trade_frame.pack_propagate(False)  # 固定宽度

        # 保证金模式和杠杆设置
        margin_frame = Frame(trade_frame, bg='#f0f0f0')
        margin_frame.pack(fill=X, pady=5)
        self.margin_button = Button(margin_frame, text="加载中...", width=15,
               command=self.change_margin_type)
        self.margin_button.pack(side=LEFT, padx=5)
        self.leverage_button = Button(margin_frame, text="加载中...", width=15,
               command=self.change_leverage)
        self.leverage_button.pack(side=LEFT, padx=5)
        self.update_margin_leverage_info()  # 初始化状态显示

        # 可用资金显示
        balance_frame = Frame(trade_frame, bg='#f0f0f0')
        balance_frame.pack(fill=X, pady=5)
        Label(balance_frame, text="可用资金:", bg='#f0f0f0').pack(side=LEFT, padx=5)
        self.balance_label = Label(balance_frame, text="加载中...", bg='#f0f0f0')
        self.balance_label.pack(side=LEFT, padx=5)
        self.start_balance_update()

        # 开平仓选择
        position_frame = Frame(trade_frame, bg='#f0f0f0')
        position_frame.pack(fill=X, pady=5)
        Label(position_frame, text="持仓方向:", bg='#f0f0f0').pack(side=LEFT, padx=5)
        self.position_type_var = StringVar(value="开仓")
        ttk.Radiobutton(position_frame, text="开仓", value="开仓", 
                       variable=self.position_type_var, command=self.on_position_type_change).pack(side=LEFT, padx=5)
        ttk.Radiobutton(position_frame, text="平仓", value="平仓", 
                       variable=self.position_type_var, command=self.on_position_type_change).pack(side=LEFT, padx=5)

        # 交易类型选择
        type_frame = Frame(trade_frame, bg='#f0f0f0')
        type_frame.pack(fill=X, pady=5)
        Label(type_frame, text="订单类型:", bg='#f0f0f0').pack(side=LEFT, padx=5)
        self.order_type_var = StringVar(value="LIMIT")
        ttk.Radiobutton(type_frame, text="限价单", value="LIMIT", 
                       variable=self.order_type_var, command=self.on_order_type_change).pack(side=LEFT, padx=10)
        ttk.Radiobutton(type_frame, text="市价单", value="MARKET", 
                       variable=self.order_type_var, command=self.on_order_type_change).pack(side=LEFT, padx=10)

        # 价格输入区域
        price_frame = Frame(trade_frame, bg='#f0f0f0')
        price_frame.pack(fill=X, pady=5)
        Label(price_frame, text="价格(USDT):", bg='#f0f0f0').pack(side=LEFT, padx=5)
        self.price_var = StringVar()
        self.price_entry = Entry(price_frame, textvariable=self.price_var)
        self.price_entry.pack(side=LEFT, fill=X, expand=True, padx=5)
        
        # 添加价格输入验证
        vcmd_price = (self.register(self.validate_price_input), '%P')
        self.price_entry.config(validate="key", validatecommand=vcmd_price)

        # 数量输入区域
        amount_frame = Frame(trade_frame, bg='#f0f0f0')
        amount_frame.pack(fill=X, pady=5)
        Label(amount_frame, text="数量:", bg='#f0f0f0').pack(side=LEFT, padx=5)
        self.amount_var = StringVar()
        self.amount_entry = Entry(amount_frame, textvariable=self.amount_var)
        self.amount_entry.pack(side=LEFT, fill=X, expand=True, padx=5)
        
        # 添加数量输入验证
        vcmd_amount = (self.register(self.validate_amount_input), '%P')
        self.amount_entry.config(validate="key", validatecommand=vcmd_amount)

        # 修改数量单位选择部分
        unit_frame = Frame(amount_frame, bg='#f0f0f0')
        unit_frame.pack(side=RIGHT)
        self.amount_unit_var = StringVar(value="COIN")
        self.coin_unit_label = ttk.Radiobutton(unit_frame, text="BTC", value="COIN", 
                       variable=self.amount_unit_var, command=self.on_unit_change)
        self.coin_unit_label.pack(side=LEFT)
        ttk.Radiobutton(unit_frame, text="USDT", value="USDT", 
                       variable=self.amount_unit_var, command=self.on_unit_change).pack(side=LEFT)

        # 快捷比例按钮
        ratio_frame = Frame(trade_frame, bg='#f0f0f0')
        ratio_frame.pack(fill=X, pady=5)
        for ratio in ["25%", "50%", "75%", "100%"]:
            Button(ratio_frame, text=ratio, width=8,
                   command=lambda r=ratio: self.set_amount_ratio(r)).pack(side=LEFT, padx=2)

        # 交易按钮区域
        self.trade_buttons_frame = Frame(trade_frame, bg='#f0f0f0')
        self.trade_buttons_frame.pack(fill=X, pady=10)
        self.update_trade_buttons()

    def on_position_type_change(self):
        """开平仓类型改变时的处理"""
        self.update_trade_buttons()

    def update_trade_buttons(self):
        """根据开平仓选择更新交易按钮"""
        # 清除现有按钮
        for widget in self.trade_buttons_frame.winfo_children():
            widget.destroy()

        if self.position_type_var.get() == "开仓":
            # 开仓按钮
            Button(self.trade_buttons_frame, text="开多", width=15, height=2, bg='#77d879', fg='white',
                   command=lambda: self.place_order("LONG", "BUY")).pack(side=LEFT, padx=5, pady=5)
            Button(self.trade_buttons_frame, text="开空", width=15, height=2, bg='#d16d6d', fg='white',
                   command=lambda: self.place_order("SHORT", "SELL")).pack(side=LEFT, padx=5, pady=5)
        else:
            # 平仓按钮
            Button(self.trade_buttons_frame, text="平多", width=15, height=2, bg='#d16d6d', fg='white',
                   command=lambda: self.place_order("LONG", "SELL")).pack(side=LEFT, padx=5, pady=5)
            Button(self.trade_buttons_frame, text="平空", width=15, height=2, bg='#77d879', fg='white',
                   command=lambda: self.place_order("SHORT", "BUY")).pack(side=LEFT, padx=5, pady=5)

    def place_order(self, position_side, side):
        """执行交易操作"""
        try:
            symbol = self.current_valid_symbol
            price_str = self.price_var.get()
            amount_str = self.amount_var.get()
            
            if not price_str or not amount_str:
                messagebox.showinfo("提示", "请输入价格和数量")
                return
            
            # 转换为数值
            try:
                price = float(price_str)
                amount = float(amount_str)
            except ValueError:
                messagebox.showinfo("提示", "价格和数量必须是数字")
                return
            
            # 获取当前是开仓还是平仓
            position_type = self.position_type_var.get()
            
            # 确认交易
            if position_type == "开仓":
                action = "开仓" + ("做多" if side == "BUY" else "做空")
            else:
                action = "平仓" + ("多头" if side == "SELL" else "空头")
                
            if not messagebox.askyesno("确认交易", f"确认要{action}吗？\n交易对: {symbol}\n价格: {price}\n数量: {amount}"):
                return
            
            # 基本参数
            order_type = self.order_type_var.get()
            params = {
                'symbol': symbol,
                'side': side,
                'type': order_type,
                'positionSide': position_side,  # LONG或SHORT
            }
                
            # 根据单位转换数量
            if self.amount_unit_var.get() == "USDT":
                # 如果是USDT单位，转换为币种数量
                # 使用K线数据获取最新价格
                if hasattr(self, 'klines_df') and self.klines_df is not None and not self.klines_df.empty:
                    current_price = float(self.klines_df.iloc[-1]['Close'])
                else:
                    ticker = self.client.ticker_price(symbol)
                    current_price = float(ticker['price'])
                amount = amount / current_price
            
            # 格式化价格和数量，确保符合交易所要求的精度
            formatted_price = self.symbol_config_manager.format_price(symbol, price)
            formatted_quantity = self.symbol_config_manager.format_quantity(symbol, amount)
            
            logging.info(f"准备{action}: {symbol}, 价格={formatted_price}, 数量={formatted_quantity}")
            
            # 根据订单类型设置参数
            if order_type == "LIMIT":
                params.update({
                    'timeInForce': 'GTC',
                    'price': formatted_price,
                    'quantity': formatted_quantity
                })
            else:  # MARKET
                params['quantity'] = formatted_quantity
                
            # 发送请求
            try:
                result = self.client.new_order(**params)
                messagebox.showinfo("下单成功", f"订单ID: {result['orderId']}\n方向: {position_side}\n数量: {formatted_quantity}")
                logging.info(f"{action}成功: {result}")
            except Exception as e:
                messagebox.showerror("下单失败", str(e))
                logging.error(f"{action}失败: {str(e)}")
            
        except Exception as e:
            messagebox.showerror("下单失败", str(e))
            logging.error(f"下单过程出错: {str(e)}")

    def on_order_type_change(self):
        """订单类型改变时的处理"""
        if self.order_type_var.get() == "MARKET":
            self.price_entry.config(state='disabled')
        else:
            self.price_entry.config(state='normal')

    def set_amount_ratio(self, ratio):
        """设置金额为账户余额的指定比例"""
        try:
            # 获取账户信息
            account_info = self.client.account()
            available_balance = float(account_info.get('availableBalance', 0))
            percentage = float(ratio.strip('%')) / 100
            
            # 使用K线数据获取最新价格
            if hasattr(self, 'klines_df') and self.klines_df is not None and not self.klines_df.empty:
                current_price = float(self.klines_df.iloc[-1]['Close'])
            else:
                ticker = self.client.ticker_price(self.current_valid_symbol)
                current_price = float(ticker['price'])
            
            # 获取当前杠杆倍数
            position_info = self.client.get_position_risk(symbol=self.current_valid_symbol)
            leverage = float(position_info[0].get('leverage', 1))
            
            # 计算可用数量（考虑杠杆）
            if self.amount_unit_var.get() == "USDT":
                max_amount = available_balance * leverage
            else:  # COIN
                max_amount = (available_balance * leverage) / current_price
                
            self.amount_var.set(f"{max_amount * percentage:.4f}")
        except Exception as e:
            messagebox.showerror("错误", f"计算数量失败: {str(e)}")

    def get_kline_file_path(self, symbol, interval, dt=None):
        symbol_dir = os.path.join(KLINE_DATA_DIR, symbol)
        os.makedirs(symbol_dir, exist_ok=True)
        if interval == '1m' and dt is not None:
            month_str = get_month_str(dt)
            return os.path.join(symbol_dir, f"{month_str}.csv")
        else:
            return os.path.join(symbol_dir, f"{interval}.csv")

    def save_klines_to_local(self, klines_df, symbol, interval):
        try:
            df = klines_df.copy()
            if 'Open time' in df.columns:
                df['Open time'] = pd.to_datetime(df['Open time'])
            if interval == '1m':
                # 按月份分组保存
                if df.empty:
                    return
                df['month'] = df['Open time'].dt.strftime('%Y-%m')
                for month, group in df.groupby('month'):
                    file_path = os.path.join(KLINE_DATA_DIR, symbol, f"{month}.csv")
                    if os.path.exists(file_path):
                        try:
                            existing_df = pd.read_csv(file_path)
                            if 'Open time' in existing_df.columns:
                                existing_df['Open time'] = pd.to_datetime(existing_df['Open time'])
                            combined_df = pd.concat([existing_df, group.drop(columns=['month'])])
                            combined_df = combined_df.drop_duplicates(subset=['Open time'], keep='last')
                            combined_df = combined_df.sort_values(by='Open time')
                            combined_df.to_csv(file_path, index=False)
                        except Exception as e:
                            group.drop(columns=['month']).to_csv(file_path, index=False)
                    else:
                        group.drop(columns=['month']).to_csv(file_path, index=False)
            else:
                file_path = self.get_kline_file_path(symbol, interval)
                if os.path.exists(file_path):
                    try:
                        existing_df = pd.read_csv(file_path)
                        if 'Open time' in existing_df.columns:
                            existing_df['Open time'] = pd.to_datetime(existing_df['Open time'])
                        combined_df = pd.concat([existing_df, df])
                        combined_df = combined_df.drop_duplicates(subset=['Open time'], keep='last')
                        combined_df = combined_df.sort_values(by='Open time')
                        combined_df.to_csv(file_path, index=False)
                    except Exception as e:
                        df.to_csv(file_path, index=False)
                else:
                    df.to_csv(file_path, index=False)
        except Exception as e:
            logging.error(f"保存K线数据失败: {str(e)}")

    def load_klines_from_local(self, symbol, interval, start_dt=None, end_dt=None):
        try:
            symbol_dir = os.path.join(KLINE_DATA_DIR, symbol)
            if interval == '1m':
                # 只加载本月和上个月，提升速度
                now = datetime.now()
                this_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                last_month = (this_month - pd.DateOffset(months=1)).to_pydatetime()
                files = []
                # 优先本月
                this_month_file = f"{this_month.strftime('%Y-%m')}.csv"
                if os.path.exists(os.path.join(symbol_dir, this_month_file)):
                    files.append(this_month_file)
                # 如果需要再加上上个月
                # last_month_file = f"{last_month.strftime('%Y-%m')}.csv"
                # if os.path.exists(os.path.join(symbol_dir, last_month_file)):
                #     files.append(last_month_file)
                dfs = []
                for f in files:
                    file_path = os.path.join(symbol_dir, f)
                    try:
                        df = pd.read_csv(file_path)
                        if not df.empty and 'Open time' in df.columns:
                            df['Open time'] = pd.to_datetime(df['Open time'])
                            dfs.append(df)
                    except Exception as e:
                        logging.error(f"读取分月K线失败: {file_path}, {e}")
                if dfs:
                    all_df = pd.concat(dfs)
                    all_df = all_df.drop_duplicates(subset=['Open time'], keep='last')
                    all_df = all_df.sort_values(by='Open time')
                    # 按需筛选
                    if start_dt is not None:
                        all_df = all_df[all_df['Open time'] >= start_dt]
                    if end_dt is not None:
                        all_df = all_df[all_df['Open time'] <= end_dt]
                    return all_df.reset_index(drop=True)
                else:
                    return pd.DataFrame()
            else:
                file_path = self.get_kline_file_path(symbol, interval)
                if os.path.exists(file_path):
                    df = pd.read_csv(file_path)
                    if not df.empty and 'Open time' in df.columns:
                        df['Open time'] = pd.to_datetime(df['Open time'])
                        if start_dt is not None:
                            df = df[df['Open time'] >= start_dt]
                        if end_dt is not None:
                            df = df[df['Open time'] <= end_dt]
                        return df.reset_index(drop=True)
                return pd.DataFrame()
        except Exception as e:
            logging.error(f"加载本地K线数据失败: {str(e)}")
            return pd.DataFrame()

    def update_range_labels(self):
        """更新当前视图范围内的最高价和最低价标签"""
        try:
            if not hasattr(self, 'klines_df') or self.klines_df is None or self.klines_df.empty:
                return
            
            if not hasattr(self, 'ax_main') or not hasattr(self, 'canvas'):
                return
                
            # 为了提高性能，限制更新频率
            if hasattr(self, 'last_range_update_time'):
                current_time = time.time()
                # 平移或缩放过程中，限制更新频率为每200毫秒一次
                if (self.pan_active or self.scale_active) and current_time - self.last_range_update_time < 0.2:
                    return
                self.last_range_update_time = current_time
            else:
                self.last_range_update_time = time.time()
            
            # 获取当前x轴范围
            xlim = self.ax_main.get_xlim()
            
            # 删除旧的高低价标签
            if hasattr(self, 'high_price_line') and self.high_price_line:
                self.high_price_line.remove()
                self.high_price_line = None
            
            if hasattr(self, 'low_price_line') and self.low_price_line:
                self.low_price_line.remove()
                self.low_price_line = None
            
            if hasattr(self, 'high_price_label') and self.high_price_label:
                self.high_price_label.remove()
                self.high_price_label = None
            
            if hasattr(self, 'low_price_label') and self.low_price_label:
                self.low_price_label.remove()
                self.low_price_label = None
            
            # 删除调试信息标签
            if hasattr(self, 'debug_info_label') and self.debug_info_label:
                self.debug_info_label.remove()
                self.debug_info_label = None
            
            # 计算当前视图下可见的K线索引范围
            total_bars = len(self.klines_df)
            
            # 修改可见K线的计算逻辑
            # 只有当K线的中心点（整数索引）在视图范围内时才计算为可见
            # 向上取整，确保只有当K线中心点在边界内才算该K线可见
            start_index = math.ceil(xlim[0])
            # 向下取整，确保只有当K线中心点在边界内才算该K线可见
            end_index = math.floor(xlim[1])
            
            # 确保索引在有效范围内
            start_index = max(0, start_index)
            end_index = min(total_bars - 1, end_index)
            
            # 检查索引范围是否有效
            if start_index > end_index or start_index < 0 or end_index >= total_bars:
                return
            
            # 获取可见范围内的K线数据
            visible_bars = self.klines_df.iloc[start_index:end_index+1]
            
            if visible_bars.empty:
                return
            
            # 获取最高价和最低价
            high_price = visible_bars['High'].max()
            low_price = visible_bars['Low'].min()
            
            # 获取当前交易对的价格精度
            symbol = self.current_valid_symbol
            config = self.symbol_config_manager.get_symbol_config(symbol)
            price_precision = config.get('price_precision', 2)
            
            # 获取微软雅黑字体
            font_path = 'C:/Windows/Fonts/msyh.ttc'
            font_prop = fm.FontProperties(fname=font_path)
            
            # 计算图表中点
            x_middle = (xlim[0] + xlim[1]) / 2
            
            # 找出最高价和最低价的位置（如果有多个相同的最高价或最低价，取最接近当前时间的一个）
            # 获取最后一根K线的索引（代表当前时间）
            latest_index = len(self.klines_df) - 1
            
            # 找到所有等于最高价的索引
            high_indices = visible_bars.index[visible_bars['High'] == high_price].tolist()
            # 找到所有等于最低价的索引
            low_indices = visible_bars.index[visible_bars['Low'] == low_price].tolist()
            
            # 如果有多个相同的最高价，选择最接近当前时间（最新K线）的一个
            if len(high_indices) > 0:
                high_index = min(high_indices, key=lambda idx: abs(idx - latest_index))
            else:
                high_index = visible_bars['High'].idxmax()
                
            # 如果有多个相同的最低价，选择最接近当前时间（最新K线）的一个
            if len(low_indices) > 0:
                low_index = min(low_indices, key=lambda idx: abs(idx - latest_index))
            else:
                low_index = visible_bars['Low'].idxmin()
            
            # 计算显示位置（相对于整个数据集的索引）
            high_pos = visible_bars.index.get_loc(high_index) + start_index
            low_pos = visible_bars.index.get_loc(low_index) + start_index
            
            # 获取Y轴范围
            ylim = self.ax_main.get_ylim()
            
            # K线理论宽度估计
            k_width = 0.5  # 假设K线宽度为0.5
            
            # # 添加调试信息标签到左上角
            # debug_info = (
            #     f"X轴范围: {xlim[0]:.1f}-{xlim[1]:.1f}\n"
            #     f"Y轴范围: {ylim[0]:.2f}-{ylim[1]:.2f}\n"
            #     f"可见K线: {start_index}-{end_index} (共{end_index-start_index+1}根)\n"
            #     f"有效K线: {len(self.klines_df)}\n"
            #     f"总K线数: {total_bars}根\n"
            #     f"最高价: {high_price:.{price_precision}f} (位于第{high_pos}根K线)\n"
            #     f"最低价: {low_price:.{price_precision}f} (位于第{low_pos}根K线)\n"
            #     # f"K线中心判定: 整数索引±{k_width/2:.2f}"
            # )
            
            # 在左上角显示调试信息
            # self.debug_info_label = self.ax_main.text(
            #     0.02, 0.98, debug_info,
            #     transform=self.ax_main.transAxes,  # 使用轴的相对坐标系统
            #     verticalalignment='top',
            #     horizontalalignment='left',
            #     fontsize=9,
            #     bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
            #     zorder=10  # 确保显示在最上层
            # )
            
            # 设置箭头属性 - 灰色水平细短线
            arrow_props = dict(
                arrowstyle='-',
                color='gray',
                linewidth=0.8,
                shrinkA=0,
                shrinkB=0
            )
            
            # 根据位置决定标签方向和连接点
            if high_pos < x_middle:  # 左半区
                high_ha = 'left'  # 水平对齐方式
                high_xt = (20, 0)  # 标签偏移量
                high_connect_x = high_pos  # 连接到K线位置
                high_position = (high_connect_x, high_price)
            else:  # 右半区
                high_ha = 'right'
                high_xt = (-20, 0)
                high_connect_x = high_pos
                high_position = (high_connect_x, high_price)
            
            if low_pos < x_middle:  # 左半区
                low_ha = 'left'
                low_xt = (20, 0)
                low_connect_x = low_pos
                low_position = (low_connect_x, low_price)
            else:  # 右半区
                low_ha = 'right'
                low_xt = (-20, 0)
                low_connect_x = low_pos
                low_position = (low_connect_x, low_price)
            
            # 如果正在拖动或缩放，只显示简单线条以提高性能
            # if self.pan_active or self.scale_active:
            #     # 绘制最高价和最低价水平线（虚线）
            #     self.high_price_line = self.ax_main.axhline(y=high_price, color='green', linestyle='--', linewidth=1, alpha=0.4)
            #     self.low_price_line = self.ax_main.axhline(y=low_price, color='red', linestyle='--', linewidth=1, alpha=0.4)
            # else:
            #     # 正常显示，添加完整标签
            #     # 绘制最高价水平线（虚线）
            #     self.high_price_line = self.ax_main.axhline(y=high_price, color='green', linestyle='--', linewidth=1, alpha=0.6)
                
            #     # 绘制最低价水平线（虚线）
            #     self.low_price_line = self.ax_main.axhline(y=low_price, color='red', linestyle='--', linewidth=1, alpha=0.6)
                
            # 创建最高价标签，使用灰色小字体，无边框，带箭头，添加千位分隔符
            self.high_price_label = self.ax_main.annotate(
                f"{high_price:,.{price_precision}f}", 
                xy=high_position,  # 箭头指向的位置
                xytext=high_xt,  # 文本相对于xy的偏移
                textcoords='offset points',
                color='gray',
                fontproperties=font_prop,  # 使用微软雅黑字体
                fontsize=8,
                ha=high_ha,  # 水平对齐方式
                va='center',  # 垂直对齐方式
                arrowprops=arrow_props
            )
            
            # 创建最低价标签，使用灰色小字体，无边框，带箭头，添加千位分隔符
            self.low_price_label = self.ax_main.annotate(
                f"{low_price:,.{price_precision}f}", 
                xy=low_position,
                xytext=low_xt,
                textcoords='offset points',
                color='gray',
                fontproperties=font_prop,  # 使用微软雅黑字体
                fontsize=8,
                ha=low_ha,
                va='center',
                arrowprops=arrow_props
            )
            
        except Exception as e:
            logging.error(f"更新价格范围标签失败: {str(e)}")
            traceback.print_exc()

    def on_mouse_release(self, event):
        """处理鼠标释放事件"""
        if self.pan_active or self.scale_active:
            # 结束平移或缩放
            self.pan_active = False
            self.scale_active = False
            self.canvas.get_tk_widget().config(cursor="crosshair")  # 恢复十字星光标
            
            # 更新价格范围标签（拖动结束后显示完整标签）
            self.update_range_labels()

    def load_kline_data(self):
        """加载K线数据，并绘制K线图"""
        try:
            symbol = self.current_valid_symbol
            interval = self.interval_var.get()
            
            # 初始加载显示的K线最大数量
            MAX_DISPLAY_KLINES = 200
            # 初始视图中显示的K线数量
            INITIAL_VIEW_KLINES = 100
            
            # 首先尝试从本地加载历史K线数据
            local_klines = self.load_klines_from_local(symbol, interval)
            
            # 确定当前时间（东八区/北京时间）
            current_time = datetime.now()
            
            # 如果需要获取特定时间段的数据，可以设置startTime和endTime参数
            # 计算API请求的开始时间（可选）
            # start_time = int((current_time - timedelta(days=7)).timestamp() * 1000)  # 获取近7天数据
            
            # 从API获取最新的K线数据
            api_klines = self.client.klines(
                symbol=symbol,
                interval=interval,
                limit=300
                # startTime=start_time  # 如需指定开始时间可取消注释
            )
            
            # 将API返回的K线数据转换为DataFrame
            api_df = pd.DataFrame(api_klines, columns=[
                'Open time', 'Open', 'High', 'Low', 'Close', 'Volume',
                'Close time', 'Quote asset volume', 'Number of trades',
                'Taker buy base asset volume', 'Taker buy quote asset volume', 'Ignore'
            ])
            
            # 转换数据类型
            # 将UTC毫秒时间戳转换为东八区datetime对象
            api_df['Open time'] = pd.to_datetime(api_df['Open time'], unit='ms') + pd.Timedelta(hours=8)
            api_df['Close time'] = pd.to_datetime(api_df['Close time'], unit='ms') + pd.Timedelta(hours=8)
            
            api_df = api_df.astype({
                'Open': float, 'High': float, 'Low': float,
                'Close': float, 'Volume': float
            })
            
            # 添加一个创建空DataFrame的辅助函数
            def create_empty_klines_df():
                return pd.DataFrame(columns=[
                    'Open time', 'Open', 'High', 'Low', 'Close', 'Volume',
                    'Close time', 'Quote asset volume', 'Number of trades',
                    'Taker buy base asset volume', 'Taker buy quote asset volume', 'Ignore'
                ])
            
            # 合并本地数据和API数据
            if not local_klines.empty:
                # 确保本地数据的时间也是东八区
                if 'Open time' in local_klines.columns:
                    local_klines['Open time'] = pd.to_datetime(local_klines['Open time'])
                
                # 合并API数据和本地数据
                if not api_df.empty:
                    # 将API数据添加到本地数据中，确保不重复
                    completed_klines = pd.concat([local_klines, api_df.iloc[:-1]])
                    all_klines = pd.concat([local_klines, api_df])
                    # 删除重复项
                    completed_klines = completed_klines.drop_duplicates(subset=['Open time'], keep='last')
                    all_klines = all_klines.drop_duplicates(subset=['Open time'], keep='last')
                    # 按时间排序
                    completed_klines = completed_klines.sort_values(by='Open time')
                    all_klines = all_klines.sort_values(by='Open time')
                else:
                    completed_klines = local_klines
                    all_klines = local_klines
            else:
                if not api_df.empty:
                    completed_klines = api_df.iloc[:-1]
                    all_klines = api_df
                else:
                    # 使用辅助函数创建空DataFrame
                    completed_klines = create_empty_klines_df()
                    all_klines = create_empty_klines_df()

            # 将updatedy_klines保存到本地
            self.save_klines_to_local(completed_klines, symbol, interval)
          
            # 检查需要显示的K线数量，确保第一根K线落在整数时间上
            if len(completed_klines) > MAX_DISPLAY_KLINES:
                # 初始显示最近的MAX_DISPLAY_KLINES条
                self.display_klines = completed_klines.tail(MAX_DISPLAY_KLINES)
                self.klines_df = all_klines.tail(MAX_DISPLAY_KLINES+1)
                
                # 检查第一根K线是否在整数时间上
                first_kline_time = self.display_klines.iloc[0]['Open time']
                
                # 定义需要增加的K线数量
                additional_klines = 0
                
                # 根据不同周期计算需要增加的K线数量，使第一根K线落在整数时间上
                if interval.endswith('m'):
                    # 分钟级别，计算到整点或半小时的距离
                    minutes = int(interval[:-1])
                    # 计算到前一个整点或半小时的分钟数
                    if first_kline_time.minute % 30 > 0:
                        # 分钟不在整点或半小时上
                        minutes_diff = first_kline_time.minute % 30
                        # 计算需要额外加载的K线数量
                        additional_klines = minutes_diff // minutes
                        if first_kline_time.second > 0:
                            additional_klines += 1  # 如果秒数不是0，需要再加一根
                
                elif interval.endswith('h'):
                    # 小时级别，计算到整点小时的距离
                    hours = int(interval[:-1])
                    if first_kline_time.minute > 0 or first_kline_time.second > 0:
                        # 计算需要额外加载的K线数量，使其落在整点小时
                        minutes_diff = first_kline_time.minute
                        additional_klines = 1  # 至少需要多一根
                
                elif interval == '1d':
                    # 日线，计算到0点的距离
                    if first_kline_time.hour > 0 or first_kline_time.minute > 0 or first_kline_time.second > 0:
                        # 如果不在0点，需要增加K线
                        additional_klines = 1  # 至少需要多一根
                
                elif interval == '1w':
                    # 周线，计算到周一0点的距离
                    if first_kline_time.weekday() != 0:
                        # 如果不在周一0点，需要增加K线
                        additional_klines = 1  # 至少需要多一根
                
                else:
                    # 其他周期默认计算到整15分钟的距离
                    if first_kline_time.minute % 15 > 0 or first_kline_time.second > 0:
                        # 计算到前一个15分钟整点的距离
                        minutes_diff = first_kline_time.minute % 15
                        # 假设为15分钟周期
                        additional_klines = (minutes_diff + 14) // 15  # 向上取整
                
                # 如果需要增加K线数量，并且有足够的历史数据
                if additional_klines > 0 and len(completed_klines) >= MAX_DISPLAY_KLINES + additional_klines:
                    new_display_count = MAX_DISPLAY_KLINES + additional_klines
                    self.display_klines = completed_klines.tail(new_display_count).reset_index(drop=True)
                    self.klines_df = all_klines.tail(new_display_count+1).reset_index(drop=True)
                    logging.info(f"增加显示K线 {additional_klines} 根，确保第一根K线在整数时间上")
                    logging.info(f"显示最近 {new_display_count} 根K线（总计有 {len(all_klines)} 根K线数据）")
            else:
                self.display_klines = completed_klines
                logging.info(f"显示所有 {len(all_klines)} 根K线数据")        
            
            # 准备绘图用的数据
            df_plot = self.display_klines.copy()
            df_plot.set_index('Open time', inplace=True)
            
            # 设置微软雅黑字体
            font_path = 'C:/Windows/Fonts/msyh.ttc'
            font_prop = fm.FontProperties(fname=font_path)
            
            # 创建K线图
            fig, axes = mpf.plot(
                df_plot,
                type='candle',
                style=mpf.make_mpf_style(
                    base_mpf_style='charles',
                    rc={'font.family': font_prop.get_name()},
                    marketcolors={
                        'candle': {'up': 'g', 'down': 'r'},
                        'edge': {'up': 'g', 'down': 'r'},
                        'wick': {'up': 'g', 'down': 'r'},
                        'ohlc': {'up': 'g', 'down': 'r'},
                        'volume': {'up': '#1f77b4', 'down': '#1f77b4'},
                        'vcedge': {'up': '#1f77b4', 'down': '#1f77b4'},
                        'vcdopcod': False,
                        'alpha': 0.5  # 设置K线透明度为50%
                    }
                ),
                update_width_config=dict(
                    candle_linewidth=0.5,
                    candle_width=0.5
                ),
                volume=False,
                returnfig=True,
                figsize=(10, 6) # 设置图表大小
            )
            self.fig = fig
            self.ax_main = axes[0]  # 主图轴
            
            # 清理旧图
            if hasattr(self, 'canvas'):
                self.canvas.get_tk_widget().destroy()
                
            # 将图表放在左侧frame中
            self.canvas = FigureCanvasTkAgg(fig, master=self.chart_frame)
            self.canvas.get_tk_widget().pack(fill=BOTH, expand=True)
            
            # 调整初始视图范围，只显示最近的100根K线
            total_bars = len(df_plot)
            if total_bars > INITIAL_VIEW_KLINES:
                # 获取当前x轴范围
                xlim = self.ax_main.get_xlim()
                # 计算每根K线所占的x轴距离
                bar_width = (xlim[1] - xlim[0]) / total_bars
                # 设置新的x轴范围，只显示最后100根K线
                start_idx = total_bars - INITIAL_VIEW_KLINES
                new_xlim = (start_idx * bar_width, xlim[1])
                self.ax_main.set_xlim(new_xlim)
                
                # 同时调整y轴范围，只考虑最近的100根K线
                last_100_bars = df_plot.iloc[-INITIAL_VIEW_KLINES:]
                y_min = last_100_bars['Low'].min() * 0.998  # 留出一点下方空间
                y_max = last_100_bars['High'].max() * 1.002  # 留出一点上方空间
                self.ax_main.set_ylim(y_min, y_max)
                
                logging.info(f"调整初始视图范围为最近 {INITIAL_VIEW_KLINES} 根K线（显示索引 {start_idx}-{total_bars-1}）")
                logging.info(f"调整y轴范围为 {y_min:.2f} - {y_max:.2f}，基于最近100根K线")
            
            # 添加工具栏
            from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
            
            # 创建自定义工具栏类，移除不需要的按钮，并自定义home按钮功能
            class CustomNavigationToolbar(NavigationToolbar2Tk):
                # 创建没有configure subplots和拖动按钮的工具栏
                toolitems = [t for t in NavigationToolbar2Tk.toolitems if t[0] not in ('Subplots', 'Pan')]
                
                def __init__(self, canvas, parent, kline_page=None):
                    self.kline_page = kline_page
                    self.zoom_active = False  # 跟踪放大镜工具状态
                    super().__init__(canvas, parent)
                    
                    # 移除默认的坐标显示标签
                    self._remove_message_label()
                    
                    # 创建自定义状态栏和时间标签
                    self.create_custom_status_widgets()
                
                def _remove_message_label(self):
                    """移除默认的坐标显示标签"""
                    for child in self.pack_slaves():
                        if isinstance(child, Label):
                            if hasattr(child, 'message') or child.winfo_name() == "Message":
                                child.pack_forget()
                
                def create_custom_status_widgets(self):
                    """创建自定义状态栏和时间标签"""
                    # 创建框架容器
                    self.status_frame = Frame(self)
                    self.status_frame.pack(side=RIGHT, fill=X, expand=True)
                    
                    # 创建状态栏（显示坐标信息）
                    self.status_bar = Label(self.status_frame, text="准备就绪", bd=1, relief=SUNKEN, anchor=W)
                    self.status_bar.pack(side=LEFT, fill=X, expand=True)
                    
                    # 创建时间标签
                    self.time_label = Label(self.status_frame, text="", bd=1, relief=SUNKEN, anchor=E, width=20)
                    self.time_label.pack(side=RIGHT)
                    
                    # 启动时间更新线程
                    if self.kline_page:
                        start_time_update(self.time_label)
                
                def set_message(self, message):
                    """重写设置消息方法，将消息显示到我们的状态栏"""
                    if hasattr(self, 'status_bar'):
                        self.status_bar.config(text=message)
                
                # 重写home按钮的功能，使用reset_view代替
                def home(self, *args):
                    """重置视图到初始状态（复位视图）"""
                    if self.kline_page and hasattr(self.kline_page, 'reset_view'):
                        self.kline_page.reset_view()
                    else:
                        # 如果无法访问reset_view，退回到原始的home功能
                        super().home(*args)
                
                # 重写zoom按钮的功能，增加对放大镜活动状态的跟踪
                def zoom(self, *args):
                    """切换放大镜工具"""
                    super().zoom(*args)
                    self.zoom_active = not self.zoom_active
                    # 更新状态栏
                    if self.zoom_active:
                        self.status_bar.config(text="放大镜工具已激活 - 左键拖动选择区域进行放大")
                    else:
                        self.status_bar.config(text="准备就绪")
                        # 更新价格范围标签
                        if self.kline_page and hasattr(self.kline_page, 'update_range_labels'):
                            self.kline_page.update_range_labels()
            
            if hasattr(self, 'toolbar'):
                self.toolbar.destroy()
            self.toolbar = CustomNavigationToolbar(self.canvas, self.chart_frame, self)
            self.toolbar.update()
            
            # 设置交互
            self.setup_interactions()

            # 获取当前交易对的价格精度
            config = self.symbol_config_manager.get_symbol_config(symbol)
            price_precision = config.get('price_precision', 2)
            # 设置价格精度格式化Y轴
            def price_formatter(x, pos):
                return f'{x:,.{price_precision}f}'
            self.ax_main.yaxis.set_major_formatter(FuncFormatter(price_formatter))
                        
            # 设置x轴日期格式 - 使用自定义格式化器
            def x_date_formatter(x, pos):
                try:
                    # 将x坐标（索引）转换为整数
                    x_index = int(round(x))
                    
                    # 确保索引在有效范围内
                    if x_index < 0 or x_index >= len(self.klines_df):
                        return ""
                    
                    # 获取第一根K线的时间
                    first_kline_time = self.display_klines.iloc[0]['Open time']
                    interval = self.interval_var.get()
                    
                    # 根据K线周期和索引计算对应的K线时间
                    if interval.endswith('m'):  # 分钟K线
                        minutes = int(interval[:-1])
                        kline_time = first_kline_time + pd.Timedelta(minutes=minutes * x_index)
                    elif interval.endswith('h'):  # 小时K线
                        hours = int(interval[:-1])
                        kline_time = first_kline_time + pd.Timedelta(hours=hours * x_index)
                    elif interval == '1d':  # 日线
                        kline_time = first_kline_time + pd.Timedelta(days=x_index)
                    elif interval == '1w':  # 周线
                        kline_time = first_kline_time + pd.Timedelta(weeks=x_index)
                    else:  # 默认15分钟
                        kline_time = first_kline_time + pd.Timedelta(minutes=15 * x_index)
                    
                    # 格式化为字符串
                    return kline_time.strftime('%Y-%m-%d %H:%M')
                except:
                    return ""

            self.ax_main.xaxis.set_major_formatter(FuncFormatter(x_date_formatter))
            
            # 设置x轴刻度定位器，只在整数索引位置显示刻度
            self.ax_main.xaxis.set_major_locator(MaxNLocator(integer=True, nbins=5))
            
            # 确保网格线也只在整数位置显示
            self.ax_main.grid(True, axis='x', which='major')
            self.ax_main.grid(False, axis='x', which='minor')  # 关闭次要网格线

            # 设置x轴和y轴标签字体样式
            for label in self.ax_main.get_xticklabels():
                label.set_fontproperties(font_prop)
                label.set_fontsize(8)
                label.set_color('gray')
                label.set_rotation(0)  # 设置为0度，使标签水平显示
                
            for label in self.ax_main.get_yticklabels():
                label.set_fontproperties(font_prop)
                label.set_fontsize(8)
                label.set_color('gray')
                
            # 删除y轴标题
            # self.ax_main.set_xlabel('')
            self.ax_main.set_ylabel('')

            # 强制更新一次K线
            self.update_kline_data(force_refresh=True)
            
        except Exception as e:
            logging.error(f"加载K线数据失败: {str(e)}")
            import traceback
            traceback.print_exc()  # 打印详细错误信息

    def setup_interactions(self):
        """设置图表交互功能"""
        # 记录初始状态便于重置
        self.original_xlim = self.ax_main.get_xlim()
        self.original_ylim = self.ax_main.get_ylim()
        
        # 设置默认光标为十字星
        if hasattr(self, 'canvas'):
            self.canvas.get_tk_widget().config(cursor="crosshair")
        
        # 保存当前平移状态
        self.pan_active = False
        self.pan_start_x = None
        self.pan_start_y = None
        
        # 保存当前尺度调整状态
        self.scale_active = False
        self.scale_start_x = None
        self.scale_start_y = None
        self.last_pixel_dx = 0
        self.last_pixel_dy = 0
        
        # 添加用户交互状态跟踪
        self.user_interacting = False
        self.last_refresh_time = time.time()
        
        # 绑定窗口交互事件 - 简化实现
        self.root = self.winfo_toplevel()
        # 检测窗口拖动/调整大小开始（鼠标按下时）
        self.root.bind("<ButtonPress-1>", self.on_window_interaction_start)
        # 检测窗口拖动/调整大小结束（鼠标松开时）
        self.root.bind("<ButtonRelease-1>", self.on_window_interaction_end)
        
        # 绑定鼠标事件
        self.cid_press = self.canvas.mpl_connect('button_press_event', self.on_mouse_press)
        self.cid_motion = self.canvas.mpl_connect('motion_notify_event', self.on_mouse_move)
        self.cid_scroll = self.canvas.mpl_connect('scroll_event', self.on_scroll)
        self.cid_key_press = self.canvas.mpl_connect('key_press_event', self.on_key_press)
        self.cid_key_release = self.canvas.mpl_connect('key_release_event', self.on_key_release)
        
        # 绑定鼠标释放事件到根窗口，以便捕获图表外的鼠标释放
        self.root.bind("<ButtonRelease-1>", self.on_root_mouse_release)
        self.root.bind("<ButtonRelease-3>", self.on_root_mouse_release)
        
        # 添加状态变量
        self.ctrl_pressed = False
        self.alt_pressed = False
        
    def on_window_interaction_start(self, event):
        """当用户开始拖动窗口或调整窗口大小时"""
        # 检查鼠标是否在窗口边缘或标题栏
        # 这里不需要复杂的计算，直接检查鼠标当前位置是否在Canvas小部件内
        if not self.canvas.get_tk_widget().winfo_containing(event.x_root, event.y_root):
            # 如果鼠标不在Canvas内，说明可能在拖动窗口或调整窗口大小
            self.user_interacting = True
            print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}, 检测到窗口拖动/调整大小，暂停刷新")
    
    def on_window_interaction_end(self, event):
        """当用户结束拖动窗口或调整窗口大小时"""
        # 鼠标释放时，延迟一小段时间后设置交互状态为False
        self.after(200, self._end_user_interaction)
        
    def _end_user_interaction(self):
        """结束用户交互状态"""
        self.user_interacting = False

    def on_mouse_press(self, event):
        """处理鼠标按下事件"""
        # 标记用户正在交互
        self.user_interacting = True
        
        # 检查放大镜工具是否激活
        if hasattr(self, 'toolbar') and hasattr(self.toolbar, 'zoom_active') and self.toolbar.zoom_active:
            # 放大镜工具激活时，不执行自定义的拖拽功能
            return
        
        if event.button == 1 and event.inaxes == self.ax_main:  # 左键且在主图轴内
            # 开始平移
            self.pan_active = True
            self.pan_start_x = event.xdata
            self.pan_start_y = event.ydata
            self.canvas.get_tk_widget().config(cursor="fleur")  # 更改光标为手型
        elif event.button == 3 and event.inaxes == self.ax_main:  # 右键且在主图轴内
            # 开始调整尺度
            self.scale_active = True
            # 记录鼠标在屏幕上的位置（像素坐标）
            self.scale_start_x = event.x
            self.scale_start_y = event.y
            # 记录当前图表范围作为初始状态
            self.scale_xlim = self.ax_main.get_xlim()
            self.scale_ylim = self.ax_main.get_ylim()
            # 重置上次像素偏移量
            self.last_pixel_dx = 0
            self.last_pixel_dy = 0
            self.canvas.get_tk_widget().config(cursor="sizing")  # 更改光标为调整尺寸
            
    def on_root_mouse_release(self, event):
        """处理鼠标释放事件（全窗口范围）"""
        if event.num == 1:  # 左键
            # 结束平移
            self.pan_active = False
            self.canvas.get_tk_widget().config(cursor="crosshair")  # 恢复为十字星光标
        elif event.num == 3:  # 右键
            # 结束调整尺度
            self.scale_active = False
            self.canvas.get_tk_widget().config(cursor="crosshair")  # 恢复为十字星光标
        # 给一点延迟再标记用户交互结束，避免立即刷新
        self.user_interacting = False

    def handle_kline_interval_input_finished(self, event=None):
        """处理K线刷新间隔输入框的输入完成事件 (回车或失焦)"""
        try:
            current_input = self.kline_refresh_interval_var.get()
            new_interval_s = float(current_input)

            if new_interval_s < 0.5:
                messagebox.showinfo("提示", "K线刷新间隔不能小于0.5秒，已自动调整为0.5秒")
                new_interval_s = 0.5
            
            self.active_kline_refresh_s = new_interval_s
            self.kline_refresh_interval_var.set(str(self.active_kline_refresh_s))  # 更新输入框为有效或修正后的值

        except ValueError:
            messagebox.showerror("输入错误", "请输入有效的K线刷新间隔时间（例如 1 或 0.5）。")
            # 输入无效，将输入框恢复为上一个有效的激活间隔
            self.kline_refresh_interval_var.set(str(self.active_kline_refresh_s))
        
        # 如果K线自动刷新当前是开启的，则应用新的间隔并重新调度
        if self.auto_refresh_var.get() == 1:
            self._start_or_reschedule_kline_refresh()

    def _start_or_reschedule_kline_refresh(self):
        """使用 self.active_kline_refresh_s 调度或重新调度K线刷新循环"""
        try:
            # 取消已存在的K线刷新任务
            if hasattr(self, 'kline_refresh_job'):
                self.after_cancel(self.kline_refresh_job)
            
            kline_interval_ms = int(self.active_kline_refresh_s * 1000)
            self.kline_refresh_job = self.after(kline_interval_ms, self.auto_refresh_kline_data)
            return True
        except Exception as e:
            logging.error(f"安排K线刷新任务失败: {str(e)}")
            return False

    def toggle_auto_refresh(self):
        """切换自动刷新状态"""
        # 价格线和倒计时刷新始终以0.1秒间隔运行
        # 停止之前的价格刷新任务（如果有）
        if hasattr(self, 'price_refresh_job'):
            self.after_cancel(self.price_refresh_job)
            
        price_interval = 0.2  # 价格线刷新固定间隔 (秒) - 根据您的最新更改
        price_interval_ms = int(price_interval * 1000)
        self.price_refresh_job = self.after(price_interval_ms, self.auto_refresh_price_line)
        
        # K线数据刷新控制（根据复选框状态）
        if self.auto_refresh_var.get() == 1:
            # 启动K线数据自动刷新
            try:
                # 使用当前有效的刷新间隔调度K线刷新
                self._start_or_reschedule_kline_refresh()
            except Exception as e:
                logging.error(f"启动K线刷新失败: {str(e)}")
                self.auto_refresh_var.set(0)  # 出错时取消选中复选框
        else:
            # 停止K线数据自动刷新
            if hasattr(self, 'kline_refresh_job'):
                self.after_cancel(self.kline_refresh_job)

    def auto_refresh_price_line(self):
        # 自动刷新价格线和相关UI元素 (此任务始终运行)
        try:
            next_schedule_delay_s = 0.2 # 默认的下一次调度延迟 (秒) - 根据您的最新更改

            if self.user_interacting:
                logging.debug("用户正在与图表交互，价格线刷新将以更快频率重新调度，但不立即更新UI")
                next_schedule_delay_s = 0.2 # 用户交互时，设置更快的下一次调度延迟 (秒)
                # 注意：根据原始逻辑，此处不调用 update_price_line() 或 draw_idle()，
                # 因为用户正在交互，UI更新应由其他事件（如鼠标移动）处理或延迟处理。
                self.update_price_line() # 更新价格线数据和标签
                self.canvas.draw_idle()  # 重绘图表以显示更新
                # self.canvas.get_tk_widget().config(cursor="crosshair") # 保持光标样式
            else:
                # 仅在用户非交互时，执行价格线和UI的更新
                self.update_price_line() # 更新价格线数据和标签
                self.canvas.draw_idle()  # 重绘图表以显示更新
                self.canvas.get_tk_widget().config(cursor="crosshair") # 保持光标样式
            
            # 重新调度下一次价格线刷新
            price_interval_ms = int(next_schedule_delay_s * 1000)
            
            # 在设置新的任务前，取消任何已存在的旧价格线刷新任务
            if hasattr(self, 'price_refresh_job'):
                self.after_cancel(self.price_refresh_job)
            self.price_refresh_job = self.after(price_interval_ms, self.auto_refresh_price_line)

        except Exception as e:
            logging.error(f"价格线自动刷新失败: {str(e)}")
            # 尝试重新调度下一次刷新，确保刷新循环不会中断
            self.after(1000, self.auto_refresh_price_line)

    def auto_refresh_kline_data(self):
        # 自动刷新K线数据 (仅当复选框选中时)
        try:
            if self.user_interacting:
                logging.debug("用户正在与图表交互，K线数据刷新将尝试重新调度（如果已启用）")
                if self.auto_refresh_var.get() == 1: # 仅当K线自动刷新启用时才重新调度
                    self._start_or_reschedule_kline_refresh() # 尝试重新调度
                return # 用户交互时，不立即获取和更新K线数据，等待下一次调度

            # 用户非交互时，执行K线数据更新
            # self.update_kline_data() 方法负责获取新数据、处理并可能重绘图表
            updated = self.update_kline_data() 

            # 如果K线自动刷新已启用，则安排下一次刷新
            if self.auto_refresh_var.get() == 1:
                self._start_or_reschedule_kline_refresh() # 处理下一次K线数据刷新调度
        except Exception as e:
            logging.error(f"K线数据自动刷新失败: {str(e)}")
            # 此处可以考虑添加更稳健的错误处理

    def update_price_line(self):
        """更新最新价格线和标签"""
        try:
            # 确保有K线数据和图表
            if not hasattr(self, 'klines_df') or self.klines_df.empty or not hasattr(self, 'ax_main'):
                return
                
            # 获取最新价格
            latest_price = float(self.klines_df.iloc[-1]['Close'])
            
            # 获取当前交易对的价格精度
            symbol = self.current_valid_symbol
            config = self.symbol_config_manager.get_symbol_config(symbol)
            price_precision = config.get('price_precision', 2)
            
            # 删除旧的价格线和标签
            if hasattr(self, 'price_line') and self.price_line:
                self.price_line.remove()
                self.price_line = None
            
            if hasattr(self, 'price_label') and self.price_label:
                self.price_label.remove()
                self.price_label = None
            
            # 创建新的价格线（虚线），使用灰色并减小线宽
            self.price_line = self.ax_main.axhline(y=latest_price, color='gray', linestyle='--', linewidth=0.5)
            
            # 获取微软雅黑字体
            font_path = 'C:/Windows/Fonts/msyh.ttc'
            if not os.path.exists(font_path):
                font_path = 'C:/Windows/Fonts/simsun.ttc'  # 宋体
            
            font_prop = fm.FontProperties(fname=font_path)
            
            # 生成价格标签文本
            price_text = self.symbol_config_manager.format_price(symbol, latest_price)
            
            # 获取图表的x轴范围，确保标签在最右侧
            xmax = 1.049  # 使用相对坐标，1.0表示最右侧
            
            # 创建包含价格和倒计时的组合标签       
            self.price_label = self.ax_main.text(
                xmax, latest_price,  # 放在最右侧
                f"{price_text}\n--:--",  # 初始文本，第一行是价格，第二行是倒计时
                color='white',  # 标签文字改为白色
                fontproperties=font_prop,
                fontsize=8,  # 与最大最小值标签字号一致
                va='center',  # 垂直居中对齐
                ha='center',  # 水平居中对齐
                transform=self.ax_main.get_yaxis_transform(),  # 使用y轴坐标系统
                bbox=dict(boxstyle='round', fc='gray', ec='white', alpha=0.7, pad=0.5),  # 增加内边距使文字更居中
                linespacing=1.2  # 行间距
            )

            # 获取当前K线间隔
            interval = self.interval_var.get()

            # 将价格标签同时作为倒计时标签
            self.countdown_label = self.price_label
            time_manager.add_countdown_label(self.countdown_label, interval)
            label_text = self.countdown_label.get_text() if hasattr(self.countdown_label, 'get_text') else ""
            countdown_part = label_text.split('\n')[1] if '\n' in label_text else label_text
            # 如果倒计时为00:00或00:00:00，且未开启自动刷新，则强制更新K线
            if (countdown_part == "00:00" or countdown_part == "00:00:00") and \
                hasattr(self, 'auto_refresh_var') and not self.auto_refresh_var.get():
                # 延迟1秒后刷新K线
                self.after(2000, lambda: self.update_kline_data(force_refresh=True))
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f'{current_time}, 倒计时归零，更新K线数据')
                return  # 已经安排了延迟刷新K线，不需要继续定时器
            
            # 刷新画布
            # self.canvas.draw_idle()
            # # 恢复十字星光标，防止更新价格线后光标变成默认箭头
            # self.canvas.get_tk_widget().config(cursor="crosshair")
            # current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            # print(f'{current_time}, 更新价格线和倒计时')
            
        except Exception as e:
            logging.error(f"更新价格线失败: {str(e)}")
            traceback.print_exc()
 
    def update_kline_data(self, force_refresh=False):
        """只更新新的K线数据，不重新生成整个图表"""
        try:
            # 获取标签和时间周期
            symbol = self.current_valid_symbol
            interval = self.interval_var.get()
            
            # 检查是否已经初始化了图表
            if not hasattr(self, 'fig') or not hasattr(self, 'ax_main') or not hasattr(self, 'klines_df'):
                # 如果图表还没有初始化，那么调用完整的加载方法
                self.load_kline_data()
                return True

            # 从API获取最新的K线数据
            api_klines = self.client.klines(
                symbol=symbol,
                interval=interval,
                limit=10  # 只获取最近的几条记录
            )
            
            # 将API返回的K线数据转换为DataFrame
            api_df = pd.DataFrame(api_klines, columns=[
                'Open time', 'Open', 'High', 'Low', 'Close', 'Volume',
                'Close time', 'Quote asset volume', 'Number of trades',
                'Taker buy base asset volume', 'Taker buy quote asset volume', 'Ignore'
            ])
            
            # 转换数据类型
            api_df['Open time'] = pd.to_datetime(api_df['Open time'], unit='ms') + pd.Timedelta(hours=8)
            api_df['Close time'] = pd.to_datetime(api_df['Close time'], unit='ms') + pd.Timedelta(hours=8)
            
            api_df = api_df.astype({
                'Open': float, 'High': float, 'Low': float,
                'Close': float, 'Volume': float
            })

            
            # 不筛选，直接使用获取的api K线列表
            # 标记是否有数据更新
            data_updated = False
            
            # 如果成功获取K线数据
            if not api_df.empty:
                # 获取当前图表上最新K线的时间
                if not self.klines_df.empty:
                    last_kline_time = self.klines_df['Open time'].max()
                    
                    # 找出比最新K线更新的K线
                    new_klines = api_df[api_df['Open time'] > last_kline_time]
                    
                    # 初始化updated_klines为空DataFrame
                    updated_klines = pd.DataFrame(columns=api_df.columns)
                    current_kline = pd.DataFrame(columns=api_df.columns)
                                        
                    # 如果发现了新K线
                    if not new_klines.empty:                     
                        # 更新上一根K线的状态
                        last_kline = api_df[api_df['Open time'] == last_kline_time]
                        
                        # 这样可以确保它在klines_df中有最新的状态
                        if not last_kline.empty:
                            # 从集合中移除旧版本
                            self.klines_df = self.klines_df[self.klines_df['Open time'] != last_kline_time]
       
                            # 在updated_klines列表中，添加last_kline
                            updated_klines = last_kline.copy()
                        
                        # 排除最新的一根K线（当前正在形成的）
                        new_klines_except_last = new_klines.iloc[:-1]
                        
                        if not new_klines_except_last.empty:
                            # 将new_klines_except_last添加到updated_klines中
                            if updated_klines.empty:
                                updated_klines = new_klines_except_last.copy()
                            else:
                                updated_klines = pd.concat([updated_klines, new_klines_except_last])
                            # 确保没有重复
                            updated_klines = updated_klines.drop_duplicates(subset=['Open time'], keep='last')
                            # 按时间排序
                            updated_klines = updated_klines.sort_values(by='Open time')

                        # 将updated_klines更新到K线集合
                        self.klines_df = pd.concat([self.klines_df, updated_klines])
                        # 确保klines_df没有重复
                        self.klines_df = self.klines_df.drop_duplicates(subset=['Open time'], keep='last')
                        # 按时间排序
                        self.klines_df = self.klines_df.sort_values(by='Open time')
                        # 将updated_klines保存到本地
                        self.save_klines_to_local(updated_klines, symbol, interval)
                        logging.info(f"已保存 {len(updated_klines)} 根已收盘K线，添加新的K线")

                        # 添加当前K线到数据集
                        current_kline = new_klines.iloc[-1:]
                        self.klines_df = pd.concat([self.klines_df, current_kline])
                        # 确保没有重复
                        self.klines_df = self.klines_df.drop_duplicates(subset=['Open time'], keep='last')
                        # 按时间排序
                        self.klines_df = self.klines_df.sort_values(by='Open time')
                        # 重置索引
                        self.klines_df = self.klines_df.reset_index(drop=True)

                        data_updated = True
                
                    else:
                        # 检查当前正在形成的K线（api中的最新一根）
                        current_kline = api_df.iloc[-1:]
                        
                        # 找到需要更新的K线在DataFrame中的索引
                        last_index = self.klines_df['Open time'] == last_kline_time
                        
                        # 检查值是否有变化
                        high_changed = float(current_kline['High'].iloc[0]) > float(self.klines_df.loc[last_index, 'High'].iloc[0])
                        low_changed = float(current_kline['Low'].iloc[0]) < float(self.klines_df.loc[last_index, 'Low'].iloc[0])
                        close_changed = float(current_kline['Close'].iloc[0]) != float(self.klines_df.loc[last_index, 'Close'].iloc[0])
                        
                        # 只在数据有变化时更新
                        if high_changed or low_changed or close_changed:
                            # 更新最高价、最低价和收盘价
                            if high_changed:
                                self.klines_df.loc[last_index, 'High'] = float(current_kline['High'].iloc[0])
                            
                            if low_changed:
                                self.klines_df.loc[last_index, 'Low'] = float(current_kline['Low'].iloc[0])
                            
                            self.klines_df.loc[last_index, 'Close'] = float(current_kline['Close'].iloc[0])
                            self.klines_df.loc[last_index, 'Volume'] = float(current_kline['Volume'].iloc[0])
                            data_updated = True
            # 强制刷新
            if force_refresh:
                data_updated = True

            # 如果有数据更新，重绘图表
            if data_updated:
                # 首先绘制主图上所有K线和指标
                # 然后单独绘制updated_klines
                # 最后单独绘制current_kline
                df_plot = self.klines_df.copy()
                df_plot.set_index('Open time', inplace=True)
                
                # 1. 如果有updated_klines，单独绘制它们
                if not updated_klines.empty:
                    logging.info(f"绘制 {len(updated_klines)} 根更新的K线")
                    
                    # 使用自定义函数绘制updated_klines
                    updated_candles = self.draw_candles(
                        updated_klines,
                        df_plot,
                        self.ax_main,
                        color_up='g',
                        color_down='r',
                        alpha=0.5,
                        linewidth=0.5   # 已收盘K线
                    )
                    # 存储对象引用以便后续可以移除
                    self.updated_candles_objects = updated_candles
                
                # 2. 如果有current_kline，单独绘制它
                if hasattr(self, 'current_kline_objects') and self.current_kline_objects:
                    # 移除旧的current_kline对象
                    for obj in self.current_kline_objects:
                        try:
                            obj.remove()
                        except:
                            pass
                    self.current_kline_objects = []
                
                if not current_kline.empty:
                    # logging.info(f"绘制当前K线")
                    
                    # 使用自定义函数绘制current_kline
                    current_candles = self.draw_candles(
                        current_kline,
                        df_plot,
                        self.ax_main,
                        color_up='g',
                        color_down='r',
                        alpha=0.8,  # 当前K线透明度稍低
                        linewidth=1.0  # 当前K线线宽稍粗
                    )
                    # 存储对象引用以便后续可以移除
                    self.current_kline_objects = current_candles

                # 绘制技术指标
                self.on_indicator_change()
                
                # 更新高低价范围标签
                self.update_range_labels()

                # 更新价格线
                self.update_price_line()
                
                # 刷新画布
                self.canvas.draw_idle()
                # 恢复十字星光标，防止K线更新导致光标变成默认箭头
                self.canvas.get_tk_widget().config(cursor="crosshair")
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f'{current_time}, 更新K线数据和指标')
                
                return True  # 表示已更新
            
            # 如果没有任何更新，返回False
            logging.info("自动刷新：无新数据")
            return False
            
        except Exception as e:
            logging.error(f"更新K线数据失败: {str(e)}")
            import traceback
            traceback.print_exc()  # 打印详细错误信息
            return False

    def on_key_press(self, event):
        """处理键盘按下事件"""
        if event.key == 'control':
            self.ctrl_pressed = True
        elif event.key == 'alt':
            self.alt_pressed = True
            
    def on_key_release(self, event):
        """处理键盘释放事件"""
        if event.key == 'control':
            self.ctrl_pressed = False
        elif event.key == 'alt':
            self.alt_pressed = False
            
    def on_mouse_move(self, event):
        """处理鼠标移动事件"""
        # 更新状态栏显示坐标
        if event.inaxes == self.ax_main and hasattr(self, 'display_klines') and not self.display_klines.empty:
            try:
                # 获取鼠标位置的x坐标，四舍五入到最近的整数索引
                x_index = round(event.xdata)
                
                # 获取第一根K线的时间
                first_kline_time = self.display_klines.iloc[0]['Open time']
                interval = self.interval_var.get()
                
                # 根据K线周期和索引计算对应的K线时间
                if interval.endswith('m'):  # 分钟K线
                    minutes = int(interval[:-1])
                    kline_time = first_kline_time + pd.Timedelta(minutes=minutes * x_index)
                elif interval.endswith('h'):  # 小时K线
                    hours = int(interval[:-1])
                    kline_time = first_kline_time + pd.Timedelta(hours=hours * x_index)
                elif interval == '1d':  # 日线
                    kline_time = first_kline_time + pd.Timedelta(days=x_index)
                elif interval == '1w':  # 周线
                    kline_time = first_kline_time + pd.Timedelta(weeks=x_index)
                else:  # 默认15分钟
                    kline_time = first_kline_time + pd.Timedelta(minutes=15 * x_index)
                
                # 格式化为字符串
                date_str = kline_time.strftime('%Y-%m-%d %H:%M')
                
                # 获取价格
                price = event.ydata
                
                # 更新状态栏
                if hasattr(self, 'toolbar') and hasattr(self.toolbar, 'status_bar'):
                    self.toolbar.status_bar.config(text=f"K线: {date_str} | 价格: {price:.2f} | 索引: {x_index}")
            except Exception as e:
                # 出错时显示原始坐标
                if hasattr(self, 'toolbar') and hasattr(self.toolbar, 'status_bar'):
                    self.toolbar.status_bar.config(text=f"x: {event.xdata:.2f} | 价格: {event.ydata:.2f}")
        
        # 检查放大镜工具是否激活
        if hasattr(self, 'toolbar') and hasattr(self.toolbar, 'zoom_active') and self.toolbar.zoom_active:
            # 放大镜工具激活时，不执行自定义的拖拽功能
            return
        
        # 处理左键平移
        if self.pan_active and event.xdata is not None and event.ydata is not None and event.inaxes == self.ax_main:
            # 计算平移量
            dx = self.pan_start_x - event.xdata
            dy = self.pan_start_y - event.ydata
            
            # 获取当前轴范围
            x_min, x_max = self.ax_main.get_xlim()
            y_min, y_max = self.ax_main.get_ylim()
            
            # 平移
            self.ax_main.set_xlim(x_min + dx, x_max + dx)
            self.ax_main.set_ylim(y_min + dy, y_max + dy)
            
            # 更新图表
            self.canvas.draw_idle()
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f'{current_time}, 平移中')
            
            # 更新最高价和最低价标签
            self.update_range_labels()
            
        # 处理右键缩放 - 即使鼠标移出图表区域也继续工作
        elif self.scale_active:
            # 计算鼠标在屏幕上的移动距离（像素）
            if hasattr(event, 'x') and hasattr(event, 'y'):
                pixel_dx = event.x - self.scale_start_x
                pixel_dy = event.y - self.scale_start_y
                # 保存最后的像素偏移量
                self.last_pixel_dx = pixel_dx
                self.last_pixel_dy = pixel_dy
            else:
                # 没有像素坐标时，使用最后记录的值
                pixel_dx = self.last_pixel_dx
                pixel_dy = self.last_pixel_dy
            
            # 获取右键按下时保存的初始坐标范围
            x_min, x_max = self.scale_xlim
            y_min, y_max = self.scale_ylim
            
            # 计算初始坐标范围
            x_range = x_max - x_min
            y_range = y_max - y_min
            
            # 设定缩放系数 - 拖拽像素与缩放比例的转换
            x_pixels_for_half = 100  # 需要拖动多少像素才能让范围减半
            y_pixels_for_half = 100
            
            # 计算缩放比例（使用指数函数，实现线性感知的缩放）
            x_scale = 2 ** (-pixel_dx / x_pixels_for_half)  # 右拖100像素，范围变为原来的1/2
            y_scale = 2 ** (-pixel_dy / y_pixels_for_half)  # 下拖100像素，范围变为原来的1/2
            
            # 计算缩放中心点
            x_center = (x_min + x_max) / 2
            y_center = (y_min + y_max) / 2
            
            # 保持中心点不变，调整坐标范围
            new_x_range = x_range * x_scale
            new_y_range = y_range * y_scale
            
            # 确保范围为正值，避免反转或收缩为零
            min_scale = 0.01  # 允许的最小缩放比例，防止范围太小
            new_x_range = max(new_x_range, x_range * min_scale)
            new_y_range = max(new_y_range, y_range * min_scale)
            
            # 计算新的坐标边界
            x_min_new = x_center - new_x_range / 2
            x_max_new = x_center + new_x_range / 2
            y_min_new = y_center - new_y_range / 2
            y_max_new = y_center + new_y_range / 2
            
            # 更新图表坐标范围
            self.ax_main.set_xlim(x_min_new, x_max_new)
            self.ax_main.set_ylim(y_min_new, y_max_new)
            
            # 更新状态栏显示当前缩放比例和范围
            # 计算当前视图下可见的K线索引范围
            xlim = self.ax_main.get_xlim()
            ylim = self.ax_main.get_ylim()
            
            # 修改可见K线的计算逻辑
            # 只有当K线的中心点（整数索引）在视图范围内时才计算为可见
            # 向上取整，确保只有当K线中心点在边界内才算该K线可见
            start_index = math.ceil(xlim[0])
            # 向下取整，确保只有当K线中心点在边界内才算该K线可见
            end_index = math.floor(xlim[1])
            
            # 确保索引在有效范围内
            total_bars = len(self.klines_df) if hasattr(self, 'klines_df') else 0
            start_index = max(0, start_index)
            end_index = min(total_bars - 1, end_index) if total_bars > 0 else 0
            
            # 获取可见范围内的价格和时间
            if hasattr(self, 'klines_df') and not self.klines_df.empty and start_index <= end_index and end_index < total_bars:
                visible_bars = self.klines_df.iloc[start_index:end_index+1]
                if not visible_bars.empty:
                    # 使用正确的列名
                    first_time = visible_bars.iloc[0]['Open time'].strftime('%Y-%m-%d %H:%M') if 'Open time' in visible_bars.columns else "N/A"
                    last_time = visible_bars.iloc[-1]['Open time'].strftime('%Y-%m-%d %H:%M') if 'Open time' in visible_bars.columns else "N/A"
                    min_price = visible_bars['Low'].min() if 'Low' in visible_bars.columns else 0
                    max_price = visible_bars['High'].max() if 'High' in visible_bars.columns else 0
                    
                    # 更新状态栏信息
                    scale_info = f"缩放比例: X: {1/x_scale:.2f}x, Y: {1/y_scale:.2f}x | 时间: {first_time} - {last_time} | 价格: {min_price} - {max_price}"
                else:
                    scale_info = f"缩放比例: X: {1/x_scale:.2f}x, Y: {1/y_scale:.2f}x | 无可见数据"
            else:
                scale_info = f"缩放比例: X: {1/x_scale:.2f}x, Y: {1/y_scale:.2f}x | 范围: X: {new_x_range:.2f}, Y: {new_y_range:.2f}"
            
            if hasattr(self, 'toolbar') and hasattr(self.toolbar, 'status_bar'):
                self.toolbar.status_bar.config(text=scale_info)
            
            # 更新最高价和最低价标签
            self.update_range_labels()
            
            # 刷新Canvas
            self.canvas.draw_idle()
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f'{current_time}, 缩放中')

    def on_scroll(self, event):
        """处理鼠标滚轮事件"""
        # 标记用户正在交互
        self.user_interacting = True
        
        if event.inaxes != self.ax_main:
            # 设置一个短暂的延时后将交互状态设为False
            self.after(500, self._end_user_interaction)
            return
        
        # 获取当前的x和y范围
        xlim = self.ax_main.get_xlim()
        ylim = self.ax_main.get_ylim()
        
        # 计算鼠标位置
        x_center = event.xdata
        y_center = event.ydata
        
        # 计算当前视图范围的宽度和高度
        x_width = xlim[1] - xlim[0]
        y_height = ylim[1] - ylim[0]
        
        # 缩放因子
        scale_factor = 1.2
        
        # 根据滚轮方向确定是放大还是缩小
        if event.button == 'up':  # 放大
            # 计算新的x轴范围
            new_x_width = x_width / scale_factor
            # 确保放大时保持鼠标位置不变
            new_xlim = (
                x_center - new_x_width * (x_center - xlim[0]) / x_width,
                x_center + new_x_width * (xlim[1] - x_center) / x_width
            )
            
            # 计算新的y轴范围
            new_y_height = y_height / scale_factor
            # 确保放大时保持鼠标位置不变
            new_ylim = (
                y_center - new_y_height * (y_center - ylim[0]) / y_height,
                y_center + new_y_height * (ylim[1] - y_center) / y_height
            )
        else:  # 缩小
            # 计算新的x轴范围
            new_x_width = x_width * scale_factor
            # 确保缩小时保持鼠标位置不变
            new_xlim = (
                x_center - new_x_width * (x_center - xlim[0]) / x_width,
                x_center + new_x_width * (xlim[1] - x_center) / x_width
            )
            
            # 计算新的y轴范围
            new_y_height = y_height * scale_factor
            # 确保缩小时保持鼠标位置不变
            new_ylim = (
                y_center - new_y_height * (y_center - ylim[0]) / y_height,
                y_center + new_y_height * (ylim[1] - y_center) / y_height
            )
            
        # 更新图表范围
        self.ax_main.set_xlim(new_xlim)
        self.ax_main.set_ylim(new_ylim)
        
        # 刷新Canvas
        self.canvas.draw_idle()
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f'{current_time}, 滚轮缩放中')
        
        # 更新价格范围标签
        self.update_range_labels()

        # 设置一个短暂的延时后将交互状态设为False
        self.after(500, self._end_user_interaction)
        
        # 防止事件传递
        return "break"

    def _end_user_interaction(self):
        """结束用户交互状态"""
        self.user_interacting = False

    def start_balance_update(self):
        self.stop_balance_update()
        self._balance_update_job = self.after(2000, self._balance_update_loop)

    def stop_balance_update(self):
        if hasattr(self, '_balance_update_job') and self._balance_update_job:
            self.after_cancel(self._balance_update_job)
            self._balance_update_job = None

    def _balance_update_loop(self):
        try:
            account_info = self.client.account()
            available_balance = float(account_info.get('availableBalance', 0))
            self.balance_label.config(text=f"{available_balance:.2f} USDT")
        except Exception as e:
            logging.error(f"更新可用资金失败: {str(e)}")
        self._balance_update_job = self.after(2000, self._balance_update_loop)

    def update_margin_leverage_info(self):
        """更新保证金模式和杠杆倍数信息"""
        try:
            symbol = self.current_valid_symbol
            # 从本地配置系统获取信息
            config = self.symbol_config_manager.get_symbol_config(symbol)
            
            # 获取保证金模式
            margin_type = config.get('margin_type', 'CROSSED')
            margin_text = "全仓" if margin_type == "CROSSED" else "逐仓"
            self.margin_button.config(text=f"{margin_text}")
            
            # 获取杠杆倍数
            leverage = config.get('leverage', 1)
            self.leverage_button.config(text=f"{leverage}x")
            
            # 保存当前状态
            self.current_margin_type = margin_type
            self.current_leverage = leverage
            
        except Exception as e:
            logging.error(f"更新保证金和杠杆信息失败: {str(e)}")
            self.margin_button.config(text="获取失败")
            self.leverage_button.config(text="获取失败")

    def change_margin_type(self):
        """切换保证金模式"""
        try:
            symbol = self.current_valid_symbol
            if not hasattr(self, 'current_margin_type'):
                self.update_margin_leverage_info()
            
            # 创建对话框
            dialog = Toplevel(self)
            dialog.title("切换保证金模式")
            dialog.geometry("300x150")
            dialog.transient(self)  # 设置为主窗口的临时窗口
            dialog.grab_set()  # 模态对话框
            
            # 使用Frame包装单选按钮，方便设置背景色
            radio_frame = Frame(dialog, bg='white')
            radio_frame.pack(fill='both', expand=True, padx=20, pady=10)
            
            # 设置当前模式
            mode_var = StringVar(value=self.current_margin_type)
            
            # 创建单选按钮组
            style = ttk.Style()
            style.configure('Selected.TRadiobutton', background='#e6e6e6')
            
            crossed_radio = ttk.Radiobutton(radio_frame, text="全仓", value="CROSSED",
                          variable=mode_var, style='Selected.TRadiobutton' if self.current_margin_type == "CROSSED" else '')
            crossed_radio.pack(pady=5)
            
            isolated_radio = ttk.Radiobutton(radio_frame, text="逐仓", value="ISOLATED",
                          variable=mode_var, style='Selected.TRadiobutton' if self.current_margin_type == "ISOLATED" else '')
            isolated_radio.pack(pady=5)
            
            def apply_change():
                try:
                    new_mode = mode_var.get()
                    if new_mode != self.current_margin_type:  # 只在发生改变时发送请求
                        self.client.change_margin_type(symbol=symbol, marginType=new_mode)
                        messagebox.showinfo("成功", f"已切换到{'全仓' if new_mode == 'CROSSED' else '逐仓'}模式")
                        
                        # 更新配置系统
                        self.symbol_config_manager.update_leverage_margin(symbol, self.current_leverage, new_mode)
                        self.update_margin_leverage_info()  # 更新按钮显示
                    dialog.destroy()
                except Exception as e:
                    messagebox.showerror("错误", str(e))
                    dialog.destroy()
            
            Button(dialog, text="确定", command=apply_change).pack(pady=10)
            
        except Exception as e:
            messagebox.showerror("错误", str(e))

    def change_leverage(self):
        """调整杠杆倍数"""
        try:
            symbol = self.current_valid_symbol
            if not hasattr(self, 'current_leverage'):
                self.update_margin_leverage_info()
            
            # 获取最大杠杆倍数
            config = self.symbol_config_manager.get_symbol_config(symbol)
            max_leverage = config.get('max_leverage')
            
            # 创建对话框
            dialog = Toplevel(self)
            dialog.title("调整杠杆倍数")
            dialog.geometry("300x200")
            dialog.transient(self)  # 设置为主窗口的临时窗口
            dialog.grab_set()  # 模态对话框
            
            # 使用Frame包装内容，设置统一背景色
            content_frame = Frame(dialog, bg='white')
            content_frame.pack(fill='both', expand=True, padx=20, pady=10)
            
            Label(content_frame, text=f"当前杠杆: {self.current_leverage}x", bg='white').pack(pady=5)
            Label(content_frame, text=f"最大杠杆: {max_leverage}x", bg='white').pack(pady=5)
            
            leverage_var = IntVar(value=self.current_leverage)
            scale = ttk.Scale(content_frame, from_=1, to=max_leverage, 
                            variable=leverage_var, orient='horizontal')
            scale.set(self.current_leverage)  # 设置当前值
            scale.pack(fill='x', pady=10)
            
            leverage_label = Label(content_frame, text=f"选择杠杆: {self.current_leverage}x", bg='white')
            leverage_label.pack(pady=5)
            
            def update_label(*args):
                leverage_label.config(text=f"选择杠杆: {leverage_var.get()}x")
            
            leverage_var.trace('w', update_label)
            
            def apply_leverage():
                try:
                    new_leverage = leverage_var.get()
                    if new_leverage != self.current_leverage:  # 只在发生改变时发送请求
                        self.client.change_leverage(
                            symbol=symbol, leverage=new_leverage)
                        messagebox.showinfo("成功", f"杠杆已调整为{new_leverage}x")
                        
                        # 更新配置系统
                        self.symbol_config_manager.update_leverage_margin(symbol, new_leverage, self.current_margin_type)
                        self.update_margin_leverage_info()  # 更新按钮显示
                    dialog.destroy()
                except Exception as e:
                    messagebox.showerror("错误", str(e))
                    dialog.destroy()
            
            Button(dialog, text="确定", command=apply_leverage).pack(pady=10)
            
        except Exception as e:
            messagebox.showerror("错误", str(e))

    def on_unit_change(self):
        """数量单位切换时自动转换数值"""
        try:
            if not self.amount_var.get():
                return
                
            amount = float(self.amount_var.get())
            symbol = self.current_valid_symbol
            
            if self.order_type_var.get() == "LIMIT":
                try:
                    price = float(self.price_var.get())
                except ValueError:
                    return
            else:
                # 使用K线数据获取最新价格
                if hasattr(self, 'klines_df') and self.klines_df is not None and not self.klines_df.empty:
                    price = float(self.klines_df.iloc[-1]['Close'])
                else:
                    ticker = self.client.ticker_price(self.current_valid_symbol)
                    price = float(ticker['price'])
            
            # 转换数量
            if self.amount_unit_var.get() == "USDT":
                # 从币种转换为USDT
                new_amount = amount * price
            else:
                # 从USDT转换为币种
                new_amount = amount / price
            
            # 获取精度
            config = self.symbol_config_manager.get_symbol_config(symbol)
            if self.amount_unit_var.get() == "USDT":
                precision = config.get('price_precision', 2)
            else:
                precision = config.get('quantity_precision', 3)
            
            # 格式化并更新数量
            self.amount_var.set(f"{new_amount:.{precision}f}")
            
            # 重新应用输入验证
            vcmd_amount = (self.register(self.validate_amount_input), '%P')
            self.amount_entry.config(validate="key", validatecommand=vcmd_amount)
            
        except Exception as e:
            logging.error(f"单位转换失败: {str(e)}")

    def on_indicator_change(self, *args):
        """指标变化时的处理，负责更新图表上的技术指标显示"""
        try:
            # 获取当前选择的指标
            indicator = self.indicator_var.get()
            
            # 移除旧的指标线
            if hasattr(self, 'indicator_lines') and self.indicator_lines:
                for line in self.indicator_lines:
                    try:
                        line.remove()
                    except:
                        pass
                self.indicator_lines = []
            
            # 初始化指标线列表
            self.indicator_lines = []

            # 获取绘图数据
            df_plot = self.klines_df.copy()
            df_plot.set_index('Open time', inplace=True)

            # 计算MA指标
            df_plot['MA5'] = df_plot['Close'].rolling(window=5).mean()
            df_plot['MA10'] = df_plot['Close'].rolling(window=10).mean()
            df_plot['MA20'] = df_plot['Close'].rolling(window=20).mean()
                
            # 计算BOLL指标
            df_plot['BOLL_MID'] = df_plot['Close'].rolling(window=20).mean()
            df_plot['BOLL_STD'] = df_plot['Close'].rolling(window=20).std()
            df_plot['BOLL_UPPER'] = df_plot['BOLL_MID'] + 2 * df_plot['BOLL_STD']
            df_plot['BOLL_LOWER'] = df_plot['BOLL_MID'] - 2 * df_plot['BOLL_STD']
            
            # 创建x值数组，应与K线图的x轴对应
            x_values = np.arange(len(df_plot))
            
            # 根据选择的指标绘制相应的线条
            if indicator == "MA":
                # 绘制均线，使用x_values确保与K线对齐
                line1 = self.ax_main.plot(x_values, df_plot['MA5'].values, 'b-', label='MA5', linewidth=1.0, alpha=0.8)[0]
                line2 = self.ax_main.plot(x_values, df_plot['MA10'].values, 'y-', label='MA10', linewidth=1.0, alpha=0.8)[0]
                line3 = self.ax_main.plot(x_values, df_plot['MA20'].values, 'm-', label='MA20', linewidth=1.0, alpha=0.8)[0]
                self.indicator_lines.extend([line1, line2, line3])
  
            elif indicator == "BOLL":
                # 绘制BOLL线，使用x_values确保与K线对齐
                line1 = self.ax_main.plot(x_values, df_plot['BOLL_UPPER'].values, 'r-', label='BOLL上轨', linewidth=1.0, alpha=0.8)[0]
                line2 = self.ax_main.plot(x_values, df_plot['BOLL_MID'].values, 'b-', label='BOLL中轨', linewidth=1.0, alpha=0.8)[0]
                line3 = self.ax_main.plot(x_values, df_plot['BOLL_LOWER'].values, 'g-', label='BOLL下轨', linewidth=1.0, alpha=0.8)[0]
                self.indicator_lines.extend([line1, line2, line3])
            
        except Exception as e:
            logging.error(f"更新指标显示失败: {str(e)}")
            import traceback
            traceback.print_exc()  # 打印详细错误信息

    def on_interval_change(self, *args):
        """切换K线周期"""
        self.load_kline_data()
    
    def reset_view(self):
        """重置视图到初始状态"""
        if hasattr(self, 'ax_main') and hasattr(self, 'fig'):
            # 初始视图中显示的K线数量
            INITIAL_VIEW_KLINES = 100
            
            # 获取当前K线数据的总数
            total_bars = len(self.klines_df) if hasattr(self, 'klines_df') else 0
            
            if total_bars > INITIAL_VIEW_KLINES:
                # 获取完整x轴范围
                xlim = (0, total_bars)
                # 计算每根K线所占的x轴距离
                bar_width = (xlim[1] - xlim[0]) / total_bars
                # 设置新的x轴范围，只显示最后100根K线
                start_idx = total_bars - INITIAL_VIEW_KLINES
                new_xlim = (start_idx * bar_width, xlim[1])
                self.ax_main.set_xlim(new_xlim)
                
                # 调整y轴范围，只考虑最近的100根K线
                df_temp = self.klines_df.copy()
                last_100_bars = df_temp.iloc[-INITIAL_VIEW_KLINES:]
                y_min = last_100_bars['Low'].min() * 0.998  # 留出一点下方空间
                y_max = last_100_bars['High'].max() * 1.002  # 留出一点上方空间
                self.ax_main.set_ylim(y_min, y_max)
                
                logging.info(f"重置视图为最近 {INITIAL_VIEW_KLINES} 根K线（显示索引 {start_idx}-{total_bars-1}）")
                logging.info(f"重置y轴范围为 {y_min:.2f} - {y_max:.2f}，基于最近100根K线")
            else:
                # 如果总数小于或等于初始显示数量，则显示全部
                self.ax_main.autoscale(True)
                self.ax_main.relim()
            
            # 刷新图表
            self.canvas.draw_idle()
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f'{current_time}, 重置图表')
            
            # 更新状态栏
            if hasattr(self, 'toolbar') and hasattr(self.toolbar, 'status_bar'):
                self.toolbar.status_bar.config(text="视图已重置")
            
            # 更新价格线和范围标签
            self.update_price_line()
            self.update_range_labels()

    def display_symbol_config(self, symbol=None):
        """显示当前交易对的配置信息"""
        try:
            if symbol is None:
                symbol = self.current_valid_symbol
            
            # 获取当前交易对的配置
            config = self.symbol_config_manager.get_symbol_config(symbol)
            
            # 创建对话框
            dialog = Toplevel(self)
            dialog.title(f"{symbol} 交易配置")
            dialog.geometry("400x300")
            dialog.transient(self)  # 设置为主窗口的临时窗口
            dialog.grab_set()  # 模态对话框
            
            # 使用Frame包装内容，设置统一背景色
            content_frame = Frame(dialog, bg='white')
            content_frame.pack(fill='both', expand=True, padx=20, pady=10)
            
            # 显示配置信息
            Label(content_frame, text=f"交易对: {symbol}", font=("Arial", 12, "bold"), bg='white').pack(pady=5, anchor='w')
            Label(content_frame, text=f"价格精度: {config.get('price_precision', 'N/A')} 位小数", bg='white').pack(pady=3, anchor='w')
            Label(content_frame, text=f"数量精度: {config.get('quantity_precision', 'N/A')} 位小数", bg='white').pack(pady=3, anchor='w')
            Label(content_frame, text=f"最小下单数量: {config.get('min_quantity', 'N/A')}", bg='white').pack(pady=3, anchor='w')
            Label(content_frame, text=f"当前杠杆: {config.get('leverage', 'N/A')}x", bg='white').pack(pady=3, anchor='w')
            Label(content_frame, text=f"最大杠杆: {config.get('max_leverage', 'N/A')}x", bg='white').pack(pady=3, anchor='w')
            
            margin_type = config.get('margin_type', 'N/A')
            margin_text = "全仓" if margin_type == "CROSSED" else "逐仓" if margin_type == "ISOLATED" else margin_type
            Label(content_frame, text=f"保证金模式: {margin_text}", bg='white').pack(pady=3, anchor='w')
            
            last_updated = config.get('last_updated', 'N/A')
            Label(content_frame, text=f"最后更新时间: {last_updated}", bg='white').pack(pady=5, anchor='w')
            
            # 关闭按钮
            Button(dialog, text="关闭", command=dialog.destroy).pack(pady=10)
            
        except Exception as e:
            logging.error(f"显示交易对配置信息失败: {str(e)}")
            messagebox.showerror("错误", str(e))

    def test_exchange_info(self):
        """测试交易规则获取功能"""
        try:
            symbol = self.current_valid_symbol
            
            # 创建测试对话框
            dialog = Toplevel(self)
            dialog.title(f"测试交易规则获取 - {symbol}")
            dialog.geometry("500x400")
            dialog.transient(self)
            dialog.grab_set()
            
            # 创建文本框以显示结果
            result_frame = Frame(dialog)
            result_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            # 创建可滚动的文本框
            scrollbar = Scrollbar(result_frame)
            scrollbar.pack(side='right', fill='y')
            
            result_text = Text(result_frame, wrap='word', yscrollcommand=scrollbar.set)
            result_text.pack(fill='both', expand=True)
            scrollbar.config(command=result_text.yview)
            
            # 开始测试
            result_text.insert('end', f"开始获取 {symbol} 的交易规则信息...\n\n")
            
            # 直接从API获取原始数据
            raw_info = self.client.exchange_info()
            result_text.insert('end', f"已获取交易所信息，开始查找 {symbol} 的规则...\n")
            
            # 查找当前交易对的信息
            found = False
            for symbol_info in raw_info.get('symbols', []):
                if symbol_info.get('symbol') == symbol:
                    found = True
                    result_text.insert('end', f"找到 {symbol} 的交易规则信息:\n")
                    
                    # 提取和显示关键信息
                    contract_type = symbol_info.get('contractType', 'N/A')
                    status = symbol_info.get('status', 'N/A')
                    price_precision = symbol_info.get('pricePrecision', 'N/A')
                    quantity_precision = symbol_info.get('quantityPrecision', 'N/A')
                    
                    result_text.insert('end', f"合约类型: {contract_type}\n")
                    result_text.insert('end', f"状态: {status}\n")
                    result_text.insert('end', f"价格精度: {price_precision}\n")
                    result_text.insert('end', f"数量精度: {quantity_precision}\n\n")
                    
                    # 显示过滤器信息
                    result_text.insert('end', "过滤器信息:\n")
                    for filter_item in symbol_info.get('filters', []):
                        filter_type = filter_item.get('filterType', 'N/A')
                        result_text.insert('end', f"- {filter_type}:\n")
                        
                        for key, value in filter_item.items():
                            if key != 'filterType':
                                result_text.insert('end', f"  {key}: {value}\n")
                        result_text.insert('end', "\n")
                    
                    break
            
            if not found:
                result_text.insert('end', f"未找到 {symbol} 的交易规则信息!\n")
                
            # 尝试使用配置管理器更新
            result_text.insert('end', "尝试使用配置管理器更新交易规则...\n")
            success = self.symbol_config_manager.update_symbol_precision(symbol, raw_info, self.client)
            
            if success:
                result_text.insert('end', "配置更新成功!\n\n")
                # 显示更新后的配置
                config = self.symbol_config_manager.get_symbol_config(symbol)
                result_text.insert('end', "当前配置信息:\n")
                for key, value in config.items():
                    result_text.insert('end', f"{key}: {value}\n")
            else:
                result_text.insert('end', "配置更新失败!\n")
            
            # 添加关闭按钮
            Button(dialog, text="关闭", command=dialog.destroy).pack(pady=10)
            
        except Exception as e:
            messagebox.showerror("错误", f"测试交易规则获取失败: {str(e)}")
            logging.error(f"测试交易规则获取失败: {str(e)}")
            traceback.print_exc()

    def validate_price_input(self, new_value):
        """验证价格输入是否符合精度要求"""
        # 允许空值和小数点
        if not new_value or new_value == '.':
            return True
        
        # 检查是否是有效数字
        try:
            # 如果包含多个小数点，不允许
            if new_value.count('.') > 1:
                return False
                
            # 如果是纯数字或者有效的小数，继续检查精度
            if '.' in new_value:
                integer_part, decimal_part = new_value.split('.')
                
                # 获取当前交易对的价格精度
                symbol = self.current_valid_symbol
                config = self.symbol_config_manager.get_symbol_config(symbol)
                price_precision = config.get('price_precision', 2)
                
                # 检查小数部分长度是否超过精度
                if len(decimal_part) > price_precision:
                    return False
            
            # 尝试转换为浮点数
            float(new_value)
            return True
        except ValueError:
            return False
    
    def validate_amount_input(self, new_value):
        """验证数量输入是否符合精度要求"""
        # 允许空值和小数点
        if not new_value or new_value == '.':
            return True
        
        # 检查是否是有效数字
        try:
            # 如果包含多个小数点，不允许
            if new_value.count('.') > 1:
                return False
                
            # 如果是纯数字或者有效的小数，继续检查精度
            if '.' in new_value:
                integer_part, decimal_part = new_value.split('.')
                
                # 获取当前交易对的数量精度
                symbol = self.current_valid_symbol
                config = self.symbol_config_manager.get_symbol_config(symbol)
                
                # 根据当前单位选择使用的精度
                if self.amount_unit_var.get() == "USDT":
                    # 如果是USDT单位，使用价格精度
                    precision = config.get('price_precision', 2)
                else:
                    # 如果是币种单位，使用数量精度
                    precision = config.get('quantity_precision', 3)
                
                # 检查小数部分长度是否超过精度
                if len(decimal_part) > precision:
                    return False
            
            # 尝试转换为浮点数
            float(new_value)
            return True
        except ValueError:
            return False

    def sync_time_with_ntplib(self):
        """使用ntplib同步系统时间"""
        if sync_time_with_ntplib(self.client):
            messagebox.showinfo("同步成功", "使用ntplib成功同步系统时间和交易所时间")
            # 立即更新时间显示
            time_label = None
            if hasattr(self, 'toolbar') and hasattr(self.toolbar, 'time_label'):
                time_label = self.toolbar.time_label
                if time_label and time_label.winfo_exists():
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    time_label.config(text=f" {current_time}")
        else:
            messagebox.showerror("同步失败", "同步系统时间失败，请检查网络连接或尝试手动同步")

    # 手动绘制K线函数
    def draw_candles(self, df_candlesticks, reference_df, ax, color_up='g', color_down='r', alpha=0.5, linewidth=0.5, label=None):
        """
        在给定的坐标轴上手动绘制一系列K线
        
        参数:
            df_candlesticks: 包含K线数据的DataFrame，需要有Open,High,Low,Close列和时间索引
            reference_df: 参考DataFrame，用于确定时间索引在x轴上的位置
            ax: matplotlib坐标轴对象
            color_up: 阳线颜色
            color_down: 阴线颜色
            alpha: 透明度
            linewidth: 线宽
            label: 图例标签
        
        返回:
            绘制的K线对象列表
        """
        candle_objects = []
        
        # 确保df_candlesticks有正确的时间索引
        if not df_candlesticks.index.name == 'Open time' and 'Open time' in df_candlesticks.columns:
            df_candlesticks = df_candlesticks.set_index('Open time')
        
        # 遍历每根K线
        for idx, row in df_candlesticks.iterrows():
            # 只处理在reference_df中存在的时间索引
            if idx in reference_df.index:
                # 获取在reference_df中的x坐标位置
                x_idx = list(reference_df.index).index(idx)
                
                # 获取K线数据
                open_price = float(row['Open'])
                high_price = float(row['High'])
                low_price = float(row['Low'])
                close_price = float(row['Close'])
                
                # 确定K线颜色
                color = color_up if close_price >= open_price else color_down
                
                # 绘制K线实体
                rect = patches.Rectangle(
                    (x_idx - 0.25, min(open_price, close_price)),  # 位置
                    0.5,  # 宽度
                    abs(close_price - open_price),  # 高度
                    color=color,
                    alpha=alpha,
                    linewidth=linewidth,
                    label=label if len(candle_objects) == 0 else None  # 只对第一个元素设置标签
                )
                candle_objects.append(ax.add_patch(rect))
                
                # 绘制上下影线
                line1 = ax.plot(
                    [x_idx, x_idx],
                    [low_price, min(open_price, close_price)],
                    color=color,
                    linewidth=linewidth,
                    alpha=alpha
                )[0]
                candle_objects.append(line1)
                
                line2 = ax.plot(
                    [x_idx, x_idx],
                    [max(open_price, close_price), high_price],
                    color=color,
                    linewidth=linewidth,
                    alpha=alpha
                )[0]
                candle_objects.append(line2)
        
        return candle_objects

    def destroy(self):
        """销毁页面，释放资源"""       
        # 移除倒计时标签
        if hasattr(self, 'countdown_label') and self.countdown_label:
            try:
                if self.countdown_label in time_manager.countdown_labels:
                    time_manager.remove_countdown_label()
            except Exception as e:
                logging.error(f"移除倒计时标签失败: {str(e)}")
        
        # 调用父类的destroy方法
        super().destroy()

    def start_price_and_countdown_refresh(self):
        # 启动价格线和倒计时刷新
        self.toggle_auto_refresh()  # price_refresh_job调度在这里
        # 重新添加倒计时标签
        if hasattr(self, 'countdown_label') and self.countdown_label:
            interval = self.interval_var.get()
            time_manager.add_countdown_label(self.countdown_label, interval)

    def stop_price_and_countdown_refresh(self):
        # 停止价格线刷新
        if hasattr(self, 'price_refresh_job'):
            self.after_cancel(self.price_refresh_job)
            del self.price_refresh_job
        # 停止K线自动刷新
        if hasattr(self, 'kline_refresh_job'):
            self.after_cancel(self.kline_refresh_job)
            del self.kline_refresh_job
        # 移除倒计时标签
        time_manager.remove_countdown_label()


class KlineDataManagerPage(Frame):
    """K线数据管理界面"""
    def __init__(self, master, switch_page_callback, client):
        super().__init__(master)
        self.switch_page_callback = switch_page_callback
        self.client = client
        self.symbols = []  # 存储所有交易对
        self.symbol_var = StringVar()
        self.interval_var = StringVar(value="1m")  # 设置默认值
        self.data_checked = False  # 新增：完整性检查标志
        self.last_checked_range = None  # 新增：记录上次检查的对齐时间范围
        
        # 先加载交易对
        self.load_trading_symbols()
        if not self.symbols:  # 如果加载失败，使用默认值
            self.symbols = ['BTCUSDT']
            self.symbol_var.set('BTCUSDT')
        else:
            self.symbol_var.set(self.symbols[0])  # 设置默认交易对
            
        self.create_widgets()

    def create_widgets(self):
        # 创建顶部控制区域
        control_frame = Frame(self)
        control_frame.pack(fill='x', padx=5, pady=5)

        # 返回按钮
        back_btn = Button(control_frame, text="返回主页", command=lambda: self.switch_page_callback("main"))
        back_btn.pack(side='left', padx=5)

        # 标的选择区域
        symbol_frame = Frame(control_frame)
        symbol_frame.pack(side='left', padx=20)
        
        Label(symbol_frame, text="交易对:").pack(side='left')
        self.symbol_combobox = ttk.Combobox(symbol_frame, textvariable=self.symbol_var, values=self.symbols, width=15)
        self.symbol_combobox.pack(side='left', padx=5)
        
        # 设置初始值
        if self.symbols:
            self.symbol_combobox.set(self.symbols[0])
        
        # 绑定交易对选择框事件
        self.symbol_var.trace('w', self.on_symbol_var_change)  # 监听变量变化
        self.symbol_combobox.bind('<<ComboboxSelected>>', self.on_symbol_select)
        self.symbol_combobox.bind('<FocusIn>', self.on_symbol_focus)
        self.symbol_combobox.bind('<Return>', self.on_symbol_return)

        # 时间周期选择区域
        interval_frame = Frame(control_frame)
        interval_frame.pack(side='left', padx=20)
        
        Label(interval_frame, text="周期:").pack(side='left')
        intervals = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w']
        self.interval_combobox = ttk.Combobox(interval_frame, textvariable=self.interval_var, values=intervals, width=10)
        self.interval_combobox.pack(side='left', padx=5)
        self.interval_combobox.set('')  # 初始为空
        self.interval_combobox.bind('<<ComboboxSelected>>', self.on_interval_change)

        # ====== 新增：自定义时间范围输入（分段） ======
        custom_time_frame = Frame(control_frame)
        custom_time_frame.pack(side='left', padx=20)
        Label(custom_time_frame, text="自定义时间:").pack(side='left')
        # 起始时间
        self.start_year = StringVar(); self.start_month = StringVar(); self.start_day = StringVar()
        self.start_hour = StringVar(); self.start_minute = StringVar()
        Entry(custom_time_frame, textvariable=self.start_year, width=4).pack(side='left'); Label(custom_time_frame, text="/").pack(side='left')
        Entry(custom_time_frame, textvariable=self.start_month, width=2).pack(side='left'); Label(custom_time_frame, text="/").pack(side='left')
        Entry(custom_time_frame, textvariable=self.start_day, width=2).pack(side='left'); Label(custom_time_frame, text=" ").pack(side='left')
        Entry(custom_time_frame, textvariable=self.start_hour, width=2).pack(side='left'); Label(custom_time_frame, text=":").pack(side='left')
        Entry(custom_time_frame, textvariable=self.start_minute, width=2).pack(side='left')
        Label(custom_time_frame, text="-").pack(side='left')
        # 结束时间
        self.end_year = StringVar(); self.end_month = StringVar(); self.end_day = StringVar()
        self.end_hour = StringVar(); self.end_minute = StringVar()
        Entry(custom_time_frame, textvariable=self.end_year, width=4).pack(side='left'); Label(custom_time_frame, text="/").pack(side='left')
        Entry(custom_time_frame, textvariable=self.end_month, width=2).pack(side='left'); Label(custom_time_frame, text="/").pack(side='left')
        Entry(custom_time_frame, textvariable=self.end_day, width=2).pack(side='left'); Label(custom_time_frame, text=" ").pack(side='left')
        Entry(custom_time_frame, textvariable=self.end_hour, width=2).pack(side='left'); Label(custom_time_frame, text=":").pack(side='left')
        Entry(custom_time_frame, textvariable=self.end_minute, width=2).pack(side='left')
        # 绑定回车和失焦事件，自动刷新数据范围显示
        for var in [self.start_year, self.start_month, self.start_day, self.start_hour, self.start_minute,
                    self.end_year, self.end_month, self.end_day, self.end_hour, self.end_minute]:
            # 只在失焦时才更新数据范围
            pass  # 不再trace写入，改为绑定失焦事件
        # 绑定失焦事件
        for entry in custom_time_frame.winfo_children():
            if isinstance(entry, Entry):
                entry.bind('<FocusOut>', lambda event: self._on_timebox_change())
        # ====== 新增结束 ======

        # 创建数据信息显示区域
        info_frame = Frame(self)
        info_frame.pack(fill='x', padx=5, pady=5)
        
        self.data_info_label = Label(info_frame, text="请选择交易对和时间周期查看数据信息")
        self.data_info_label.pack(pady=5)

        # 创建按钮区域
        button_frame = Frame(self)
        button_frame.pack(fill='x', padx=5, pady=5)
        
        self.check_btn = Button(button_frame, text="检查数据完整性", command=self.check_data_integrity)
        self.check_btn.pack(side='left', padx=5)
        
        self.repair_btn = Button(button_frame, text="修复数据", command=self.repair_data, state=DISABLED)
        self.repair_btn.pack(side='left', padx=5)

        # 创建日志显示区域
        log_frame = Frame(self)
        log_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        self.log_text = Text(log_frame, height=10, wrap='word')
        scrollbar = Scrollbar(log_frame, command=self.log_text.yview)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.log_text.pack(side=LEFT, fill=BOTH, expand=True)
        self.log_text.config(yscrollcommand=scrollbar.set)
        self.log_text.config(state=DISABLED)

        # 新增停止修复按钮
        self.stop_repair_btn = Button(button_frame, text="停止修复", command=self.stop_repair, state=DISABLED)
        self.stop_repair_btn.pack(side='left', padx=5)

        # 进入界面后自动显示当前默认交易对和周期的数据及时间范围
        # self.update_data_info(auto_fill=True)

    def load_trading_symbols(self):
        """加载所有可交易的交易对"""
        try:
            # 获取交易所信息
            info = self.client.exchange_info()
            # 获取所有可交易的交易对
            all_symbols = []
            for symbol_info in info['symbols']:
                if (symbol_info['status'] == 'TRADING' and 
                    symbol_info['contractType'] == 'PERPETUAL' and
                    symbol_info['symbol'].endswith('USDT')):  # 只获取USDT合约
                    all_symbols.append(symbol_info['symbol'])
            
            # 对交易对进行排序，优先显示主流币种
            priority_coins = ['BTC', 'ETH', 'SOL', 'BNB', 'XRP', 'DOT', 'ADA']
            
            def get_sort_key(symbol):
                # 移除USDT后缀
                base_coin = symbol.replace('USDT', '')
                # 如果是优先币种，返回其在列表中的索引
                if base_coin in priority_coins:
                    return (0, priority_coins.index(base_coin))
                # 其他币种按字母顺序排序
                return (1, base_coin)
            
            self.symbols = sorted(all_symbols, key=get_sort_key)
            logging.info(f"K线管理加载了 {len(self.symbols)} 个交易对")
            
            # 设置默认选中的交易对
            if self.symbols:
                self.symbol_var.set(self.symbols[0])
                
        except Exception as e:
            logging.error(f"加载交易对失败: {str(e)}")
            self.symbols = ['BTCUSDT']  # 加载失败时使用默认值
            self.symbol_var.set('BTCUSDT')

    def on_symbol_select(self, event=None):
        """当用户从下拉列表中选择一个交易对时"""
        self.on_symbol_change()
        self.symbol_combobox.selection_clear()  # 清除选择，避免重复触发
        
    def on_symbol_focus(self, event=None):
        """当交易对选择框获得焦点时"""
        # 优先显示主流币种
        priority_coins = ['BTC', 'ETH', 'SOL', 'BNB', 'XRP', 'DOT', 'ADA']
        def get_sort_key(symbol):
            base_coin = symbol.replace('USDT', '')
            if base_coin in priority_coins:
                return (0, priority_coins.index(base_coin))
            return (1, base_coin)
        
        sorted_symbols = sorted(self.symbols, key=get_sort_key)
        self.symbol_combobox['values'] = sorted_symbols
        
        # 展开下拉列表
        if not self.symbol_combobox.winfo_ismapped():
            self.symbol_combobox.event_generate('<Down>')  # 展开下拉列表
            
    def on_symbol_var_change(self, *args):
        """当输入内容变化时触发"""
        current_text = self.symbol_var.get().upper()
        
        # 如果输入框为空，显示所有交易对（常用在前）
        if not current_text:
            # 优先显示主流币种
            priority_coins = ['BTC', 'ETH', 'SOL', 'BNB', 'XRP', 'DOT', 'ADA']
            def get_sort_key(symbol):
                base_coin = symbol.replace('USDT', '')
                if base_coin in priority_coins:
                    return (0, priority_coins.index(base_coin))
                return (1, base_coin)
            
            sorted_symbols = sorted(self.symbols, key=get_sort_key)
            self.symbol_combobox['values'] = sorted_symbols
        else:
            # 有输入内容时，只显示匹配的交易对（不区分大小写）
            filtered_symbols = [s for s in self.symbols if current_text in s.upper()]
            self.symbol_combobox['values'] = filtered_symbols
        
        # 如果有匹配结果且列表未展开，则展开列表
        if self.symbol_combobox['values'] and not self.symbol_combobox.winfo_ismapped():
            self.symbol_combobox.event_generate('<Down>')
        
        # 保持输入框焦点，光标在末尾
        self.after(10, lambda: self.symbol_combobox.icursor('end'))
        self.after(10, lambda: self.symbol_combobox.focus_set())

    def on_symbol_return(self, event=None):
        """当用户按下回车键时"""
        if self.symbol_combobox['values']:
            # 如果没有选中项但有匹配结果，选择第一个
            if not self.symbol_combobox.get() in self.symbol_combobox['values']:
                self.symbol_var.set(self.symbol_combobox['values'][0])
            self.on_symbol_change()
        return 'break'

    def on_symbol_change(self, event=None):
        """当选择的交易对改变时"""
        try:
            symbol = self.symbol_var.get().strip().upper()
            if not symbol.endswith('USDT'):
                symbol = f"{symbol}USDT"
            self.symbol_var.set(symbol)  # 更新为标准格式
            self.data_checked = False  # 只要切换交易对就重置检查标志
            self.update_data_info(auto_fill=True)
        except Exception as e:
            self.log_message(f"更新交易对信息失败: {str(e)}")

    def on_interval_change(self, event=None):
        """当选择的时间周期改变时"""
        # 只有选中有效周期时才允许检查数据完整性
        if self.interval_var.get() in ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w']:
            self.check_btn.config(state=NORMAL)
        else:
            self.check_btn.config(state=DISABLED)
        self.data_checked = False  # 只要切换周期就重置检查标志
        self.update_data_info(auto_fill=True)

    def get_safe_end(self, interval):
        if interval.endswith('m'):
            minutes = int(interval[:-1])
            now = datetime.now()
            prev_time = now - pd.Timedelta(minutes=minutes)
            total_minutes = prev_time.hour * 60 + prev_time.minute
            safe_minutes = (total_minutes // minutes) * minutes
            last_open = prev_time.replace(hour=safe_minutes // 60, minute=safe_minutes % 60, second=0, microsecond=0)
            safe_end = last_open + pd.Timedelta(minutes=minutes)
            return pd.Timestamp(safe_end)
        elif interval.endswith('h'):
            hours = int(interval[:-1])
            now = datetime.now()
            prev_time = now - pd.Timedelta(hours=hours)
            safe_hour = (prev_time.hour // hours) * hours
            last_open = prev_time.replace(hour=safe_hour, minute=0, second=0, microsecond=0)
            safe_end = last_open + pd.Timedelta(hours=hours)
            return pd.Timestamp(safe_end)
        elif interval == '1d':
            now = datetime.now()
            base = now.replace(hour=8, minute=0, second=0, microsecond=0)
            if now < base:
                last_open = base - pd.Timedelta(days=1)
            else:
                last_open = base
                while last_open + pd.Timedelta(days=1) <= now:
                    last_open += pd.Timedelta(days=1)
            safe_end = last_open
            return pd.Timestamp(safe_end)
        elif interval == '1w':
            now = datetime.now()
            # 币安期货周线对齐到每周一08:00（北京时间）
            # 找到本周一8:00
            weekday = now.weekday()  # 周一为0
            base = now.replace(hour=8, minute=0, second=0, microsecond=0) - pd.Timedelta(days=weekday)
            if now < base:
                last_open = base - pd.Timedelta(weeks=1)
            else:
                last_open = base
                while last_open + pd.Timedelta(weeks=1) <= now:
                    last_open += pd.Timedelta(weeks=1)
            safe_end = last_open
            return pd.Timestamp(safe_end)
        else:
            return pd.Timestamp(datetime.now())

    def update_data_info(self, auto_fill=False):
        if getattr(self, '_auto_filling_timebox', False):
            return
        symbol = self.symbol_var.get()
        interval = self.interval_var.get()
        if not symbol or not interval:
            return
        try:
            df = self.load_klines_from_local(symbol, interval)
            if not df.empty:
                min_time = pd.to_datetime(df['Open time'].min())
                # 计算最后一根K线的收盘时间 = 最后一根K线的Open time + 周期长度
                max_open_time = pd.to_datetime(df['Open time'].max())
                # 解析周期长度
                if interval.endswith('m'):
                    minutes = int(interval[:-1])
                    max_time = max_open_time + pd.Timedelta(minutes=minutes)
                elif interval.endswith('h'):
                    hours = int(interval[:-1])
                    max_time = max_open_time + pd.Timedelta(hours=hours)
                elif interval == '1d':
                    max_time = max_open_time + pd.Timedelta(days=1)
                elif interval == '1w':
                    max_time = max_open_time + pd.Timedelta(weeks=1)
                else:
                    # 默认按1分钟处理
                    max_time = max_open_time + pd.Timedelta(minutes=1)
                if auto_fill:
                    self._auto_filling_timebox = True
                    self.start_year.set(str(min_time.year))
                    self.start_month.set(f"{min_time.month:02d}")
                    self.start_day.set(f"{min_time.day:02d}")
                    self.start_hour.set(f"{min_time.hour:02d}")
                    self.start_minute.set(f"{min_time.minute:02d}")
                    self.end_year.set(str(max_time.year))
                    self.end_month.set(f"{max_time.month:02d}")
                    self.end_day.set(f"{max_time.day:02d}")
                    self.end_hour.set(f"{max_time.hour:02d}")
                    self.end_minute.set(f"{max_time.minute:02d}")
                    self._auto_filling_timebox = False
                self.data_checked = False
                start_dt, end_dt = self.get_custom_time_range()
                aligned_start = self.align_time_to_interval(start_dt, interval, align_forward=True) if start_dt else None
                aligned_end = self.align_time_to_interval(end_dt, interval, align_forward=False) if end_dt else None
                # 应用safe_end
                safe_end = self.get_safe_end(interval)
                if aligned_end and aligned_end > safe_end:
                    aligned_end = safe_end
                range_str = "--"
                if aligned_start and aligned_end:
                    range_str = f"{aligned_start.strftime('%Y/%m/%d %H:%M')} - {aligned_end.strftime('%Y/%m/%d %H:%M')}"
                # 只判断时间范围本身是否有效，不再判断本地数据是否有K线
                invalid_range = False
                if not aligned_start or not aligned_end or aligned_start >= aligned_end:
                    invalid_range = True

                if invalid_range:
                    self.data_info_label.config(
                        text=f"{range_str}\n⚠️ 该时间范围不存在K线（请检查输入的时间和周期）"
                    )
                    self.repair_btn.config(state=DISABLED)
                else:
                    self.data_info_label.config(
                        text=f"{range_str}\n❌ 未经过完整性检查"
                    )
            else:
                self.data_checked = False
                self.data_info_label.config(text="未找到本地数据")
        except Exception as e:
            self.log_message(f"更新数据信息失败: {str(e)}")

    def filter_data_by_input_time(self, df):
        """根据当前输入框内容识别时间范围并筛选本地数据，返回筛选后的DataFrame"""
        start_dt, end_dt = self.get_custom_time_range()
        if not df.empty:
            if start_dt is not None:
                df = df[df['Open time'] >= start_dt]
            if end_dt is not None:
                df = df[df['Open time'] <= end_dt]
        return df

    def detect_missing_ranges(self, df, start_dt, end_dt, interval_minutes):
        """检测缺失区间，返回[(start, end), ...]，含首尾和中间缺口"""
        missing_ranges = []
        if df.empty:
            if start_dt and end_dt:
                missing_ranges.append((start_dt, end_dt))
            return missing_ranges
        df = df.sort_values(by='Open time')
        # 首区间
        if start_dt is not None and df['Open time'].min() > start_dt:
            first_time = df['Open time'].min()
            missing_start = start_dt
            missing_end = first_time
            missing_ranges.append((missing_start, missing_end))
        # 中间缺口
        expected_diff = pd.Timedelta(minutes=interval_minutes)
        time_diffs = df['Open time'].diff()
        for idx in time_diffs[time_diffs > expected_diff].index:
            prev_time = df.loc[idx-1, 'Open time']
            curr_time = df.loc[idx, 'Open time']
            missing_start = prev_time + expected_diff
            missing_end = curr_time
            missing_ranges.append((missing_start, missing_end))
        # 尾区间
        if end_dt is not None and df['Open time'].max() < end_dt:
            last_time = df['Open time'].max()
            missing_start = last_time + pd.Timedelta(minutes=interval_minutes)
            missing_end = end_dt
            if missing_start <= missing_end - pd.Timedelta(minutes=interval_minutes):
                missing_ranges.append((missing_start, missing_end))
        print(missing_ranges)
        return missing_ranges

    def check_data_integrity(self):
        symbol = self.symbol_var.get()
        interval = self.interval_var.get()
        if not symbol or not interval:
            return
        try:
            df = self.load_klines_from_local(symbol, interval)
            start_dt, end_dt = self.get_custom_time_range()
            aligned_start = self.align_time_to_interval(start_dt, interval, align_forward=True) if start_dt else None
            aligned_end = self.align_time_to_interval(end_dt, interval, align_forward=False) if end_dt else None
            # 应用safe_end
            safe_end = self.get_safe_end(interval)
            if aligned_end and aligned_end > safe_end:
                aligned_end = safe_end
            range_str = "--"
            if aligned_start and aligned_end:
                range_str = f"{aligned_start.strftime('%Y/%m/%d %H:%M')} - {aligned_end.strftime('%Y/%m/%d %H:%M')}"
            # 判断时间范围有效性
            invalid_range = (
                not aligned_start or not aligned_end or aligned_start > aligned_end or aligned_start == aligned_end
            )
            if invalid_range:
                self.data_checked = False
                self.data_info_label.config(
                    text=f"{range_str}\n⚠️ 该时间范围不存在K线（请检查输入的时间和周期）"
                )
                self.repair_btn.config(state=DISABLED)
                self.log_message(f"该时间范围不存在K线（请检查输入的时间和周期）")
                return
            interval_minutes = self.get_interval_minutes(interval)
            df = self.filter_data_by_input_time(df)
            missing_ranges = self.detect_missing_ranges(df, aligned_start, aligned_end, interval_minutes)
            if not missing_ranges:
                self.log_message("✅ 数据完整，没有缺失")
                self.repair_btn.config(state=DISABLED)
                self.data_checked = True
                self.last_checked_range = (aligned_start, aligned_end)
                self.data_info_label.config(
                    text=f"{range_str}\n✅ 已通过完整性检查"
                )
                return
            # 有缺失区间
            self.log_message(f"❌ 数据不完整，缺失区间如下：")
            self.repair_btn.config(state=NORMAL)
            self.data_checked = False
            self.last_checked_range = (aligned_start, aligned_end)
            self.data_info_label.config(
                text=f"{range_str}\n❌ 未通过完整性检查"
            )
            # 计算缺失区间右端（收盘时间）
            for rng in missing_ranges:
                start_dt = self.align_time_to_interval(rng[0], interval, align_forward=True) if rng[0] else None
                end_dt = self.align_time_to_interval(rng[1], interval, align_forward=False) if rng[1] else None
                total_minutes = int((end_dt - start_dt).total_seconds() // 60)
                missing_count = max(0, total_minutes // interval_minutes)
                self.log_message(f"缺失区间: {start_dt.strftime('%Y/%m/%d %H:%M')} - {end_dt.strftime('%Y/%m/%d %H:%M')}  缺失K线: {missing_count} 条")
        except Exception as e:
            self.data_checked = False
            self.data_info_label.config(text=f"--\n❌ 未通过完整性检查")
            self.log_message(f"检查数据完整性失败: {str(e)}")

    def repair_data(self):
        symbol = self.symbol_var.get()
        interval = self.interval_var.get()
        if not symbol or not interval:
            return
        if getattr(self, '_repair_running', False):
            self.log_message("修复任务正在进行中，请勿重复启动！")
            return
        self._repair_running = True
        self._repair_stop_flag = False
        self.check_btn.config(state=DISABLED)
        self.stop_repair_btn.config(state=NORMAL)
        self.repair_btn.config(state=DISABLED)
        def do_repair():
            try:
                df = self.load_klines_from_local(symbol, interval)
                start_dt, end_dt = self.get_custom_time_range()
                interval_minutes = self.get_interval_minutes(interval)
                aligned_start = self.align_time_to_interval(start_dt, interval, align_forward=True) if start_dt else None
                aligned_end = self.align_time_to_interval(end_dt, interval, align_forward=False) if end_dt else None
                # 应用safe_end
                safe_end = self.get_safe_end(interval)
                if aligned_end and aligned_end > safe_end:
                    aligned_end = safe_end
                df = self.filter_data_by_input_time(df)
                missing_ranges = self.detect_missing_ranges(df, aligned_start, aligned_end, interval_minutes)
                if not missing_ranges:
                    self.log_text.after(0, lambda: self.log_message("数据已完整，无需修复"))
                    self.repair_btn.after(0, lambda: self.repair_btn.config(state=NORMAL))
                    self.check_btn.after(0, lambda: self.check_btn.config(state=NORMAL))
                    self.stop_repair_btn.after(0, lambda: self.stop_repair_btn.config(state=DISABLED))
                    self._repair_running = False
                    return
                for rng in missing_ranges:
                    # 区间对齐与check_data_integrity一致
                    start_time = self.align_time_to_interval(rng[0], interval, align_forward=True) if rng[0] else None
                    end_time = self.align_time_to_interval(rng[1], interval, align_forward=False) if rng[1] else None
                    total_minutes = int((end_time - start_time).total_seconds() // 60)
                    missing_count = max(0, total_minutes // interval_minutes)
                    # 日志区间严格与检查一致
                    self.log_text.after(0, lambda s=start_time, e=end_time, n=missing_count: self.log_message(
                        f"正在修复区间: {s.strftime('%Y/%m/%d %H:%M')} - {e.strftime('%Y/%m/%d %H:%M')}"
                    ))
                    current_start = start_time
                    while current_start < end_time:
                        if getattr(self, '_repair_stop_flag', False):
                            self.log_text.after(0, lambda: self.log_message("用户已请求停止修复，正在退出..."))
                            self.repair_btn.after(0, lambda: self.repair_btn.config(state=NORMAL))
                            self.check_btn.after(0, lambda: self.check_btn.config(state=NORMAL))
                            self.stop_repair_btn.after(0, lambda: self.stop_repair_btn.config(state=DISABLED))
                            self._repair_running = False
                            self.log_text.after(0, lambda: self.check_data_integrity())
                            return
                        max_span = pd.Timedelta(minutes=interval_minutes * 1000)
                        batch_end = min(current_start + max_span, end_time)
                        batch_end = pd.to_datetime(batch_end)
                        start_utc = current_start - pd.Timedelta(hours=8)
                        end_utc = batch_end - pd.Timedelta(hours=8)
                        start_ms = int(start_utc.timestamp() * 1000)
                        end_ms = int(end_utc.timestamp() * 1000) - 1
                        klines = self.client.klines(
                            symbol=symbol,
                            interval=interval,
                            startTime=start_ms,
                            endTime=end_ms,
                            limit=1000
                        )
                        print(klines)
                        if klines:
                            repair_df = pd.DataFrame(klines, columns=[
                                'Open time', 'Open', 'High', 'Low', 'Close', 'Volume',
                                'Close time', 'Quote asset volume', 'Number of trades',
                                'Taker buy base asset volume', 'Taker buy quote asset volume', 'Ignore'
                            ])
                            repair_df['Open time'] = pd.to_datetime(repair_df['Open time'], unit='ms') + pd.Timedelta(hours=8)
                            repair_df['Close time'] = pd.to_datetime(repair_df['Close time'], unit='ms') + pd.Timedelta(hours=8)
                            # 只保留 current_start <= kline['Open time'] < batch_end
                            repair_df = repair_df[(repair_df['Open time'] >= current_start) & (repair_df['Open time'] < batch_end)]
                            self.save_klines_to_local(repair_df, symbol, interval)
                            n = len(repair_df)
                            self.log_text.after(0, lambda cs=current_start, ce=batch_end, nn=n: self.log_message(
                                f"成功补充了 {nn} 条数据: {cs.strftime('%Y/%m/%d %H:%M')} - {ce.strftime('%Y/%m/%d %H:%M')}"
                            ))
                        else:
                            self.log_text.after(0, lambda cs=current_start, ce=batch_end: self.log_message(
                                f"API未返回任何K线数据，区间: {cs.strftime('%Y/%m/%d %H:%M')} - {ce.strftime('%Y/%m/%d %H:%M')}"
                            ))
                        current_start = batch_end
                self.log_text.after(0, lambda: self.check_data_integrity())
                self.log_text.after(0, lambda: self.update_data_info())
                self.repair_btn.after(0, lambda: self.repair_btn.config(state=NORMAL))
                self.check_btn.after(0, lambda: self.check_btn.config(state=NORMAL))
                self.stop_repair_btn.after(0, lambda: self.stop_repair_btn.config(state=DISABLED))
                self._repair_running = False
            except Exception as e:
                self.log_text.after(0, lambda: self.log_message(f"修复数据失败: {str(e)}"))
                self.repair_btn.after(0, lambda: self.repair_btn.config(state=NORMAL))
                self.check_btn.after(0, lambda: self.check_btn.config(state=NORMAL))
                self.stop_repair_btn.after(0, lambda: self.stop_repair_btn.config(state=DISABLED))
                self._repair_running = False
        threading.Thread(target=do_repair, daemon=True).start()

    def get_interval_minutes(self, interval):
        """将时间周期转换为分钟数"""
        interval_map = {
            '1m': 1,
            '5m': 5,
            '15m': 15,
            '30m': 30,
            '1h': 60,
            '4h': 240,
            '1d': 1440,
            '1w': 10080
        }
        return interval_map.get(interval, 1)

    def log_message(self, message):
        """在日志区域显示消息，只有在最底部时才自动滚动"""
        self.log_text.config(state=NORMAL)
        # 判断当前是否在最底部
        yview = self.log_text.yview()
        at_bottom = yview[1] >= 0.999  # 允许微小误差
        self.log_text.insert(END, f"{datetime.now().strftime('%H:%M:%S')} - {message}\n")
        if at_bottom:
            self.log_text.see(END)
        self.log_text.config(state=DISABLED)

    def get_kline_file_path(self, symbol, interval, dt=None):
        symbol_dir = os.path.join(KLINE_DATA_DIR, symbol)
        os.makedirs(symbol_dir, exist_ok=True)
        if interval == '1m' and dt is not None:
            month_str = get_month_str(dt)
            return os.path.join(symbol_dir, f"{month_str}.csv")
        else:
            return os.path.join(symbol_dir, f"{interval}.csv")

    def load_klines_from_local(self, symbol, interval, start_dt=None, end_dt=None):
        try:
            symbol_dir = os.path.join(KLINE_DATA_DIR, symbol)
            if interval == '1m':
                # 自动合并所需月份
                if start_dt is None or end_dt is None:
                    # 没有指定范围，合并所有1m分月csv
                    files = [f for f in os.listdir(symbol_dir) if f.endswith('.csv') and len(f) == 11]
                else:
                    months = get_month_range(start_dt, end_dt)
                    files = [f"{m}.csv" for m in months if os.path.exists(os.path.join(symbol_dir, f"{m}.csv"))]
                dfs = []
                for f in files:
                    file_path = os.path.join(symbol_dir, f)
                    try:
                        df = pd.read_csv(file_path)
                        if not df.empty and 'Open time' in df.columns:
                            df['Open time'] = pd.to_datetime(df['Open time'])
                            dfs.append(df)
                    except Exception as e:
                        logging.error(f"读取分月K线失败: {file_path}, {e}")
                if dfs:
                    all_df = pd.concat(dfs)
                    all_df = all_df.drop_duplicates(subset=['Open time'], keep='last')
                    all_df = all_df.sort_values(by='Open time')
                    # 按需筛选
                    if start_dt is not None:
                        all_df = all_df[all_df['Open time'] >= start_dt]
                    if end_dt is not None:
                        all_df = all_df[all_df['Open time'] <= end_dt]
                    return all_df.reset_index(drop=True)
                else:
                    return pd.DataFrame()
            else:
                file_path = self.get_kline_file_path(symbol, interval)
                if os.path.exists(file_path):
                    df = pd.read_csv(file_path)
                    if not df.empty and 'Open time' in df.columns:
                        df['Open time'] = pd.to_datetime(df['Open time'])
                        if start_dt is not None:
                            df = df[df['Open time'] >= start_dt]
                        if end_dt is not None:
                            df = df[df['Open time'] <= end_dt]
                        return df.reset_index(drop=True)
                return pd.DataFrame()
        except Exception as e:
            logging.error(f"加载本地K线数据失败: {str(e)}")
            return pd.DataFrame()

    def save_klines_to_local(self, klines_df, symbol, interval):
        try:
            # 自动创建symbol目录，防止目录不存在导致保存失败
            symbol_dir = os.path.join(KLINE_DATA_DIR, symbol)
            os.makedirs(symbol_dir, exist_ok=True)
            df = klines_df.copy()
            if 'Open time' in df.columns:
                df['Open time'] = pd.to_datetime(df['Open time'])
            if interval == '1m':
                if df.empty:
                    return
                df['month'] = df['Open time'].dt.strftime('%Y-%m')
                for month, group in df.groupby('month'):
                    file_path = os.path.join(KLINE_DATA_DIR, symbol, f"{month}.csv")
                    if os.path.exists(file_path):
                        try:
                            existing_df = pd.read_csv(file_path)
                            if 'Open time' in existing_df.columns:
                                existing_df['Open time'] = pd.to_datetime(existing_df['Open time'])
                            combined_df = pd.concat([existing_df, group.drop(columns=['month'])])
                            combined_df = combined_df.drop_duplicates(subset=['Open time'], keep='last')
                            combined_df = combined_df.sort_values(by='Open time')
                            combined_df.to_csv(file_path, index=False)
                        except Exception as e:
                            group.drop(columns=['month']).to_csv(file_path, index=False)
                    else:
                        group.drop(columns=['month']).to_csv(file_path, index=False)
            else:
                file_path = self.get_kline_file_path(symbol, interval)
                if os.path.exists(file_path):
                    try:
                        existing_df = pd.read_csv(file_path)
                        if 'Open time' in existing_df.columns:
                            existing_df['Open time'] = pd.to_datetime(existing_df['Open time'])
                        combined_df = pd.concat([existing_df, df])
                        combined_df = combined_df.drop_duplicates(subset=['Open time'], keep='last')
                        combined_df = combined_df.sort_values(by='Open time')
                        combined_df.to_csv(file_path, index=False)
                    except Exception as e:
                        df.to_csv(file_path, index=False)
                else:
                    df.to_csv(file_path, index=False)
        except Exception as e:
            self.log_message(f"保存K线数据失败: {str(e)}")

    def get_custom_time_range(self):
        """获取自定义时间范围，返回(start_dt, end_dt)，无效返回None"""
        def parse_time(y, m, d, h, mi):
            try:
                if all([y, m, d, h, mi]):
                    return pd.to_datetime(f"{y}/{m}/{d} {h}:{mi}", format="%Y/%m/%d %H:%M")
            except Exception:
                return None
            return None
        start_dt = parse_time(self.start_year.get().strip(), self.start_month.get().strip(), self.start_day.get().strip(),
                              self.start_hour.get().strip(), self.start_minute.get().strip())
        end_dt = parse_time(self.end_year.get().strip(), self.end_month.get().strip(), self.end_day.get().strip(),
                            self.end_hour.get().strip(), self.end_minute.get().strip())
        return start_dt, end_dt

    def stop_repair(self):
        """停止修复数据的后台线程"""
        self._repair_stop_flag = True

    def align_time_to_interval(self, dt, interval, align_forward=False):
        if dt is None:
            return None
        if interval.endswith('m'):
            minutes = int(interval[:-1])
            total_minutes = dt.hour * 60 + dt.minute
            if align_forward:
                aligned_minutes = ((total_minutes + minutes - 1) // minutes) * minutes
            else:
                aligned_minutes = (total_minutes // minutes) * minutes
            hour = aligned_minutes // 60
            minute = aligned_minutes % 60
            aligned = dt.replace(hour=hour, minute=minute, second=0, microsecond=0)
            if align_forward and aligned < dt:
                aligned += pd.Timedelta(minutes=minutes)
            if not align_forward and aligned > dt:
                aligned -= pd.Timedelta(minutes=minutes)
            return aligned
        elif interval.endswith('h'):
            hours = int(interval[:-1])
            if align_forward:
                aligned_hour = ((dt.hour + hours - 1) // hours) * hours
            else:
                aligned_hour = (dt.hour // hours) * hours
            aligned = dt.replace(hour=aligned_hour, minute=0, second=0, microsecond=0)
            if align_forward and aligned < dt:
                aligned += pd.Timedelta(hours=hours)
            if not align_forward and aligned > dt:
                aligned -= pd.Timedelta(hours=hours)
            return aligned
        elif interval == '1d':
            base = dt.replace(hour=8, minute=0, second=0, microsecond=0)
            if align_forward:
                if dt < base:
                    aligned = base
                else:
                    days = ((dt - base).days + (1 if dt.time() > base.time() else 0))
                    aligned = base + pd.Timedelta(days=days)
            else:
                if dt < base:
                    aligned = base - pd.Timedelta(days=1)
                else:
                    days = (dt - base).days
                    aligned = base + pd.Timedelta(days=days)
            return aligned
        elif interval == '1w':
            # 币安期货周线对齐到每周一08:00（北京时间）
            weekday = dt.weekday()  # 周一为0
            base = dt.replace(hour=8, minute=0, second=0, microsecond=0) - pd.Timedelta(days=weekday)
            if align_forward:
                if dt < base:
                    aligned = base
                else:
                    weeks = ((dt - base).days // 7) + (1 if dt > base else 0)
                    aligned = base + pd.Timedelta(weeks=weeks)
            else:
                if dt < base:
                    aligned = base - pd.Timedelta(weeks=1)
                else:
                    weeks = (dt - base).days // 7
                    aligned = base + pd.Timedelta(weeks=weeks)
            return aligned
        else:
            return dt

    def _on_timebox_change(self):
        self.data_checked = False
        self.update_data_info(auto_fill=False)

def get_month_str(dt):
    return dt.strftime('%Y-%m')

def get_month_range(start_dt, end_dt):
    """返回[start_dt, end_dt]之间所有月份的YYYY-MM字符串列表"""
    months = []
    cur = start_dt.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    while cur <= end_dt:
        months.append(cur.strftime('%Y-%m'))
        # 下个月
        year = cur.year + (cur.month // 12)
        month = (cur.month % 12) + 1
        cur = cur.replace(year=year, month=month)
    return months

class TradingApp:
    def __init__(self, root, client):
        self.root = root
        self.client = client
        self.root.title("多功能交易软件")
        self.root.geometry("1000x800")
        self.current_page = None
        self.pages = {}
        self.create_pages()
        self.show_page("main")

    def create_pages(self):
        self.pages["main"] = MainPage(self.root, self.show_page, self.client)
        self.pages["log"] = LogPage(self.root, self.show_page)
        self.pages["orders"] = OrdersPage(self.root, self.show_page, self.client)
        self.pages["positions"] = PositionsPage(self.root, self.show_page, self.client)
        self.pages["kline"] = KlinePage(self.root, self.show_page, self.client)
        self.pages["kline_data"] = KlineDataManagerPage(self.root, self.show_page, self.client)
        
    def show_page(self, page_name):
        kline_page = self.pages.get("kline")
        orders_page = self.pages.get("orders")
        positions_page = self.pages.get("positions")
        # 先全部停止
        if kline_page:
            kline_page.stop_balance_update()
            kline_page.stop_price_and_countdown_refresh()
        if orders_page:
            orders_page.stop_orders_update()
        if positions_page:
            positions_page.stop_positions_update()
        # 再根据页面启动
        if page_name == "kline":
            if kline_page:
                kline_page.start_price_and_countdown_refresh()
                kline_page.start_balance_update()
        elif page_name == "orders":
            if orders_page:
                orders_page.start_orders_update()
        elif page_name == "positions":
            if positions_page:
                positions_page.start_positions_update()
        # 主菜单时间自动刷新
        if page_name == "main":
            main_page = self.pages.get("main")
            if main_page and hasattr(main_page, "time_label"):
                start_time_update(main_page.time_label)
        for page in self.pages.values():
            page.pack_forget()
        self.current_page = self.pages.get(page_name)
        if self.current_page:
            self.current_page.pack(fill=BOTH, expand=True)

def sync_time_with_ntplib(client=None):
    """使用ntplib同步系统时间，并更新客户端时间偏移"""
    try:
        # 创建NTP客户端
        ntp_client = ntplib.NTPClient()
        # 从国内NTP服务器池获取时间
        response = ntp_client.request('pool.ntp.org')
        # 获取NTP时间戳
        ntp_time = response.tx_time
        
        # 转换为本地日期和时间字符串
        date_str = time.strftime('%Y-%m-%d', time.localtime(ntp_time))
        time_str = time.strftime('%H:%M:%S', time.localtime(ntp_time))
        
        # 使用系统命令设置日期和时间（适用于Windows）
        os.system(f'date {date_str.replace("-", "/")}')  # Windows使用/分隔日期
        os.system(f'time {time_str}')
        
        logging.info(f"使用ntplib同步系统时间成功：{date_str} {time_str}")
        
        # 如果提供了API客户端，也同步交易所时间
        if client:
            server_time = int(client.time()["serverTime"])
            local_time = int(time.time() * 1000)
            offset = server_time - local_time
            client.timestamp_offset = offset
            logging.info(f"同步交易所时间：服务器时间：{server_time}，本地时间：{local_time}，时间偏移：{offset}ms")
        
        return True
    except ImportError:
        error_msg = "未安装ntplib库，请使用pip安装：pip install ntplib"
        logging.error(error_msg)
        return False
    except Exception as e:
        error_msg = f"使用ntplib同步时间出错：{str(e)}"
        logging.error(error_msg)
        return False

def print_thread_info():
    """定期打印当前活动的线程信息"""
    while True:
        print("\n=== 当前活动线程信息 ===")
        for thread in threading.enumerate():
            print(f"线程名: {thread.name}, ID: {thread.ident}, 活动状态: {thread.is_alive()}")
        print("=======================\n")
        time.sleep(60)  # 每分钟打印一次

def main():
    # 加载环境变量，并初始化 API 客户端
    load_dotenv('1.env')
    API_KEY = os.getenv('API_KEY')
    SECRET_KEY = os.getenv('SECRET_KEY')
    
    # 创建全局时间管理器实例
    global time_manager
    time_manager = TimeManager()
    time_manager.start()
    
    um_futures_client = UMFutures(key=API_KEY, secret=SECRET_KEY)

    # 启动线程监控
    threading.Thread(target=print_thread_info, name="ThreadMonitor", daemon=True).start()
    
    # 创建主窗口
    root = Tk()
    
    # 在关闭应用时清理资源
    def on_closing():
        """应用关闭时的清理工作"""
        try:
            # 停止时间管理器
            if 'time_manager' in globals():
                time_manager.stop()
                
        except Exception as e:
            logging.error(f"应用关闭时清理失败: {str(e)}")
            traceback.print_exc()
            
        # 销毁主窗口
        root.destroy()
    
    # 设置关闭窗口的回调
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    app = TradingApp(root, um_futures_client)
    root.mainloop()

if __name__ == '__main__':
    main()

