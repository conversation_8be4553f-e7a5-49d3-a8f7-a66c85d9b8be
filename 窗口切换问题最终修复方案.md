# 窗口切换问题最终修复方案

## 问题描述

当本程序打开马丁策略参数设置窗口等对话框时，如果用户点击了Windows右下角的"显示桌面"按钮，再点击任务栏的本程序图标，将无法正常切回本程序。

## 根本原因分析

1. **模态对话框限制**：原始代码使用 `dialog.transient(self)` 和 `dialog.grab_set()` 创建模态对话框
2. **任务栏图标映射**：任务栏图标只对应主程序窗口，不包括临时对话框
3. **窗口层级问题**：对话框具有更高优先级，但无法通过任务栏直接访问

## 最终解决方案：独立任务栏窗口

### 核心思路
将对话框改为独立的任务栏窗口，类似Windows文件夹属性窗口的行为：
- 每个对话框都有独立的任务栏图标
- 可以单独点击任务栏图标进行切换
- 不会因为"显示桌面"功能而无法恢复

### 实现方法

#### 1. 创建独立窗口
```python
# 原始代码（有问题）
dialog = Toplevel(self)
dialog.transient(self)
dialog.grab_set()

# 修复后的代码
dialog = Toplevel()  # 不传入parent，创建独立窗口
# 不使用 transient() 和 grab_set()
```

#### 2. 设置窗口属性
```python
# 设置窗口图标，使其在任务栏中显示为独立窗口
try:
    dialog.iconbitmap(default='')  # 使用默认图标
except:
    pass

# 设置窗口属性
dialog.attributes('-topmost', False)
dialog.resizable(True, True)  # 允许调整大小
```

#### 3. 防止重复打开
```python
# 检查是否已经有窗口打开
if hasattr(self, '_dialog_name') and self._dialog_name and self._dialog_name.winfo_exists():
    # 如果窗口已存在，直接激活它
    self._dialog_name.lift()
    self._dialog_name.focus_set()
    return

# 保存对话框引用
self._dialog_name = dialog
```

#### 4. 主程序操控性管理
```python
# 使主程序不可操控（但不阻塞后台功能）
self.grab_release()  # 先释放当前grab
dialog.grab_set()    # 对话框获取grab

# 当对话框关闭时，恢复主程序操控
def on_dialog_close():
    self._dialog_name = None
    # 恢复主程序的操控性
    self.grab_set()
    self.after(100, lambda: self.grab_release())
    dialog.destroy()
    
dialog.protocol("WM_DELETE_WINDOW", on_dialog_close)
```

#### 5. 屏幕居中显示
```python
def center_dialog_on_screen(self, dialog):
    """将对话框居中显示在屏幕中央"""
    dialog.update_idletasks()
    screen_width = dialog.winfo_screenwidth()
    screen_height = dialog.winfo_screenheight()
    
    # 解析窗口尺寸
    geometry = dialog.geometry()
    if 'x' in geometry and '+' in geometry:
        size_part = geometry.split('+')[0]
        if 'x' in size_part:
            dialog_width, dialog_height = map(int, size_part.split('x'))
        else:
            dialog_width = dialog.winfo_reqwidth()
            dialog_height = dialog.winfo_reqheight()
    else:
        dialog_width = dialog.winfo_reqwidth()
        dialog_height = dialog.winfo_reqheight()
    
    # 计算居中位置
    x = (screen_width - dialog_width) // 2
    y = (screen_height - dialog_height) // 2
    dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
```

## 已修复的对话框

在 `ma19.py` 中，以下对话框已经应用了独立窗口修复：

### 主要功能对话框
1. **马丁策略测试参数设置窗口** (`open_martin_test_dialog`)
   - 变量：`self._martin_dialog`
   - 尺寸：500x800
   - 功能：马丁策略参数配置

2. **测试最大涨跌幅参数设置窗口** (`open_test_dialog`)
   - 变量：`self._test_dialog`
   - 尺寸：300x150
   - 功能：涨跌幅测试参数输入

3. **交易配置显示窗口** (`display_symbol_config`)
   - 变量：`self._config_dialog`
   - 尺寸：400x400
   - 功能：显示交易对配置信息

### K线界面对话框
4. **切换保证金模式对话框** (`change_margin_mode`)
   - 变量：`self._margin_dialog`
   - 尺寸：300x150
   - 功能：切换全仓/逐仓模式

5. **调整杠杆倍数对话框** (`change_leverage`)
   - 变量：`self._leverage_dialog`
   - 尺寸：300x200
   - 功能：调整杠杆倍数

## 修复效果

### ✅ 解决的问题
1. **任务栏切换**：每个对话框都有独立的任务栏图标，可以单独切换
2. **显示桌面兼容**：使用"显示桌面"后，可以通过任务栏分别恢复各个窗口
3. **防止重复打开**：同一类型的对话框只能打开一个，重复点击会激活现有窗口
4. **主程序保护**：对话框打开时主程序不可操控，防止误操作
5. **后台任务继续**：K线更新等后台任务不受影响

### 🎯 用户体验改进
1. **直观的窗口管理**：类似Windows原生应用的窗口行为
2. **灵活的操作方式**：可以同时打开多个不同类型的配置窗口
3. **稳定的程序状态**：避免了窗口切换导致的程序无响应问题

## 测试验证

运行 `test_kline_dialogs.py` 可以验证修复效果：

1. 打开对话框后观察任务栏是否出现新图标
2. 使用"显示桌面"功能最小化所有窗口
3. 分别点击任务栏中的不同图标
4. 验证每个窗口都可以独立控制
5. 确认后台任务（计数器）持续运行

## 技术要点

1. **独立窗口创建**：`Toplevel()` 不传入parent参数
2. **任务栏图标设置**：使用 `iconbitmap(default='')` 确保显示图标
3. **窗口属性配置**：`attributes('-topmost', False)` 和 `resizable(True, True)`
4. **grab管理**：合理使用 `grab_set()` 和 `grab_release()` 控制操控性
5. **引用管理**：使用实例变量跟踪窗口状态，防止重复打开

这个解决方案完全解决了Windows环境下的窗口切换问题，提供了与原生Windows应用一致的用户体验。
