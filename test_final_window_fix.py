#!/usr/bin/env python3
"""
最终的窗口切换问题修复测试
演示独立窗口解决方案的效果
"""

import tkinter as tk
from tkinter import ttk, StringVar, IntVar, Frame, Label, Entry, Button, Toplevel, LEFT, RIGHT, X, BOTH, W

class TestFinalWindowFix:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("主程序 - 最终窗口修复测试")
        self.root.geometry("700x500")
        
        # 用于跟踪独立窗口
        self._martin_dialog = None
        self._test_dialog = None
        self._config_dialog = None
        
        self.create_ui()
        
    def create_ui(self):
        main_frame = Frame(self.root, bg='#f5f5f5')
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_frame = Frame(main_frame, bg='#f5f5f5')
        title_frame.pack(fill=X, pady=(0, 20))
        
        Label(title_frame, text="🔧 窗口切换问题最终修复方案", 
              font=("微软雅黑", 18, "bold"), bg='#f5f5f5', fg='#2c3e50').pack()
        Label(title_frame, text="独立任务栏窗口解决方案", 
              font=("微软雅黑", 12), bg='#f5f5f5', fg='#7f8c8d').pack()
        
        # 解决方案说明
        solution_frame = Frame(main_frame, bg='white', relief='raised', bd=1)
        solution_frame.pack(fill=X, pady=(0, 20))
        
        Label(solution_frame, text="💡 解决方案特点：", 
              font=("微软雅黑", 12, "bold"), bg='white', fg='#27ae60').pack(anchor=W, padx=15, pady=(10, 5))
        
        features = [
            "✅ 每个对话框都是独立的任务栏窗口",
            "✅ 可以单独点击任务栏图标进行切换",
            "✅ 不会阻塞主程序的操作",
            "✅ 类似Windows文件夹属性窗口的行为",
            "✅ 解决了'显示桌面'后无法恢复的问题"
        ]
        
        for feature in features:
            Label(solution_frame, text=feature, font=("微软雅黑", 10), 
                  bg='white', fg='#2c3e50').pack(anchor=W, padx=30, pady=2)
        
        Label(solution_frame, text="", bg='white').pack(pady=5)  # 底部间距
        
        # 测试说明
        test_frame = Frame(main_frame, bg='#ecf0f1', relief='raised', bd=1)
        test_frame.pack(fill=X, pady=(0, 20))
        
        Label(test_frame, text="🧪 测试步骤：", 
              font=("微软雅黑", 12, "bold"), bg='#ecf0f1', fg='#e74c3c').pack(anchor=W, padx=15, pady=(10, 5))
        
        steps = [
            "1. 点击下面的按钮打开独立窗口",
            "2. 观察任务栏是否出现新的窗口图标",
            "3. 点击'显示桌面'最小化所有窗口",
            "4. 分别点击任务栏中的不同图标",
            "5. 验证每个窗口都可以独立控制"
        ]
        
        for step in steps:
            Label(test_frame, text=step, font=("微软雅黑", 10), 
                  bg='#ecf0f1', fg='#2c3e50').pack(anchor=W, padx=30, pady=2)
        
        Label(test_frame, text="", bg='#ecf0f1').pack(pady=5)  # 底部间距
        
        # 测试按钮区域
        button_frame = Frame(main_frame, bg='#f5f5f5')
        button_frame.pack(fill=X, pady=20)
        
        # 按钮样式配置
        button_configs = [
            ("打开马丁策略窗口", self.open_martin_window, "#27ae60"),
            ("打开测试参数窗口", self.open_test_window, "#3498db"),
            ("打开交易配置窗口", self.open_config_window, "#f39c12")
        ]
        
        for text, command, color in button_configs:
            btn = Button(button_frame, text=text, command=command,
                        font=("微软雅黑", 11, "bold"), bg=color, fg="white",
                        width=18, height=2, relief='flat')
            btn.pack(pady=8)
            
            # 添加悬停效果
            def on_enter(e, btn=btn, color=color):
                btn.config(bg=self.darken_color(color))
            def on_leave(e, btn=btn, color=color):
                btn.config(bg=color)
            btn.bind("<Enter>", on_enter)
            btn.bind("<Leave>", on_leave)
        
        # 状态显示区域
        status_frame = Frame(main_frame, bg='#34495e', relief='raised', bd=1)
        status_frame.pack(fill=X, pady=(20, 0))
        
        Label(status_frame, text="📊 窗口状态监控", 
              font=("微软雅黑", 12, "bold"), bg='#34495e', fg='white').pack(pady=(10, 5))
        
        self.status_label = Label(status_frame, text="", font=("微软雅黑", 10), 
                                 bg='#34495e', fg='#ecf0f1', justify=LEFT)
        self.status_label.pack(padx=20, pady=(0, 10))
        
        # 定时更新状态
        self.update_status()
        
    def darken_color(self, color):
        """使颜色变暗（简单实现）"""
        color_map = {
            "#27ae60": "#229954",
            "#3498db": "#2980b9", 
            "#f39c12": "#e67e22"
        }
        return color_map.get(color, color)
        
    def update_status(self):
        """更新窗口状态显示"""
        status_lines = []
        
        # 检查各个窗口状态
        windows = [
            ("马丁策略窗口", self._martin_dialog),
            ("测试参数窗口", self._test_dialog),
            ("交易配置窗口", self._config_dialog)
        ]
        
        for name, window in windows:
            if window and window.winfo_exists():
                try:
                    state = window.wm_state()
                    if state == 'normal':
                        status_lines.append(f"🟢 {name}: 正常显示")
                    elif state == 'iconic':
                        status_lines.append(f"🟡 {name}: 已最小化")
                    else:
                        status_lines.append(f"🔵 {name}: {state}")
                except:
                    status_lines.append(f"❓ {name}: 状态未知")
            else:
                status_lines.append(f"⚫ {name}: 已关闭")
        
        self.status_label.config(text="\n".join(status_lines))
        self.root.after(1000, self.update_status)
        
    def center_window_on_screen(self, window):
        """将窗口居中显示在屏幕中央"""
        window.update_idletasks()
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        window_width = window.winfo_reqwidth()
        window_height = window.winfo_reqheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
    def open_martin_window(self):
        """打开马丁策略独立窗口"""
        if self._martin_dialog and self._martin_dialog.winfo_exists():
            self._martin_dialog.lift()
            self._martin_dialog.focus_set()
            return
            
        window = Toplevel()
        window.title("马丁策略参数设置")
        window.geometry("500x600")
        
        try:
            window.iconbitmap(default='')
        except:
            pass
        window.attributes('-topmost', False)
        window.resizable(True, True)
        
        self._martin_dialog = window
        
        def on_close():
            self._martin_dialog = None
            window.destroy()
        window.protocol("WM_DELETE_WINDOW", on_close)
        
        self.center_window_on_screen(window)
        
        # 创建内容
        main_frame = Frame(window, bg='#f8f9fa')
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        Label(main_frame, text="🎯 马丁策略参数设置", 
              font=("微软雅黑", 16, "bold"), bg='#f8f9fa', fg="#27ae60").pack(pady=(0, 20))
        
        Label(main_frame, text="这是一个独立的任务栏窗口", 
              font=("微软雅黑", 12), bg='#f8f9fa').pack(pady=5)
        Label(main_frame, text="可以在任务栏中单独点击切换", 
              font=("微软雅黑", 12), bg='#f8f9fa').pack(pady=5)
        
        # 参数设置区域
        params_frame = Frame(main_frame, bg='white', relief='raised', bd=1)
        params_frame.pack(fill=BOTH, expand=True, pady=20)
        
        Label(params_frame, text="参数设置：", font=("微软雅黑", 12, "bold"), 
              bg='white').pack(anchor=W, padx=15, pady=(15, 10))
        
        for i in range(6):
            frame = Frame(params_frame, bg='white')
            frame.pack(fill=X, padx=15, pady=5)
            Label(frame, text=f"参数 {i+1}:", width=10, anchor=W, bg='white').pack(side=LEFT)
            Entry(frame, width=20).pack(side=LEFT, padx=10)
        
        Label(params_frame, text="", bg='white').pack(pady=10)  # 底部间距
        
        # 按钮
        button_frame = Frame(main_frame, bg='#f8f9fa')
        button_frame.pack(pady=20)
        Button(button_frame, text="确定", command=on_close, 
               bg="#27ae60", fg="white", width=12, font=("微软雅黑", 10, "bold")).pack(side=LEFT, padx=5)
        Button(button_frame, text="取消", command=on_close, 
               bg="#e74c3c", fg="white", width=12, font=("微软雅黑", 10, "bold")).pack(side=LEFT, padx=5)
        
    def open_test_window(self):
        """打开测试参数独立窗口"""
        if self._test_dialog and self._test_dialog.winfo_exists():
            self._test_dialog.lift()
            self._test_dialog.focus_set()
            return
            
        window = Toplevel()
        window.title("测试参数设置")
        window.geometry("400x250")
        
        try:
            window.iconbitmap(default='')
        except:
            pass
        window.attributes('-topmost', False)
        window.resizable(True, True)
        
        self._test_dialog = window
        
        def on_close():
            self._test_dialog = None
            window.destroy()
        window.protocol("WM_DELETE_WINDOW", on_close)
        
        self.center_window_on_screen(window)
        
        # 创建内容
        main_frame = Frame(window, bg='#f8f9fa')
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        Label(main_frame, text="📈 测试参数设置", 
              font=("微软雅黑", 16, "bold"), bg='#f8f9fa', fg="#3498db").pack(pady=(0, 20))
        
        Label(main_frame, text="请输入反弹/回调百分比：", 
              font=("微软雅黑", 12), bg='#f8f9fa').pack(pady=10)
        
        entry_frame = Frame(main_frame, bg='#f8f9fa')
        entry_frame.pack(pady=10)
        percent_var = StringVar(value="5")
        entry = Entry(entry_frame, textvariable=percent_var, width=10, font=("微软雅黑", 12))
        entry.pack(side=LEFT, padx=5)
        Label(entry_frame, text="%", font=("微软雅黑", 12), bg='#f8f9fa').pack(side=LEFT)
        
        Button(main_frame, text="开始测试", command=on_close, 
               bg="#3498db", fg="white", width=15, height=2, 
               font=("微软雅黑", 11, "bold")).pack(pady=30)
        
    def open_config_window(self):
        """打开配置独立窗口"""
        if self._config_dialog and self._config_dialog.winfo_exists():
            self._config_dialog.lift()
            self._config_dialog.focus_set()
            return
            
        window = Toplevel()
        window.title("交易配置")
        window.geometry("450x350")
        
        try:
            window.iconbitmap(default='')
        except:
            pass
        window.attributes('-topmost', False)
        window.resizable(True, True)
        
        self._config_dialog = window
        
        def on_close():
            self._config_dialog = None
            window.destroy()
        window.protocol("WM_DELETE_WINDOW", on_close)
        
        self.center_window_on_screen(window)
        
        # 创建内容
        main_frame = Frame(window, bg='#f8f9fa')
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        Label(main_frame, text="⚙️ 交易配置", 
              font=("微软雅黑", 16, "bold"), bg='#f8f9fa', fg="#f39c12").pack(pady=(0, 20))
        
        # 配置选项
        config_frame = Frame(main_frame, bg='white', relief='raised', bd=1)
        config_frame.pack(fill=BOTH, expand=True)
        
        Label(config_frame, text="配置选项：", font=("微软雅黑", 12, "bold"), 
              bg='white').pack(anchor=W, padx=15, pady=(15, 10))
        
        # 杠杆设置
        leverage_frame = Frame(config_frame, bg='white')
        leverage_frame.pack(fill=X, padx=15, pady=5)
        Label(leverage_frame, text="杠杆倍数:", width=12, anchor=W, bg='white').pack(side=LEFT)
        leverage_var = StringVar(value="10")
        Entry(leverage_frame, textvariable=leverage_var, width=10).pack(side=LEFT, padx=10)
        
        # 保证金模式
        margin_frame = Frame(config_frame, bg='white')
        margin_frame.pack(fill=X, padx=15, pady=5)
        Label(margin_frame, text="保证金模式:", width=12, anchor=W, bg='white').pack(side=LEFT)
        margin_var = StringVar(value="全仓")
        ttk.Combobox(margin_frame, textvariable=margin_var, 
                     values=["全仓", "逐仓"], width=10).pack(side=LEFT, padx=10)
        
        # 交易对
        symbol_frame = Frame(config_frame, bg='white')
        symbol_frame.pack(fill=X, padx=15, pady=5)
        Label(symbol_frame, text="交易对:", width=12, anchor=W, bg='white').pack(side=LEFT)
        symbol_var = StringVar(value="BTCUSDT")
        Entry(symbol_frame, textvariable=symbol_var, width=15).pack(side=LEFT, padx=10)
        
        Label(config_frame, text="", bg='white').pack(pady=15)  # 底部间距
        
        # 按钮
        button_frame = Frame(main_frame, bg='#f8f9fa')
        button_frame.pack(pady=20)
        Button(button_frame, text="保存配置", command=on_close, 
               bg="#f39c12", fg="white", width=12, font=("微软雅黑", 10, "bold")).pack(side=LEFT, padx=5)
        Button(button_frame, text="取消", command=on_close, 
               bg="#95a5a6", fg="white", width=12, font=("微软雅黑", 10, "bold")).pack(side=LEFT, padx=5)
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    print("启动独立窗口测试程序...")
    print("请按照界面上的测试步骤进行验证")
    app = TestFinalWindowFix()
    app.run()
