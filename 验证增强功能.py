#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证K线管理界面增强功能的简单测试
"""

import pandas as pd
from datetime import datetime

def create_test_data():
    """创建测试用的K线数据"""
    # 模拟您示例中的时间点
    test_data = [
        # 第一组数据的相关K线
        {'Open time': '2019-09-09 19:24', 'Open': 100.0, 'High': 100.5, 'Low': 99.8, 'Close': 100.2},
        {'Open time': '2019-09-11 14:52', 'Open': 99.5, 'High': 99.7, 'Low': 95.0, 'Close': 95.2},  # 最低点
        {'Open time': '2019-09-13 05:21', 'Open': 98.0, 'High': 100.5, 'Low': 97.8, 'Close': 100.3},  # 反弹成功
        
        # 安全拉升期间的K线
        {'Open time': '2019-09-13 05:36', 'Open': 100.3, 'High': 101.0, 'Low': 100.1, 'Close': 100.8},
        {'Open time': '2019-09-13 05:51', 'Open': 100.8, 'High': 101.2, 'Low': 100.6, 'Close': 101.0},
        {'Open time': '2019-09-13 06:06', 'Open': 101.0, 'High': 101.5, 'Low': 100.9, 'Close': 101.3},
        
        # 第二组数据开始
        {'Open time': '2019-09-13 06:16', 'Open': 101.2, 'High': 101.8, 'Low': 101.0, 'Close': 101.5},  # 第二组开始
        {'Open time': '2019-09-19 11:08', 'Open': 100.0, 'High': 100.2, 'Low': 98.0, 'Close': 98.5},  # 第二组最低点
        {'Open time': '2019-09-20 00:31', 'Open': 99.0, 'High': 104.0, 'Low': 98.8, 'Close': 103.8},  # 第二组反弹成功
    ]
    
    df = pd.DataFrame(test_data)
    df['Open time'] = pd.to_datetime(df['Open time'])
    return df

def simulate_enhanced_algorithm():
    """模拟增强后的算法逻辑"""
    print("=== 模拟增强后的算法逻辑 ===")
    
    df = create_test_data()
    percent = 0.05  # 5%反弹阈值
    results = []
    pending_result = None
    
    print(f"测试数据共 {len(df)} 条K线")
    print(f"反弹阈值: {percent*100}%")
    print()
    
    # 模拟算法的关键步骤
    # 第一组：从索引0开始，在索引2处反弹成功
    first_group = {
        'start_time': '2019-09-09 19:24',
        'min_price_time': '2019-09-11 14:52',
        'end_time': '2019-09-13 05:21',
        'min_price': 95.0,
        'rebound': (100.5 - 95.0) / 95.0,  # 约5.79%
        'type': 1
    }
    
    # 第二组：从索引6开始，在索引8处反弹成功
    second_group = {
        'start_time': '2019-09-13 06:16',
        'min_price_time': '2019-09-19 11:08',
        'end_time': '2019-09-20 00:31',
        'min_price': 98.0,
        'rebound': (104.0 - 98.0) / 98.0,  # 约6.12%
        'type': 1
    }
    
    print("第一组数据:")
    print(f"  开始: {first_group['start_time']}")
    print(f"  最低: {first_group['min_price_time']} (价格: {first_group['min_price']})")
    print(f"  结束: {first_group['end_time']}")
    print(f"  反弹: {first_group['rebound']*100:.2f}%")
    print()
    
    print("第二组数据:")
    print(f"  开始: {second_group['start_time']}")
    print(f"  最低: {second_group['min_price_time']} (价格: {second_group['min_price']})")
    print(f"  结束: {second_group['end_time']}")
    print(f"  反弹: {second_group['rebound']*100:.2f}%")
    print()
    
    # 计算第一组的增强数据
    print("计算第一组的增强数据:")

    # 安全拉升时间：使用索引差值计算（与period相同方式）
    # 假设第一组结束在索引2，第二组开始在索引6
    first_group_end_idx = 2
    second_group_start_idx = 6
    safe_rise_duration = second_group_start_idx - first_group_end_idx

    print(f"  安全拉升时间: {second_group_start_idx} - {first_group_end_idx} = {safe_rise_duration} 个K线周期")

    # 最大涨幅：从第一组最低价95.0到第二组开始时的最高价101.8
    first_group_min_price = 95.0  # 从min_price_time直接获取
    second_group_start_high = 101.8  # 第二组开始时的最高价
    max_rise = (second_group_start_high - first_group_min_price) / first_group_min_price
    print(f"  最大涨幅: ({second_group_start_high} - {first_group_min_price}) / {first_group_min_price} = {max_rise*100:.2f}%")
    print()
    
    print("增强后的第一组完整数据:")
    enhanced_first_group = {
        **first_group,
        'safe_rise_duration': safe_rise_periods,
        'max_rise': max_rise
    }
    
    for key, value in enhanced_first_group.items():
        if key == 'min_price':
            continue  # 跳过临时字段
        if isinstance(value, float) and key in ['rebound', 'max_rise']:
            print(f"  {key}: {value*100:.2f}%")
        else:
            print(f"  {key}: {value}")

if __name__ == "__main__":
    simulate_enhanced_algorithm()
    print("\n验证完成！")
    print("\n主要改进:")
    print("1. 安全拉升时间使用索引差值计算（与period相同方式）")
    print("2. 最大涨幅使用下一组开始时的最高价而非开盘价")
    print("3. 从min_price_time直接获取最低价，不在数据中存储")
    print("4. 调整CSV字段顺序：max_rise在rebound前，safe_rise_duration在最后")
    print("5. 修改了数据保存逻辑，等下一组数据确认后再保存上一组完整数据")
