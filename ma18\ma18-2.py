import sys
import time
import os
import json
import threading
import traceback
import logging
import csv
import concurrent.futures
from datetime import datetime, timedelta
from decimal import Decimal, ROUND_HALF_EVEN
from tkinter import Tk, Frame, Button, Text, END, Scrollbar, VERTICAL, RIGHT, Y, LEFT, BOTH, TOP, Label, Entry, StringVar, ttk, messagebox, X, Toplevel, IntVar, Listbox, BOTTOM, SUNKEN, W, E, CENTER, DISABLED, NORMAL, PanedWindow
import tkinter.font as tkfont
from binance.um_futures import UMFutures
from dotenv import load_dotenv
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import mplfinance as mpf
import pandas as pd
import matplotlib.font_manager as fm
import numpy as np
import math
from matplotlib.ticker import FuncFormatter
import matplotlib.dates as mdates
from matplotlib.ticker import MaxNLocator
import ntplib
import subprocess
import ctypes
import matplotlib.patches as patches
from matplotlib.font_manager import FontProperties
import calendar

# 添加全局时间管理类
class TimeManager:
    def __init__(self):
        self.time_labels = []
        self.countdown_labels = {}  # 存储倒计时标签和对应的K线间隔及收盘时间
        self.running = False
        self.time_update_thread = None
        
    def add_time_label(self, label):
        """添加需要更新时间的标签"""
        if label not in self.time_labels:
            self.time_labels.append(label)
            # 立即更新一次
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            label.after(0, lambda: label.config(text=f" {current_time}"))
    
    def remove_time_label(self, label):
        """移除标签"""
        try:
            if label in self.time_labels:
                self.time_labels.remove(label)
        except Exception as e:
            logging.error(f"移除时间标签失败: {str(e)}")
    
    def add_countdown_label(self, label, interval):
        """添加K线倒计时标签
        label: 显示倒计时的标签
        interval: K线间隔，例如 '1m', '5m', '1h', '4h', '1d' 等
        """
        # 清空现有的所有倒计时标签
        self.countdown_labels = {}
        # 添加新标签
        self.countdown_labels[label] = {
            'interval': interval,
        }
        # 立即更新一次
        self.update_countdown_label(label)
    
    def remove_countdown_label(self):
        """移除倒计时标签"""
        try:
            # 直接清空所有标签
            self.countdown_labels = {}
        except Exception as e:
            logging.error(f"移除倒计时标签失败: {str(e)}")
    
    def update_countdown_label(self, label):
        """更新倒计时标签"""
        try:
            if label not in self.countdown_labels:
                return
                
            # 检查标签是否有效（matplotlib Text对象没有winfo_exists方法）
            try:
                # 尝试获取文本，如果失败说明对象无效
                current_text = label.get_text()
            except:
                # 如果标签无效，移除它
                self.remove_countdown_label()
                return
                
            label_info = self.countdown_labels[label]
            interval = label_info['interval']
            current_time = datetime.now()
            next_kline_time = self._calculate_next_kline_time(current_time, interval)
            
            # 计算剩余时间
            time_diff = next_kline_time - current_time
            total_seconds = int(time_diff.total_seconds())
            # print(current_time,total_seconds)
            
            # 确保不显示负数
            if total_seconds < 0:
                total_seconds = 0
                
            # 格式化显示
            if total_seconds <= 3600:  # 1小时以内
                countdown_text = f"{total_seconds // 60:02d}:{total_seconds % 60:02d}"
            elif total_seconds <= 86400:  # 24小时以内
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                seconds = total_seconds % 60
                countdown_text = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
            else:  # 24小时以上
                days = total_seconds // 86400
                hours = (total_seconds % 86400) // 3600
                countdown_text = f"{days}D:{hours:02d}h"
            
            # 如果文本包含换行符，说明是组合标签
            if '\n' in current_text:
                # 分割文本，保留第一行（价格），更新第二行（倒计时）
                price_text = current_text.split('\n')[0]
                new_text = f"{price_text}\n{countdown_text}"
                label.set_text(new_text)
            else:
                # 单独的倒计时标签
                label.set_text(countdown_text)
            
        except Exception as e:
            logging.error(f"更新倒计时失败: {str(e)}")
            
            # 尝试保持原有价格文本
            try:
                current_text = label.get_text()
                if '\n' in current_text:
                    price_text = current_text.split('\n')[0]
                    label.set_text(f"{price_text}\n--:--")
                else:
                    label.set_text("--:--")
            except:
                # 如果无法获取原有文本，直接设置为错误提示
                label.set_text("--:--")
    
    def _calculate_next_kline_time(self, base_time, interval):
        """计算下一个K线的开始时间"""
        if interval.endswith('m'):
            minutes = int(interval[:-1])
            current_minute = base_time.minute
            current_second = base_time.second
            elapsed_seconds = current_minute * 60 + current_second
            elapsed_intervals = elapsed_seconds // (minutes * 60)
            next_kline_time = base_time.replace(
                minute=((elapsed_intervals + 1) * minutes) % 60,
                second=0,
                microsecond=0
            )
            if next_kline_time <= base_time:
                next_kline_time = next_kline_time + timedelta(hours=1)
                
        elif interval.endswith('h'):
            hours = int(interval[:-1])
            current_hour = base_time.hour
            elapsed_intervals = current_hour // hours
            next_kline_time = base_time.replace(
                hour=((elapsed_intervals + 1) * hours) % 24,
                minute=0,
                second=0,
                microsecond=0
            )
            if next_kline_time <= base_time:
                next_kline_time = next_kline_time + timedelta(days=1)
                
        elif interval == '1d':
            next_kline_time = (base_time + timedelta(days=1)).replace(
                hour=0,
                minute=0,
                second=0,
                microsecond=0
            )
        elif interval == '1w':
            # 下一个周K线的开始时间（每周一08:00）
            weekday = base_time.weekday()
            base = base_time.replace(hour=8, minute=0, second=0, microsecond=0) - timedelta(days=weekday)
            if base_time < base:
                next_kline_time = base
            else:
                weeks = ((base_time - base).days // 7) + 1
                next_kline_time = base + timedelta(weeks=weeks)
        else:
            # 默认情况，直接加一分钟
            next_kline_time = base_time + timedelta(minutes=1)
            
        return next_kline_time
    
    def start(self):
        """启动时间更新"""
        if not self.running:
            self.running = True
            self.time_update_thread = threading.Thread(target=self.time_update_loop, daemon=True)
            self.time_update_thread.start()
            
    def stop(self):
        """停止时间更新线程"""
        self.running = False
        if self.time_update_thread:
            self.time_update_thread.join(timeout=1)
            self.time_update_thread = None
    
    def time_update_loop(self):
        """时间更新循环"""
        while self.running:
            try:
                # 更新系统时间
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                # 更新所有时间标签（tkinter组件）
                for label in self.time_labels[:]:  # 使用切片创建副本进行迭代
                    try:
                        if hasattr(label, 'winfo_exists') and label.winfo_exists():
                            # 捕获当前循环变量值
                            current_label = label
                            label.after(0, lambda l=current_label: l.config(text=f" {current_time}"))
                        else:
                            self.time_labels.remove(label)
                    except Exception as e:
                        logging.error(f"更新时间标签失败: {str(e)}")
                        self.time_labels.remove(label)
                
                # 更新所有倒计时标签（可能是matplotlib Text对象）
                # for label in list(self.countdown_labels.keys()):
                #     try:
                #         # 对于matplotlib Text对象，直接尝试调用update_countdown_label
                #         # 异常处理已经在方法内部
                #         current_label = label
                #         if hasattr(label, 'after'):  # tkinter对象
                #             label.after(0, lambda l=current_label: self.update_countdown_label(l))
                #         else:  # matplotlib Text对象或其他
                #             self.update_countdown_label(current_label)
                #     except Exception as e:
                #         logging.error(f"更新倒计时标签循环中失败: {str(e)}")
                #         self.remove_countdown_label()
                        
            except Exception as e:
                logging.error(f"时间更新失败: {str(e)}")
            
            # 每秒更新一次
            time.sleep(1)

def start_time_update(widget):
    """添加时间标签到更新列表"""
    # 检查time_manager是否已经初始化
    if 'time_manager' in globals() and time_manager is not None:
        time_manager.add_time_label(widget)
    else:
        # 如果time_manager还未初始化，延迟添加
        def delayed_add():
            if 'time_manager' in globals() and time_manager is not None:
                time_manager.add_time_label(widget)
            else:
                # 如果还是没有，再次延迟
                widget.after(500, delayed_add)
        widget.after(500, delayed_add)

def validate_time_input(char, max_length):
    """验证时间输入：只允许数字，并限制长度"""
    if char.isdigit() and len(char) <= max_length:
        return True
    elif char == "":  # 允许删除
        return True
    else:
        return False

# 检查管理员权限并尝试提升权限
def is_admin():
    """检查程序是否以管理员权限运行"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

# 如果不是以管理员权限运行，则尝试重新以管理员权限启动
# if not is_admin():
#     try:
#         # 使用 shell32.dll 的 ShellExecuteW 函数以管理员权限重新启动程序
#         ctypes.windll.shell32.ShellExecuteW(
#             None, "runas", sys.executable, " ".join(sys.argv), None, 1
#         )
#         # 退出当前程序
#         sys.exit(0)
#     except Exception as e:
#         print(f"获取管理员权限失败: {str(e)}")
#         # 继续执行，但可能在需要管理员权限的操作时会失败


# ===================== 文件目录与初始化 ======================
ROOT_DIR = os.getcwd()
CONFIG_FILE = os.path.join(ROOT_DIR, 'config.json')
CURRENT_ORDERS_FILE = os.path.join(ROOT_DIR, 'current_orders.json')
POSITIONS_FILE = os.path.join(ROOT_DIR, 'positions.json')
HISTORY_ORDERS_FILE = os.path.join(ROOT_DIR, 'history_orders.json')
LOG_FILE = os.path.join(ROOT_DIR, 'martingale_log.txt')
KLINE_DATA_DIR = os.path.join(ROOT_DIR, 'kline_data')  # K线数据存储目录

# 确保K线数据目录存在
if not os.path.exists(KLINE_DATA_DIR):
    os.makedirs(KLINE_DATA_DIR)

# 交易对数据存储文件
TRADING_SYMBOLS_FILE = os.path.join(ROOT_DIR, 'trading_symbols.json')

# 全局交易对管理类
class TradingSymbolsManager:
    """交易对管理器，负责获取、存储和管理所有交易对信息"""

    def __init__(self, client):
        self.client = client
        self.tradable_symbols = []  # 可交易的交易对
        self.non_tradable_symbols = []  # 不可交易的交易对（已下架等）
        self.priority_coins = ['BTC', 'ETH', 'SOL', 'BNB', 'XRP', 'DOT', 'ADA']

    def load_symbols_from_api(self, timeout=3):
        """从API获取交易对信息，带超时控制"""
        import concurrent.futures
        import threading
        import requests

        def fetch_exchange_info():
            """获取交易所信息的内部函数"""
            try:
                return self.client.exchange_info()
            except Exception as e:
                logging.error(f"API获取交易对失败: {str(e)}")
                return None

        try:
            # 使用ThreadPoolExecutor实现超时控制
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(fetch_exchange_info)
                try:
                    info = future.result(timeout=timeout)
                    if info is None:
                        return False

                    # 解析交易对信息
                    tradable_symbols = []
                    non_tradable_symbols = []

                    for symbol_info in info['symbols']:
                        # 获取所有USDT合约（永续和交割）
                        # 包括以USDT结尾的永续合约和包含USDT的交割合约
                        symbol = symbol_info['symbol']
                        if 'USDT' in symbol and (symbol.endswith('USDT') or '_' in symbol):
                            symbol_data = {
                                'symbol': symbol,
                                'status': symbol_info['status'],
                                'contractType': symbol_info['contractType'],
                                'deliveryDate': symbol_info.get('deliveryDate', 0),
                                'onboardDate': symbol_info.get('onboardDate', 0)
                            }

                            # 根据状态分类
                            if symbol_info['status'] == 'TRADING':
                                tradable_symbols.append(symbol_data)
                            else:
                                non_tradable_symbols.append(symbol_data)

                    # 对交易对进行排序（永续合约在前，交割合约在后）
                    self.tradable_symbols = self._sort_symbols_with_contracts(tradable_symbols)
                    self.non_tradable_symbols = self._sort_symbols_with_contracts(non_tradable_symbols)

                    # 获取历史交割合约并添加到不可交易列表
                    historical_contracts = self._fetch_historical_delivery_contracts()
                    if historical_contracts:
                        # 将历史合约添加到不可交易列表的末尾
                        self.non_tradable_symbols.extend(historical_contracts)
                        # logging.info(f"添加了 {len(historical_contracts)} 个历史交割合约")

                    total_symbols = len(self.tradable_symbols) + len(self.non_tradable_symbols)
                    tradable_perpetual = [s for s in self.tradable_symbols if not '_' in s]
                    tradable_delivery = [s for s in self.tradable_symbols if '_' in s]
                    non_tradable_perpetual = [s for s in self.non_tradable_symbols if not '_' in s]
                    non_tradable_delivery = [s for s in self.non_tradable_symbols if '_' in s]

                    logging.info(f"从API获取了 {total_symbols} 个交易对：")
                    logging.info(f"  可交易: {len(self.tradable_symbols)} 个 (永续: {len(tradable_perpetual)}, 交割: {len(tradable_delivery)})")
                    logging.info(f"  不可交易: {len(self.non_tradable_symbols)} 个 (永续: {len(non_tradable_perpetual)}, 交割: {len(non_tradable_delivery)})")
                    return True

                except concurrent.futures.TimeoutError:
                    logging.error(f"API获取交易对超时（{timeout}秒）")
                    return False

        except Exception as e:
            logging.error(f"获取交易对时发生错误: {str(e)}")
            return False

    def _sort_symbols(self, symbols):
        """对交易对进行排序，优先显示主流币种"""
        def get_sort_key(symbol):
            # 移除USDT后缀
            base_coin = symbol.replace('USDT', '')
            # 如果是优先币种，返回其在列表中的索引
            if base_coin in self.priority_coins:
                return (0, self.priority_coins.index(base_coin))
            # 其他币种按字母顺序排序
            return (1, base_coin)

        return sorted(symbols, key=get_sort_key)

    def _sort_symbols_with_contracts(self, symbol_data_list):
        """对交易对进行排序，永续合约在前，交割合约在后"""
        perpetual_contracts = []
        delivery_contracts = []

        # 分离永续合约和交割合约
        for symbol_data in symbol_data_list:
            if symbol_data['contractType'] == 'PERPETUAL':
                perpetual_contracts.append(symbol_data['symbol'])
            else:
                delivery_contracts.append(symbol_data)

        # 对永续合约进行排序
        sorted_perpetual = self._sort_symbols(perpetual_contracts)

        # 对交割合约进行排序（按交割日期和基础资产排序）
        sorted_delivery = self._sort_delivery_contracts(delivery_contracts)

        # 返回排序后的符号列表：永续合约在前，交割合约在后
        return sorted_perpetual + sorted_delivery

    def _sort_delivery_contracts(self, delivery_contracts):
        """对交割合约进行排序"""
        def get_delivery_sort_key(symbol_data):
            symbol = symbol_data['symbol']
            delivery_date = symbol_data['deliveryDate']
            contract_type = symbol_data['contractType']

            # 提取基础资产（去掉USDT和日期后缀）
            if '_' in symbol:
                base_part = symbol.split('_')[0].replace('USDT', '')
            else:
                base_part = symbol.replace('USDT', '')

            # 优先级排序：主流币种优先
            if base_part in self.priority_coins:
                priority = (0, self.priority_coins.index(base_part))
            else:
                priority = (1, base_part)

            # 合约类型排序：CURRENT_QUARTER < NEXT_QUARTER < 其他
            contract_priority = {
                'CURRENT_QUARTER': 0,
                'NEXT_QUARTER': 1
            }.get(contract_type, 2)

            return (priority[0], priority[1], contract_priority, delivery_date)

        sorted_contracts = sorted(delivery_contracts, key=get_delivery_sort_key)
        return [contract['symbol'] for contract in sorted_contracts]

    def _fetch_historical_delivery_contracts(self):
        """获取历史交割合约列表"""
        try:
            import requests
            from datetime import datetime

            historical_contracts = []

            # 主要币种列表
            main_pairs = ['BTCUSDT', 'ETHUSDT']

            for pair in main_pairs:
                try:
                    # 调用delivery_price API获取历史交割信息
                    url = f"https://fapi.binance.com/futures/data/delivery-price?pair={pair}"
                    response = requests.get(url, timeout=3)

                    if response.status_code == 200:
                        delivery_data = response.json()

                        for record in delivery_data:
                            delivery_time = record['deliveryTime']
                            dt = datetime.fromtimestamp(delivery_time/1000)

                            # 生成历史合约名称
                            year = dt.year
                            month = dt.month
                            day = dt.day

                            # 格式化为YYMMDD
                            contract_suffix = f"{str(year)[2:]}{month:02d}{day:02d}"
                            historical_symbol = f"{pair}_{contract_suffix}"

                            # 只添加过去的合约（已交割的）
                            if dt < datetime.now():
                                historical_contracts.append(historical_symbol)

                except Exception as e:
                    logging.warning(f"获取 {pair} 历史交割合约失败: {str(e)}")
                    continue

            # 去重并排序
            historical_contracts = list(set(historical_contracts))
            historical_contracts.sort(reverse=True)  # 最新的在前

            # logging.info(f"成功获取 {len(historical_contracts)} 个历史交割合约")
            return historical_contracts

        except Exception as e:
            logging.error(f"获取历史交割合约时发生错误: {str(e)}")
            return []

    def _is_historical_contract(self, symbol):
        """判断是否为历史交割合约"""
        if '_' not in symbol:
            return False

        try:
            from datetime import datetime
            # 提取日期部分
            date_part = symbol.split('_')[1]
            if len(date_part) == 6:  # YYMMDD格式
                year = int('20' + date_part[:2])
                month = int(date_part[2:4])
                day = int(date_part[4:6])

                contract_date = datetime(year, month, day)
                return contract_date < datetime.now()
        except:
            pass

        return False

    def save_symbols_to_local(self):
        """将交易对信息保存到本地文件"""
        try:
            # 统计合约类型
            tradable_perpetual = [s for s in self.tradable_symbols if not '_' in s]
            tradable_delivery = [s for s in self.tradable_symbols if '_' in s]
            non_tradable_perpetual = [s for s in self.non_tradable_symbols if not '_' in s]
            non_tradable_delivery = [s for s in self.non_tradable_symbols if '_' in s]

            # 进一步区分当前交割合约和历史交割合约
            current_delivery = [s for s in non_tradable_delivery if not self._is_historical_contract(s)]
            historical_delivery = [s for s in non_tradable_delivery if self._is_historical_contract(s)]

            data = {
                'tradable_symbols': self.tradable_symbols,
                'non_tradable_symbols': self.non_tradable_symbols,
                'contract_statistics': {
                    'tradable_perpetual_count': len(tradable_perpetual),
                    'tradable_delivery_count': len(tradable_delivery),
                    'non_tradable_perpetual_count': len(non_tradable_perpetual),
                    'non_tradable_current_delivery_count': len(current_delivery),
                    'non_tradable_historical_delivery_count': len(historical_delivery),
                    'total_non_tradable_delivery_count': len(non_tradable_delivery)
                },
                'last_updated': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            with open(TRADING_SYMBOLS_FILE, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            logging.info(f"交易对信息已保存到本地文件: {TRADING_SYMBOLS_FILE}")
            return True

        except Exception as e:
            logging.error(f"保存交易对信息到本地失败: {str(e)}")
            return False

    def load_symbols_from_local(self):
        """从本地文件加载交易对信息"""
        try:
            if os.path.exists(TRADING_SYMBOLS_FILE):
                with open(TRADING_SYMBOLS_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 兼容旧格式
                if 'all_symbols' in data:
                    # 旧格式：转换为新格式
                    self.tradable_symbols = data.get('tradable_symbols', [])
                    all_symbols = data.get('all_symbols', [])
                    self.non_tradable_symbols = [s for s in all_symbols if s not in self.tradable_symbols]
                else:
                    # 新格式
                    self.tradable_symbols = data.get('tradable_symbols', [])
                    self.non_tradable_symbols = data.get('non_tradable_symbols', [])

                last_updated = data.get('last_updated', '未知')
                total_symbols = len(self.tradable_symbols) + len(self.non_tradable_symbols)

                # 统计合约类型
                tradable_perpetual = [s for s in self.tradable_symbols if not '_' in s]
                tradable_delivery = [s for s in self.tradable_symbols if '_' in s]
                non_tradable_perpetual = [s for s in self.non_tradable_symbols if not '_' in s]
                non_tradable_delivery = [s for s in self.non_tradable_symbols if '_' in s]

                logging.info(f"从本地文件加载了 {total_symbols} 个交易对（更新时间: {last_updated}）：")
                logging.info(f"  可交易: {len(self.tradable_symbols)} 个 (永续: {len(tradable_perpetual)}, 交割: {len(tradable_delivery)})")
                logging.info(f"  不可交易: {len(self.non_tradable_symbols)} 个 (永续: {len(non_tradable_perpetual)}, 交割: {len(non_tradable_delivery)})")
                return True
            else:
                logging.info("本地交易对文件不存在")
                return False

        except Exception as e:
            logging.error(f"从本地文件加载交易对信息失败: {str(e)}")
            return False

    def get_symbols(self, tradable_only=True):
        """获取交易对列表
        Args:
            tradable_only: True返回可交易的交易对，False返回所有交易对（可交易的在前，不可交易的在后）
        """
        if tradable_only:
            return self.tradable_symbols if self.tradable_symbols else ['BTCUSDT']
        else:
            # 返回所有交易对：可交易的在前，不可交易的在后，不重新排序
            all_symbols = self.tradable_symbols + self.non_tradable_symbols
            return all_symbols if all_symbols else ['BTCUSDT']

    def refresh_symbols(self):
        """刷新交易对信息：先尝试从API获取，失败则从本地加载，都失败则使用默认值"""
        # 先尝试从API获取
        if self.load_symbols_from_api():
            # API获取成功，保存到本地
            self.save_symbols_to_local()
            return True
        else:
            # API获取失败，尝试从本地加载
            logging.warning("API获取交易对失败，尝试从本地文件加载")
            if self.load_symbols_from_local():
                return True
            else:
                # 本地也没有，使用默认值
                logging.warning("本地文件也无法加载，使用默认交易对BTCUSDT")
                self.tradable_symbols = ['BTCUSDT']
                self.non_tradable_symbols = []
                return False

# SymbolConfig 类 - 用于管理交易对配置信息
class SymbolConfig:
    def __init__(self):
        self.config_dir = "symbol_configs"
        self.config_file = os.path.join(ROOT_DIR, self.config_dir, "symbol_configs.json")
        self.ensure_config_dir()
        self.configs = self.load_configs()
    
    def ensure_config_dir(self):
        """确保配置目录存在"""
        config_path = os.path.join(ROOT_DIR, self.config_dir)
        if not os.path.exists(config_path):
            os.makedirs(config_path)
            logging.info(f"创建交易对配置目录: {config_path}")
    
    def load_configs(self):
        """加载所有交易对配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                logging.error(f"加载交易对配置文件失败: {str(e)}")
                return {}
        else:
            return {}
    
    def save_configs(self):
        """保存所有交易对配置"""
        try:
            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(self.configs, f, indent=2, ensure_ascii=False)
            logging.info("交易对配置文件已保存")
        except Exception as e:
            logging.error(f"保存交易对配置文件失败: {str(e)}")
    
    def get_symbol_config(self, symbol):
        """获取特定交易对的配置"""
        if symbol in self.configs:
            return self.configs[symbol]
        else:
            # 返回默认配置
            return {}
    
    def update_symbol_config(self, symbol, config_data):
        """更新特定交易对的配置"""
        if symbol not in self.configs:
            self.configs[symbol] = {}

        # 记录更新时间
        self.configs[symbol]["lastUpdated"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 更新配置
        for key, value in config_data.items():
            self.configs[symbol][key] = value
        
        # 保存到文件
        self.save_configs()
        return self.configs[symbol]
    
    def update_symbol_precision(self, symbol, exchange_info=None, client=None):
        """从交易所信息更新交易对精度信息

        返回:
            (success, error_message): 成功标志和错误信息
        """
        try:
            # 第一个API：获取交易所信息
            if exchange_info is None and client is not None:
                logging.info(f"正在获取交易规则信息...")
                # 使用超时控制获取交易所信息
                exchange_info, success = self._api_call_with_timeout(
                    client.exchange_info,
                    timeout=3.0
                )
                if not success:
                    error_msg = "获取交易所信息失败或超时，请检查网络连接"
                    logging.error(error_msg)
                    return False, error_msg
                logging.info(f"已获取交易规则信息")

            if exchange_info is None:
                error_msg = "无法获取交易所信息"
                logging.error(error_msg)
                return False, error_msg

            # 记录数据结构，便于调试
            logging.debug(f"exchangeInfo 数据结构: {json.dumps(exchange_info, indent=2)}")

            found = False
            for symbol_info in exchange_info.get('symbols', []):
                if symbol_info.get('symbol') == symbol:
                    found = True
                    config_data = {}

                    # 获取过滤器信息，使用tickSize和stepSize作为精度标准
                    for filter_item in symbol_info.get('filters', []):
                        # 价格过滤器 - 使用tickSize作为价格精度
                        if filter_item.get('filterType') == 'PRICE_FILTER':
                            config_data['tickSize'] = float(filter_item.get('tickSize', '0.00001'))
                            config_data['minPrice'] = float(filter_item.get('minPrice', '0'))
                            config_data['maxPrice'] = float(filter_item.get('maxPrice', '0'))

                        # 数量过滤器 - 使用stepSize作为数量精度
                        if filter_item.get('filterType') == 'LOT_SIZE':
                            config_data['stepSize'] = float(filter_item.get('stepSize', '0.00001'))
                            config_data['minQty'] = float(filter_item.get('minQty', '0'))
                            config_data['maxQty'] = float(filter_item.get('maxQty', '0'))

                        # 最小名义价值
                        if filter_item.get('filterType') == 'MIN_NOTIONAL':
                            config_data['minNotional'] = float(filter_item.get('notional', '0'))

                    # 记录交易对的合约类型
                    config_data['contractType'] = symbol_info.get('contractType', '')

                    # 记录交易对状态
                    config_data['status'] = symbol_info.get('status', '')

                    # 如果有client，继续获取其他信息
                    user_info_failed = False
                    user_info_error = ""

                    if client is not None:
                        # 第二个API：尝试更新最大杠杆信息
                        logging.info(f"正在获取 {symbol} 杠杆信息...")
                        brackets, success = self._api_call_with_timeout(
                            client.leverage_brackets,
                            symbol=symbol,
                            timeout=3.0
                        )
                        if success and brackets and len(brackets) > 0:
                            max_leverage = brackets[0]['brackets'][0]['initialLeverage']
                            config_data['maxLeverage'] = max_leverage
                            logging.info(f"{symbol} 最大杠杆: {max_leverage}x")
                        else:
                            # 检查本地是否已有最大杠杆数据
                            existing_config = self.get_symbol_config(symbol)
                            if 'maxLeverage' not in existing_config or existing_config.get('maxLeverage') is None:
                                # 本地没有数据，保存N/A
                                config_data['maxLeverage'] = 'N/A'
                                logging.warning(f"获取最大杠杆信息失败，保存N/A到本地配置")
                            else:
                                # 本地有数据，保持原值
                                logging.warning(f"获取最大杠杆信息失败，保持本地配置: {existing_config.get('maxLeverage')}")
                            # 标记用户信息获取失败
                            user_info_failed = True
                            user_info_error = "获取最大杠杆信息失败"

                        # 第三个API：尝试获取仓位风险信息（保证金模式和当前杠杆）
                        logging.info(f"正在获取 {symbol} 仓位风险信息...")
                        position_risk, success = self._api_call_with_timeout(
                            client.get_position_risk,
                            symbol=symbol,
                            timeout=3.0
                        )
                        if success and position_risk and len(position_risk) > 0:
                            position_info = position_risk[0]
                            config_data['leverage'] = float(position_info.get('leverage', 1))
                            config_data['marginType'] = position_info.get('marginType', 'CROSSED')
                            config_data['positionSide'] = position_info.get('positionSide', 'BOTH')
                            logging.info(f"{symbol} 当前杠杆: {config_data['leverage']}x, 保证金模式: {config_data['marginType']}, 仓位方向: {config_data['positionSide']}")
                        else:
                            # 检查本地是否已有仓位相关数据
                            existing_config = self.get_symbol_config(symbol)

                            # 检查并设置leverage
                            if 'leverage' not in existing_config or existing_config.get('leverage') is None:
                                config_data['leverage'] = 'N/A'

                            # 检查并设置marginType
                            if 'marginType' not in existing_config or existing_config.get('marginType') is None:
                                config_data['marginType'] = 'N/A'

                            # 检查并设置positionSide
                            if 'positionSide' not in existing_config or existing_config.get('positionSide') is None:
                                config_data['positionSide'] = 'N/A'

                            logging.warning(f"获取仓位风险信息失败，缺失的字段已保存N/A到本地配置")
                            # 标记用户信息获取失败
                            user_info_failed = True
                            if user_info_error:
                                user_info_error += "，获取仓位风险信息失败"
                            else:
                                user_info_error = "获取仓位风险信息失败"

                    # 更新配置
                    self.update_symbol_config(symbol, config_data)
                    logging.info(f"更新 {symbol} 交易规则配置成功: tickSize={config_data.get('tickSize', 'N/A')}, "
                                f"stepSize={config_data.get('stepSize', 'N/A')}, "
                                f"最小下单量={config_data.get('minQty', 'N/A')}")

                    # 检查是否有用户信息获取失败
                    if user_info_failed:
                        error_msg = f"更新用户仓位信息失败：{user_info_error}"
                        return False, error_msg
                    else:
                        return True, "配置更新成功"

            if not found:
                error_msg = f"未找到 {symbol} 的交易所信息"
                logging.warning(error_msg)
                return False, error_msg

            return True, "配置更新成功"

        except Exception as e:
            error_msg = f"更新精度信息失败: {str(e)}"
            logging.error(error_msg)
            traceback.print_exc()
            return False, error_msg
    
    def update_leverage_margin(self, symbol, leverage, margin_type):
        """更新杠杆和保证金模式"""
        config_data = {
            "leverage": leverage,
            "marginType": margin_type
        }
        self.update_symbol_config(symbol, config_data)
        logging.info(f"更新 {symbol} 杠杆={leverage}, 保证金模式={margin_type}")

    @staticmethod
    def get_precision_from_step_size(step_size):
        """根据步长计算精度（小数位数）"""
        import decimal

        # 使用Decimal来避免浮点数精度问题
        try:
            # 将step_size转换为Decimal
            decimal_step = decimal.Decimal(str(step_size))

            # 获取小数位数
            if decimal_step == decimal_step.to_integral_value():
                # 如果是整数，返回0
                return 0
            else:
                # 获取小数部分的位数
                sign, digits, exponent = decimal_step.as_tuple()
                if exponent < 0:
                    return abs(exponent)
                else:
                    return 0
        except (ValueError, decimal.InvalidOperation):
            # 如果转换失败，使用原来的方法作为备选
            step_str = f"{step_size:.20f}".rstrip('0')
            if '.' in step_str:
                return len(step_str) - step_str.index('.') - 1
            return 0

    def get_price_precision(self, symbol):
        """获取价格精度（小数位数）"""
        config = self.get_symbol_config(symbol)
        tick_size = config.get('tickSize', 0.01)
        return self.get_precision_from_step_size(tick_size)

    def get_quantity_precision(self, symbol):
        """获取数量精度（小数位数）"""
        config = self.get_symbol_config(symbol)
        step_size = config.get('stepSize', 0.001)
        return self.get_precision_from_step_size(step_size)

    def _setup_timeout_adapter(self, client, timeout=3.0):
        """设置HTTP请求超时适配器"""
        if not hasattr(client, 'session'):
            return False

        import requests
        # 创建一个临时的适配器来设置超时
        class TimeoutHTTPAdapter(requests.adapters.HTTPAdapter):
            def __init__(self, timeout=3.0, *args, **kwargs):
                self.timeout = timeout
                super().__init__(*args, **kwargs)

            def send(self, request, **kwargs):
                kwargs['timeout'] = self.timeout
                return super().send(request, **kwargs)

        # 保存原始适配器
        original_adapters = {}
        for prefix in ['http://', 'https://']:
            if prefix in client.session.adapters:
                original_adapters[prefix] = client.session.adapters[prefix]

        # 安装超时适配器
        timeout_adapter = TimeoutHTTPAdapter(timeout=timeout)
        client.session.mount('http://', timeout_adapter)
        client.session.mount('https://', timeout_adapter)
        return original_adapters

    def _restore_adapters(self, client, original_adapters):
        """恢复原始HTTP适配器"""
        if not hasattr(client, 'session') or not original_adapters:
            return

        # 恢复原始适配器
        for prefix, adapter in original_adapters.items():
            client.session.mount(prefix, adapter)

    def _api_call_with_timeout(self, api_func, *args, timeout=3.0, **kwargs):
        """执行API调用，带超时控制

        参数:
            api_func: 要调用的API函数
            *args: 传递给API函数的位置参数
            timeout: 超时时间（秒）
            **kwargs: 传递给API函数的关键字参数

        返回:
            (result, success): API调用结果和是否成功的标志
        """
        result = None
        success = False
        original_adapters = None

        # 从api_func中提取client对象
        client = None
        if hasattr(api_func, '__self__'):
            client = api_func.__self__

        try:
            # 设置超时适配器
            if client:
                original_adapters = self._setup_timeout_adapter(client, timeout)

            try:
                # 调用API函数
                result = api_func(*args, **kwargs)
                success = True
            finally:
                # 恢复原始适配器
                if client and original_adapters:
                    self._restore_adapters(client, original_adapters)

        except Exception as e:
            logging.error(f"API调用失败: {str(e)}")
            result = None
            success = False

        return result, success
    

    
    def format_price(self, symbol, price):
        """根据交易对配置格式化价格"""
        try:
            precision = self.get_price_precision(symbol)
            # 使用round函数确保正确的精度，避免浮点数精度问题
            rounded_price = round(float(price), precision)
            return f"{rounded_price:.{precision}f}"
        except Exception as e:
            logging.error(f"格式化价格出错: {str(e)}")
            return str(price)

    def format_quantity(self, symbol, quantity):
        """根据交易对配置格式化数量"""
        try:
            precision = self.get_quantity_precision(symbol)
            # 使用round函数确保正确的精度，避免浮点数精度问题
            rounded_quantity = round(float(quantity), precision)
            return f"{rounded_quantity:.{precision}f}"
        except Exception as e:
            logging.error(f"格式化数量出错: {str(e)}")
            return str(quantity)

def init_json_file(file_path, default_data):
    """如果文件不存在或解析出错，则初始化为默认数据"""
    try:
        with open(file_path, 'r') as f:
            json.load(f)
    except Exception:
        with open(file_path, 'w') as f:
            json.dump(default_data, f, indent=4, ensure_ascii=False)

# 默认配置（这里仅作为示例，现阶段不使用策略下单）
default_config = {
    "enable_strategy": False,
    "preset_orders": []
}

init_json_file(CONFIG_FILE, default_config)
init_json_file(CURRENT_ORDERS_FILE, [])        # 当前挂单记录（设为列表）
init_json_file(POSITIONS_FILE, [])             # 当前仓位记录（列表）
init_json_file(HISTORY_ORDERS_FILE, [])        # 历史订单记录

# ===================== 日志设置 ======================
logger = logging.getLogger()
logger.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler = logging.FileHandler(LOG_FILE, mode='a', encoding='utf-8')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)
stream_handler = logging.StreamHandler(sys.stdout)
stream_handler.setFormatter(formatter)
logger.addHandler(stream_handler)

# ===================== API 相关函数 ======================
def sync_time(client):
    """同步服务器时间，设置客户端时间偏移"""
    try:
        server_time = int(client.time()["serverTime"])
        local_time = int(time.time() * 1000)
        offset = server_time - local_time
        client.timestamp_offset = offset
        logging.info(f"服务器时间：{server_time}，本地时间：{local_time}，时间偏移：{offset}ms")
    except Exception as e:
        logging.error(f"同步时间出错：{e}")

def place_order(client, symbol, side, quantity, price, extra_params=None):
    """下限价订单（开仓）——保留接口，暂未在后台调用"""
    params = {
        'symbol': symbol,
        'side': side,
        'type': 'LIMIT',
        'timeInForce': 'GTC',
        'quantity': quantity,
        'price': price
    }
    if extra_params:
        params.update(extra_params)
    try:
        result = client.new_order(**params)
        logging.info(f"下单成功：{params}，返回：{result}")
        return result.get('orderId')
    except Exception as e:
        logging.error("下单错误：" + str(e))
        return None

def monitor_order(client, symbol, order_id, wait_time=60):
    """轮询检测订单是否成交（接口示例）"""
    start_time = time.time()
    while time.time() - start_time < wait_time:
        try:
            order_info = client.query_order(symbol=symbol, orderId=order_id)
            status = order_info.get('status', '')
            logging.info(f"订单 {order_id} 状态：{status}")
            if status == 'FILLED':
                return order_info
        except Exception as e:
            logging.error("查询订单状态出错：" + str(e))
        time.sleep(1)
    logging.info(f"订单 {order_id} 在 {wait_time} 秒内未成交")
    return None

def cancel_order(client, symbol, order_id):
    """取消未成交订单"""
    try:
        result = client.cancel_order(symbol=symbol, orderId=order_id)
        logging.info(f"取消订单 {order_id} 成功：{result}")
    except Exception as e:
        logging.error("取消订单出错：" + str(e))

def place_close_order(client, symbol, side, quantity, close_price):
    """下平仓单（平仓接口示例）"""
    params = {
        'symbol': symbol,
        'side': side,
        'type': 'LIMIT',
        'timeInForce': 'GTC',
        'quantity': quantity,
        'price': close_price
    }
    try:
        result = client.new_order(**params)
        logging.info(f"平仓单下单成功：{params}，返回：{result}")
        return result
    except Exception as e:
        logging.error("平仓单下单出错：" + str(e))
        return None

# ===================== 本地数据读写函数 ======================
def update_current_orders_from_api(client):
    """调用 API 获取当前挂单并写入 CURRENT_ORDERS_FILE"""
    try:
        # 调用 API 获取当前挂单，使用 get_open_orders 接口
        orders = client.get_orders()  # 改为使用 get_open_orders 并传入 SYMBOL
        with open(CURRENT_ORDERS_FILE, 'w', encoding='utf-8') as f:
            json.dump(orders, f, indent=4, ensure_ascii=False)
        # logging.info("当前挂单数据已更新。")
    except Exception as e:
        logging.error("更新当前挂单数据出错: " + str(e))

def update_positions_from_api(client):
    """调用 API 获取账户信息，并写入 POSITIONS_FILE（取 positions 字段）"""
    try:
        account_info = client.account()
        positions = account_info.get("positions", [])
        with open(POSITIONS_FILE, 'w', encoding='utf-8') as f:
            json.dump(positions, f, indent=4, ensure_ascii=False)
        # logging.info("账户仓位数据已更新。")
    except Exception as e:
        logging.error("更新仓位数据出错: " + str(e))

# ===================== 图形界面 ======================
class MainPage(Frame):
    """主界面：包含四个按钮切换到各个功能页面"""
    def __init__(self, master, switch_page_callback, client):
        super().__init__(master)
        self.switch_page_callback = switch_page_callback
        self.client = client
        self.create_widgets()
        
    def create_widgets(self):
        self.pack(fill=BOTH, expand=True)
        Label(self, text="主菜单", font=("宋体", 18)).pack(pady=20)
        
        # 系统时间显示
        self.time_label = Label(self, text="加载中...", font=("微软雅黑", 10), fg="black")
        self.time_label.pack(pady=2)
        
        # 功能按钮
        Button(self, text="查看日志", width=20,
               command=lambda: self.switch_page_callback("log")).pack(pady=10)
        Button(self, text="查看当前挂单", width=20,
               command=lambda: self.switch_page_callback("orders")).pack(pady=10)
        Button(self, text="查看当前仓位", width=20,
               command=lambda: self.switch_page_callback("positions")).pack(pady=10)
        Button(self, text="K线图表", width=20,
               command=lambda: self.switch_page_callback("kline")).pack(pady=10)
        Button(self, text="K线数据管理", width=20,
               command=lambda: self.switch_page_callback("kline_data")).pack(pady=10)
        
        # 时间同步按钮
        Button(self, text="同步系统时间", width=20, fg="blue", bg="#e6e6e6",
               command=self.sync_time_with_ntplib).pack(pady=10)
    
    def sync_time_with_ntplib(self):
        """使用ntplib同步系统时间"""
        if sync_time_with_ntplib(self.client):
            messagebox.showinfo("同步成功", "使用ntplib成功同步系统时间和交易所时间")
            # 立即更新时间显示
            if hasattr(self, 'time_label') and self.time_label.winfo_exists():
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.time_label.config(text=f" {current_time}")
        else:
            messagebox.showerror("同步失败", "同步系统时间失败，请检查网络连接或尝试手动同步")

class LogPage(Frame):
    """日志显示页面：显示 martingale_log.txt 文件内容，提供手动刷新"""
    def __init__(self, master, switch_page_callback):
        super().__init__(master)
        self.switch_page_callback = switch_page_callback
        self.create_widgets()
        self.refresh_log()
        
    def create_widgets(self):
        self.pack(fill=BOTH, expand=True)
        top_frame = Frame(self)
        top_frame.pack(fill="x", pady=5)
        Button(top_frame, text="返回主菜单", command=lambda: self.switch_page_callback("main")).pack(side="left", padx=5)
        Button(top_frame, text="刷新", command=self.refresh_log).pack(side="left", padx=5)
        self.text = Text(self, wrap='word')
        self.text.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar = Scrollbar(self, orient=VERTICAL, command=self.text.yview)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.text.config(yscrollcommand=scrollbar.set)
        
    def refresh_log(self):
        try:
            with open(LOG_FILE, 'r', encoding='utf-8') as f:
                data = f.read()
        except Exception as e:
            data = f"读取日志出错: {e}"
        self.text.delete("1.0", END)
        self.text.insert(END, data)

class OrdersPage(Frame):
    """当前挂单页面：将 current_orders.json 格式化后显示易读信息"""
    def __init__(self, master, switch_page_callback, client):
        super().__init__(master)
        self.switch_page_callback = switch_page_callback
        self.client = client
        self.create_widgets()
        self.refresh_orders()
        self._orders_update_job = None
        
    def create_widgets(self):
        self.pack(fill=BOTH, expand=True)
        top_frame = Frame(self)
        top_frame.pack(fill="x", pady=5)
        Button(top_frame, text="返回主菜单", command=lambda: self.switch_page_callback("main")).pack(side="left", padx=5)
        Button(top_frame, text="刷新", command=self.refresh_orders).pack(side="left", padx=5)
        self.text = Text(self, wrap='word')
        self.text.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar = Scrollbar(self, orient=VERTICAL, command=self.text.yview)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.text.config(yscrollcommand=scrollbar.set)
        
    def refresh_orders(self):
        try:
            with open(CURRENT_ORDERS_FILE, 'r', encoding='utf-8') as f:
                orders = json.load(f)
            display_text = ""
            if orders:
                # 假设 orders 为列表或字典格式，下面分别处理
                if isinstance(orders, list):
                    for order in orders:
                        # 这里按订单内部字段格式化输出
                        order_id = order.get("orderId", "未知")
                        symbol = order.get("symbol", "")
                        side = order.get("side", "")
                        order_type = order.get("type", "")
                        quantity = order.get("quantity", "")
                        price = order.get("price", "")
                        update_time = order.get("update_time", "")
                        display_text += (f"订单编号: {order_id}\n"
                                         f"  交易对: {symbol}\n"
                                         f"  类型: {order_type} / {side}\n"
                                         f"  数量: {quantity}\n"
                                         f"  价格: {price}\n"
                                         f"  更新时间: {update_time}\n"
                                         "--------------------------\n")
                elif isinstance(orders, dict):
                    for order_id, info in orders.items():
                        params = info.get("order_params", {})
                        symbol = params.get("symbol", "")
                        side = params.get("side", "")
                        order_type = params.get("type", "")
                        quantity = params.get("quantity", "")
                        price = params.get("price", "")
                        update_time = info.get("update_time", "")
                        display_text += (f"订单编号: {order_id}\n"
                                         f"  交易对: {symbol}\n"
                                         f"  类型: {order_type} / {side}\n"
                                         f"  数量: {quantity}\n"
                                         f"  价格: {price}\n"
                                         f"  更新时间: {update_time}\n"
                                         "--------------------------\n")
            else:
                display_text = "当前无挂单记录。"
        except Exception as e:
            display_text = f"读取挂单数据出错: {e}"
        self.text.delete("1.0", END)
        self.text.insert(END, display_text)

    def start_orders_update(self):
        self.stop_orders_update()
        # 设置订单更新线程并启动
        self.orders_update_job = threading.Event()
        self.orders_update_job.set()
        if not hasattr(self, 'orders_update_thread') or not self.orders_update_thread.is_alive():
            self.orders_update_thread = threading.Thread(target=self.orders_update_loop, daemon=True)
            self.orders_update_thread.start()

    def stop_orders_update(self):
        if hasattr(self, 'orders_update_job'):
            self.orders_update_job.clear()

    def orders_update_loop(self):
        """在线程中执行订单更新"""
        while self.orders_update_job.is_set():
            try:
                self.orders_update()
            except Exception as e:
                logging.error(f"更新订单失败")
            time.sleep(2)  # 每2秒更新一次

    def orders_update(self):
        """线程中执行的订单更新操作"""
        try:
            update_current_orders_from_api(self.client)
            # 使用after方法将UI更新操作放回主线程
            self.after(0, self.refresh_orders)
        except Exception as e:
            logging.error(f"更新订单失败")

class PositionsPage(Frame):
    """当前仓位页面：格式化显示 positions.json 中的数据"""
    def __init__(self, master, switch_page_callback, client):
        super().__init__(master)
        self.switch_page_callback = switch_page_callback
        self.client = client
        self.create_widgets()
        self.refresh_positions()
        self._positions_update_job = None
        
    def create_widgets(self):
        self.pack(fill=BOTH, expand=True)
        top_frame = Frame(self)
        top_frame.pack(fill="x", pady=5)
        Button(top_frame, text="返回主菜单", command=lambda: self.switch_page_callback("main")).pack(side="left", padx=5)
        Button(top_frame, text="刷新", command=self.refresh_positions).pack(side="left", padx=5)
        self.text = Text(self, wrap='word')
        self.text.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar = Scrollbar(self, orient=VERTICAL, command=self.text.yview)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.text.config(yscrollcommand=scrollbar.set)
    
    def refresh_positions(self):
        try:
            with open(POSITIONS_FILE, 'r', encoding='utf-8') as f:
                positions = json.load(f)
            display_text = ""
            if positions:
                # 假设 positions 为列表，按每个仓位条目格式化输出
                for pos in positions:
                    symbol = pos.get("symbol", "")
                    positionAmt = pos.get("positionAmt", "")
                    entryPrice = pos.get("entryPrice", "")
                    unrealizedProfit = pos.get("unrealizedProfit", "")
                    display_text += (f"交易对: {symbol}\n"
                                     f"  持仓量: {positionAmt}\n"
                                     f"  未实现盈亏: {unrealizedProfit}\n"
                                     "--------------------------\n")
            else:
                display_text = "当前无仓位记录。"
        except Exception as e:
            display_text = f"读取仓位数据出错: {e}"
        self.text.delete("1.0", END)
        self.text.insert(END, display_text)

    def start_positions_update(self):
        self.stop_positions_update()
        # 设置仓位更新线程并启动
        self.positions_update_job = threading.Event()
        self.positions_update_job.set()
        if not hasattr(self, 'positions_update_thread') or not self.positions_update_thread.is_alive():
            self.positions_update_thread = threading.Thread(target=self.positions_update_loop, daemon=True)
            self.positions_update_thread.start()

    def stop_positions_update(self):
        if hasattr(self, 'positions_update_job'):
            self.positions_update_job.clear()

    def positions_update_loop(self):
        """在线程中执行仓位更新"""
        while self.positions_update_job.is_set():
            try:
                self.positions_update()
            except Exception as e:
                logging.error(f"更新仓位失败")
            time.sleep(2)  # 每2秒更新一次

    def positions_update(self):
        """线程中执行的仓位更新操作"""
        try:
            update_positions_from_api(self.client)
            # 使用after方法将UI更新操作放回主线程
            self.after(0, self.refresh_positions)
        except Exception as e:
            logging.error(f"更新仓位失败")

class KlinePage(Frame):
    """K线界面"""
    def __init__(self, master, switch_page_callback, client, default_symbol="BTCUSDT"):
        super().__init__(master)
        self.switch_page_callback = switch_page_callback
        self.client = client
        self.symbols = []  # 存储所有交易对
        self.default_symbol = default_symbol  # 新增：内置默认标的物
        # 初始化配置管理器
        self.symbol_config_manager = SymbolConfig()
        # 初始化超时适配器
        self._timeout_adapter = None
        self._original_adapters = {}
        
        # 初始化变量
        self.current_valid_symbol = self.default_symbol  # 当前有效的交易对
        # 从全局交易对管理器获取可交易的交易对
        self.load_trading_symbols()
        if not self.symbols:  # 如果加载失败，使用默认值
            self.symbols = [self.default_symbol]
        # 创建界面
        self.create_widgets()
        
        # 更新保证金和杠杆信息
        self.update_margin_leverage_info()
        
        # 创建图表
        self.create_chart()

        # 添加首次打开时的图表显示修复标志
        self.first_time_opened = True
        
    def load_trading_symbols(self):
        """从全局交易对管理器加载可交易的交易对"""
        try:
            global trading_symbols_manager
            if 'trading_symbols_manager' in globals():
                # 获取可交易的交易对（只显示可交易的）
                self.symbols = trading_symbols_manager.get_symbols(tradable_only=True)
                logging.info(f"K线页面加载了 {len(self.symbols)} 个可交易交易对")
            else:
                logging.warning("全局交易对管理器未初始化，使用默认交易对")
                self.symbols = [self.default_symbol]

        except Exception as e:
            logging.error(f"K线页面加载交易对失败: {str(e)}")
            self.symbols = [self.default_symbol]

    def filter_symbols(self, pattern):
        """根据输入过滤交易对"""
        pattern = pattern.upper()
        self.filtered_symbols = [s for s in self.symbols if pattern in s]
        self.update_symbol_listbox()

    def update_symbol_listbox(self):
        """更新交易对列表显示"""
        self.symbol_listbox.delete(0, 'end')
        for symbol in self.filtered_symbols[:10]:  # 最多显示10个选项
            self.symbol_listbox.insert('end', symbol)
        
        # 如果有匹配项，显示列表框
        if self.filtered_symbols:
            self.symbol_listbox.place(x=self.symbol_entry.winfo_x(),
                                    y=self.symbol_entry.winfo_y() + self.symbol_entry.winfo_height(),
                                    width=self.symbol_entry.winfo_width())
        else:
            self.symbol_listbox.place_forget()

    def on_symbol_select(self, event=None):
        """当用户从下拉列表中选择一个交易对时"""
        self.on_symbol_change()
        self.symbol_combobox.selection_clear()  # 清除选择，避免重复触发
        
    def on_symbol_focus(self, event=None):
        """当交易对选择框获得焦点时"""
        # 直接使用已经排序好的交易对列表（包含交割合约）
        self.symbol_combobox['values'] = self.symbols

        # 展开下拉列表
        if not self.symbol_combobox.winfo_ismapped():
            self.symbol_combobox.event_generate('<Down>')  # 展开下拉列表
            
    def on_symbol_var_change(self, *args):
        """当输入内容变化时触发"""
        current_text = self.symbol_var.get().upper()

        # 如果输入框为空，显示所有交易对（保持原有排序）
        if not current_text:
            self.symbol_combobox['values'] = self.symbols
        else:
            # 有输入内容时，只显示匹配的交易对（不区分大小写），保持原有排序
            filtered_symbols = [s for s in self.symbols if current_text in s.upper()]
            self.symbol_combobox['values'] = filtered_symbols
        
        # 如果有匹配结果且列表未展开，则展开列表
        if self.symbol_combobox['values'] and not self.symbol_combobox.winfo_ismapped():
            self.symbol_combobox.event_generate('<Down>')
        
        # 保持输入框焦点，光标在末尾
        self.after(10, lambda: self.symbol_combobox.icursor('end'))
        self.after(10, lambda: self.symbol_combobox.focus_set())

    def on_symbol_change(self, event=None):
        """交易对变化时更新币种单位显示和保证金信息"""
        try:
            symbol = self.symbol_var.get().strip().upper()

            # 检查是否是有效的交易对（包括交割合约）
            if symbol not in self.symbols:
                # 如果不在列表中，尝试添加USDT后缀（兼容旧的输入方式）
                if not symbol.endswith('USDT') and not '_' in symbol:
                    symbol = f"{symbol}USDT"
                    if symbol not in self.symbols:
                        # 如果还是不在列表中，使用默认值
                        symbol = self.symbols[0] if self.symbols else 'BTCUSDT'

            self.symbol_var.set(symbol)  # 更新为标准格式

            # 更新当前有效交易对
            self.current_valid_symbol = symbol

            # 更新币种单位显示（处理交割合约）
            if '_' in symbol:
                # 交割合约：BTCUSDT_250926 -> BTC
                coin = symbol.split('_')[0].replace("USDT", "")
            else:
                # 永续合约：BTCUSDT -> BTC
                coin = symbol.replace("USDT", "")
            self.coin_unit_label.config(text=coin)
            
            # 从API获取交易规则信息并更新配置
            try:
                # 获取交易所信息并更新交易对精度等信息
                success, error_message = self.symbol_config_manager.update_symbol_precision(symbol, client=self.client)
                if success:
                    logging.info(f"已更新{symbol}交易规则配置")
                else:
                    logging.warning(f"更新{symbol}交易规则配置失败: {error_message}")

                # 清空并重置价格和数量输入框
                self.price_var.set("")
                self.amount_var.set("")

                # 重新应用输入验证
                vcmd_price = (self.register(self.validate_price_input), '%P')
                self.price_entry.config(validate="key", validatecommand=vcmd_price)

                vcmd_amount = (self.register(self.validate_amount_input), '%P')
                self.amount_entry.config(validate="key", validatecommand=vcmd_amount)

            except Exception as e:
                logging.error(f"更新交易对配置失败: {str(e)}")
            
            # 更新保证金和杠杆信息
            self.update_margin_leverage_info()
            
            # 刷新K线图
            self.load_kline_data()
        except Exception as e:
            logging.error(f"更新交易对信息失败: {str(e)}")

    def on_symbol_return(self, event=None):
        """当用户按下回车键时"""
        if self.symbol_combobox['values']:
            # 如果没有选中项但有匹配结果，选择第一个
            if not self.symbol_combobox.get() in self.symbol_combobox['values']:
                self.symbol_var.set(self.symbol_combobox['values'][0])
            self.on_symbol_change()
        return 'break'

    def create_widgets(self):
        """创建界面元素"""
        self.pack(fill=BOTH, expand=True)
        
        # 创建顶部栏
        top_frame = Frame(self)
        top_frame.pack(fill="x", pady=5)
        
        # 返回按钮
        Button(top_frame, text="返回主菜单", 
               command=lambda: self.switch_page_callback("main")).pack(side="left", padx=5)
        
        # 同步时间按钮
        Button(top_frame, text="同步时间", fg="blue", bg="#e6e6e6", 
               command=self.sync_time_with_ntplib).pack(side="left", padx=5)
        
        # 添加查看配置按钮
        Button(top_frame, text="查看配置",
               command=self.display_symbol_config).pack(side="left", padx=5)
        
        # 指标选择
        self.indicator_var = StringVar(value="MA")
        indicator_menu = ttk.Combobox(top_frame, textvariable=self.indicator_var, 
                                    values=["None", "MA", "BOLL"], width=6)
        indicator_menu.pack(side="left", padx=5)
        # 绑定指标选择变化事件
        self.indicator_var.trace('w', self.on_indicator_change)
        
        # 交易对选择下拉框
        self.symbol_var = StringVar(value=self.default_symbol)
        self.symbol_combobox = ttk.Combobox(top_frame, textvariable=self.symbol_var, 
                                          values=self.symbols, width=15)
        self.symbol_combobox.pack(side="left", padx=5)
        
        # 绑定交易对选择框事件
        self.symbol_var.trace('w', self.on_symbol_var_change)  # 监听变量变化
        self.symbol_combobox.bind('<<ComboboxSelected>>', self.on_symbol_select)
        self.symbol_combobox.bind('<FocusIn>', self.on_symbol_focus)
        self.symbol_combobox.bind('<Return>', self.on_symbol_return)
        
        # K线周期选择
        self.interval_var = StringVar(value="15m")
        interval_options = ["1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w"]
        interval_menu = ttk.Combobox(top_frame, textvariable=self.interval_var, 
                                   values=interval_options, width=5)
        interval_menu.pack(side="left", padx=5)
        # 绑定K线周期变化事件
        self.interval_var.trace('w', self.on_interval_change)
        
        Button(top_frame, text="刷新图表", command=self.refresh_chart).pack(side="left", padx=5)
        
        # 自动刷新设置
        refresh_frame = Frame(top_frame)
        refresh_frame.pack(side="left", padx=10)
        self.auto_refresh_kline_var = IntVar(value=1) # Default to on
        self.auto_refresh_checkbtn = ttk.Checkbutton(refresh_frame, text="自动刷新", 
                                                  variable=self.auto_refresh_kline_var,
                                                  command=self.toggle_kline_update)
        self.auto_refresh_checkbtn.pack(side="left")
        
        # K线刷新控制 (价格刷新固定为0.1s)
        Label(refresh_frame, text="K线刷新(秒):").pack(side="left") 
        self.active_kline_refresh_s = 1.0 # 当前激活的K线刷新间隔，秒
        self.kline_refresh_interval_var = StringVar(value=str(self.active_kline_refresh_s)) 
        kline_refresh_interval_entry = Entry(refresh_frame, textvariable=self.kline_refresh_interval_var, width=3)
        kline_refresh_interval_entry.pack(side="left")
        # 绑定输入完成事件
        kline_refresh_interval_entry.bind("<Return>", self.handle_kline_interval_input_finished)
        kline_refresh_interval_entry.bind("<FocusOut>", self.handle_kline_interval_input_finished)
        
        # 主体部分使用水平布局
        self.main_frame = Frame(self)
        self.main_frame.pack(fill=BOTH, expand=True)

        # 获取DPI缩放因子
        import ctypes
        try:
            scale_factor = ctypes.windll.shcore.GetScaleFactorForDevice(0) / 100
        except:
            scale_factor = 1.0

        # 左侧K线图区域（动态宽度）
        self.chart_frame_width = 800 * scale_factor
        self.chart_frame = Frame(self.main_frame, width=self.chart_frame_width)
        self.chart_frame.pack(side=LEFT, fill=BOTH, expand=False)

        # 右侧交易面板（动态显示）
        self.trade_frame_width = 300 * scale_factor
        self.trade_frame = Frame(self.main_frame, width=self.trade_frame_width, bg='#f0f0f0')
        self.trade_frame.pack(side=LEFT, fill=Y, padx=10, pady=5)
        self.trade_frame.pack_propagate(False)

        # 绑定窗口大小变化事件
        self.bind('<Configure>', self.on_window_configure)
        self.main_frame.bind('<Configure>', self.on_main_frame_configure)

        # 延迟调用初始布局更新，确保组件已经完全初始化
        # self.after(1111,self.chart_frame.place(x=0, y=0, width=33))
        # self.after(1, self.update_layout)

        # 保证金模式和杠杆设置
        margin_frame = Frame(self.trade_frame, bg='#f0f0f0')
        margin_frame.pack(fill=X, pady=5)
        self.margin_button = Button(margin_frame, text="加载中...", width=15,
               command=self.change_margin_type)
        self.margin_button.pack(side=LEFT, padx=5)
        self.leverage_button = Button(margin_frame, text="加载中...", width=15,
               command=self.change_leverage)
        self.leverage_button.pack(side=LEFT, padx=5)
        self.update_margin_leverage_info()  # 初始化状态显示

        # 可用资金显示
        balance_frame = Frame(self.trade_frame, bg='#f0f0f0')
        balance_frame.pack(fill=X, pady=5)
        Label(balance_frame, text="可用资金:", bg='#f0f0f0').pack(side=LEFT, padx=5)
        self.balance_label = Label(balance_frame, text="加载中...", bg='#f0f0f0')
        self.balance_label.pack(side=LEFT, padx=5)

        # 开平仓选择
        position_frame = Frame(self.trade_frame, bg='#f0f0f0')
        position_frame.pack(fill=X, pady=5)
        Label(position_frame, text="持仓方向:", bg='#f0f0f0').pack(side=LEFT, padx=5)
        self.position_type_var = StringVar(value="开仓")
        ttk.Radiobutton(position_frame, text="开仓", value="开仓",
                       variable=self.position_type_var, command=self.on_position_type_change).pack(side=LEFT, padx=5)
        ttk.Radiobutton(position_frame, text="平仓", value="平仓",
                       variable=self.position_type_var, command=self.on_position_type_change).pack(side=LEFT, padx=5)

        # 交易类型选择
        type_frame = Frame(self.trade_frame, bg='#f0f0f0')
        type_frame.pack(fill=X, pady=5)
        Label(type_frame, text="订单类型:", bg='#f0f0f0').pack(side=LEFT, padx=5)
        self.order_type_var = StringVar(value="LIMIT")
        ttk.Radiobutton(type_frame, text="限价单", value="LIMIT",
                       variable=self.order_type_var, command=self.on_order_type_change).pack(side=LEFT, padx=10)
        ttk.Radiobutton(type_frame, text="市价单", value="MARKET",
                       variable=self.order_type_var, command=self.on_order_type_change).pack(side=LEFT, padx=10)

        # 价格输入区域
        price_frame = Frame(self.trade_frame, bg='#f0f0f0')
        price_frame.pack(fill=X, pady=5)
        Label(price_frame, text="价格(USDT):", bg='#f0f0f0').pack(side=LEFT, padx=5)
        self.price_var = StringVar()
        self.price_entry = Entry(price_frame, textvariable=self.price_var)
        self.price_entry.pack(side=LEFT, fill=X, expand=True, padx=5)

        # 添加价格输入验证
        vcmd_price = (self.register(self.validate_price_input), '%P')
        self.price_entry.config(validate="key", validatecommand=vcmd_price)

        # 数量输入区域
        amount_frame = Frame(self.trade_frame, bg='#f0f0f0')
        amount_frame.pack(fill=X, pady=5)
        Label(amount_frame, text="数量:", bg='#f0f0f0').pack(side=LEFT, padx=5)
        self.amount_var = StringVar()
        self.amount_entry = Entry(amount_frame, textvariable=self.amount_var)
        self.amount_entry.pack(side=LEFT, fill=X, expand=True, padx=5)

        # 添加数量输入验证
        vcmd_amount = (self.register(self.validate_amount_input), '%P')
        self.amount_entry.config(validate="key", validatecommand=vcmd_amount)

        # 修改数量单位选择部分
        unit_frame = Frame(amount_frame, bg='#f0f0f0')
        unit_frame.pack(side=RIGHT)
        self.amount_unit_var = StringVar(value="COIN")
        self.coin_unit_label = ttk.Radiobutton(unit_frame, text="BTC", value="COIN",
                       variable=self.amount_unit_var, command=self.on_unit_change)
        self.coin_unit_label.pack(side=LEFT)
        ttk.Radiobutton(unit_frame, text="USDT", value="USDT",
                       variable=self.amount_unit_var, command=self.on_unit_change).pack(side=LEFT)

        # 快捷比例按钮
        ratio_frame = Frame(self.trade_frame, bg='#f0f0f0')
        ratio_frame.pack(fill=X, pady=5)
        for ratio in ["25%", "50%", "75%", "100%"]:
            Button(ratio_frame, text=ratio, width=8,
                   command=lambda r=ratio: self.set_amount_ratio(r)).pack(side=LEFT, padx=2)

        # 交易按钮区域
        self.trade_buttons_frame = Frame(self.trade_frame, bg='#f0f0f0')
        self.trade_buttons_frame.pack(fill=X, pady=10)
        self.update_trade_buttons()

    def on_position_type_change(self):
        """开平仓类型改变时的处理"""
        self.update_trade_buttons()

    def update_trade_buttons(self):
        """根据开平仓选择更新交易按钮"""
        # 清除现有按钮
        for widget in self.trade_buttons_frame.winfo_children():
            widget.destroy()

        if self.position_type_var.get() == "开仓":
            # 开仓按钮
            Button(self.trade_buttons_frame, text="开多", width=15, height=2, bg='#77d879', fg='white',
                   command=lambda: self.place_order("LONG", "BUY")).pack(side=LEFT, padx=5, pady=5)
            Button(self.trade_buttons_frame, text="开空", width=15, height=2, bg='#d16d6d', fg='white',
                   command=lambda: self.place_order("SHORT", "SELL")).pack(side=LEFT, padx=5, pady=5)
        else:
            # 平仓按钮
            Button(self.trade_buttons_frame, text="平多", width=15, height=2, bg='#d16d6d', fg='white',
                   command=lambda: self.place_order("LONG", "SELL")).pack(side=LEFT, padx=5, pady=5)
            Button(self.trade_buttons_frame, text="平空", width=15, height=2, bg='#77d879', fg='white',
                   command=lambda: self.place_order("SHORT", "BUY")).pack(side=LEFT, padx=5, pady=5)

    def place_order(self, position_side, side):
        """执行交易操作"""
        try:
            symbol = self.current_valid_symbol
            price_str = self.price_var.get()
            amount_str = self.amount_var.get()
            
            if not price_str or not amount_str:
                messagebox.showinfo("提示", "请输入价格和数量")
                return
            
            # 转换为数值
            try:
                price = float(price_str)
                amount = float(amount_str)
            except ValueError:
                messagebox.showinfo("提示", "价格和数量必须是数字")
                return
            
            # 获取当前是开仓还是平仓
            position_type = self.position_type_var.get()
            
            # 确认交易
            if position_type == "开仓":
                action = "开仓" + ("做多" if side == "BUY" else "做空")
            else:
                action = "平仓" + ("多头" if side == "SELL" else "空头")
                
            if not messagebox.askyesno("确认交易", f"确认要{action}吗？\n交易对: {symbol}\n价格: {price}\n数量: {amount}"):
                return
            
            # 基本参数
            order_type = self.order_type_var.get()
            params = {
                'symbol': symbol,
                'side': side,
                'type': order_type,
                'positionSide': position_side,  # LONG或SHORT
            }
                
            # 根据单位转换数量
            if self.amount_unit_var.get() == "USDT":
                # 如果是USDT单位，转换为币种数量
                # 使用K线数据获取最新价格
                if hasattr(self, 'klines_df') and self.klines_df is not None and not self.klines_df.empty:
                    current_price = float(self.klines_df.iloc[-1]['Close'])
                else:
                    ticker = self.client.ticker_price(symbol)
                    current_price = float(ticker['price'])
                amount = amount / current_price
            
            # 格式化价格和数量，确保符合交易所要求的精度
            formatted_price = self.symbol_config_manager.format_price(symbol, price)
            formatted_quantity = self.symbol_config_manager.format_quantity(symbol, amount)
            
            logging.info(f"准备{action}: {symbol}, 价格={formatted_price}, 数量={formatted_quantity}")
            
            # 根据订单类型设置参数
            if order_type == "LIMIT":
                params.update({
                    'timeInForce': 'GTC',
                    'price': formatted_price,
                    'quantity': formatted_quantity
                })
            else:  # MARKET
                params['quantity'] = formatted_quantity
                
            # 发送请求
            try:
                result = self.client.new_order(**params)
                messagebox.showinfo("下单成功", f"订单ID: {result['orderId']}\n方向: {position_side}\n数量: {formatted_quantity}")
                logging.info(f"{action}成功: {result}")
            except Exception as e:
                messagebox.showerror("下单失败", str(e))
                logging.error(f"{action}失败: {str(e)}")
            
        except Exception as e:
            messagebox.showerror("下单失败", str(e))
            logging.error(f"下单过程出错: {str(e)}")

    def on_order_type_change(self):
        """订单类型改变时的处理"""
        if self.order_type_var.get() == "MARKET":
            self.price_entry.config(state='disabled')
        else:
            self.price_entry.config(state='normal')

    def set_amount_ratio(self, ratio):
        """设置金额为账户余额的指定比例"""
        try:
            # 获取账户信息
            account_info = self.client.account()
            available_balance = float(account_info.get('availableBalance', 0))
            percentage = float(ratio.strip('%')) / 100
            
            # 使用K线数据获取最新价格
            if hasattr(self, 'klines_df') and self.klines_df is not None and not self.klines_df.empty:
                current_price = float(self.klines_df.iloc[-1]['Close'])
            else:
                ticker = self.client.ticker_price(self.current_valid_symbol)
                current_price = float(ticker['price'])
            
            # 从本地配置获取当前杠杆倍数
            config = self.symbol_config_manager.get_symbol_config(self.current_valid_symbol)
            leverage = config.get('leverage', 1)
            if leverage == 'N/A':
                leverage = 1  # 如果没有杠杆信息，使用默认值1
            else:
                leverage = float(leverage)
            
            # 计算可用数量（考虑杠杆）
            if self.amount_unit_var.get() == "USDT":
                max_amount = available_balance * leverage
            else:  # COIN
                max_amount = (available_balance * leverage) / current_price
                
            self.amount_var.set(f"{max_amount * percentage:.4f}")
        except Exception as e:
            messagebox.showerror("错误", f"计算数量失败: {str(e)}")

    def get_kline_file_path(self, symbol, interval, dt=None):
        symbol_dir = os.path.join(KLINE_DATA_DIR, symbol)
        os.makedirs(symbol_dir, exist_ok=True)
        if interval == '1m' and dt is not None:
            month_str = get_month_str(dt)
            return os.path.join(symbol_dir, f"{month_str}.csv")
        else:
            return os.path.join(symbol_dir, f"{interval}.csv")

    def save_klines_to_local(self, klines_df, symbol, interval):
        try:
            df = klines_df.copy()
            if 'Open time' in df.columns:
                df['Open time'] = pd.to_datetime(df['Open time'])
            if interval == '1m':
                # 按月份分组保存
                if df.empty:
                    return
                df['month'] = df['Open time'].dt.strftime('%Y-%m')
                for month, group in df.groupby('month'):
                    file_path = os.path.join(KLINE_DATA_DIR, symbol, f"{month}.csv")
                    if os.path.exists(file_path):
                        try:
                            existing_df = pd.read_csv(file_path)
                            if 'Open time' in existing_df.columns:
                                existing_df['Open time'] = pd.to_datetime(existing_df['Open time'])
                            combined_df = pd.concat([existing_df, group.drop(columns=['month'])])
                            combined_df = combined_df.drop_duplicates(subset=['Open time'], keep='last')
                            combined_df = combined_df.sort_values(by='Open time')
                            combined_df.to_csv(file_path, index=False)
                        except Exception as e:
                            group.drop(columns=['month']).to_csv(file_path, index=False)
                    else:
                        group.drop(columns=['month']).to_csv(file_path, index=False)
            else:
                file_path = self.get_kline_file_path(symbol, interval)
                if os.path.exists(file_path):
                    try:
                        existing_df = pd.read_csv(file_path)
                        if 'Open time' in existing_df.columns:
                            existing_df['Open time'] = pd.to_datetime(existing_df['Open time'])
                        combined_df = pd.concat([existing_df, df])
                        combined_df = combined_df.drop_duplicates(subset=['Open time'], keep='last')
                        combined_df = combined_df.sort_values(by='Open time')
                        combined_df.to_csv(file_path, index=False)
                    except Exception as e:
                        df.to_csv(file_path, index=False)
                else:
                    df.to_csv(file_path, index=False)
        except Exception as e:
            logging.error(f"保存K线数据失败: {str(e)}")

    def load_klines_from_local(self, symbol, interval, start_dt=None, end_dt=None):
        try:
            symbol_dir = os.path.join(KLINE_DATA_DIR, symbol)
            if interval == '1m':
                # 只加载本月和上个月，提升速度
                now = datetime.now()
                this_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                last_month = (this_month - pd.DateOffset(months=1)).to_pydatetime()
                files = []
                # 优先本月
                this_month_file = f"{this_month.strftime('%Y-%m')}.csv"
                if os.path.exists(os.path.join(symbol_dir, this_month_file)):
                    files.append(this_month_file)
                # 如果需要再加上上个月
                # last_month_file = f"{last_month.strftime('%Y-%m')}.csv"
                # if os.path.exists(os.path.join(symbol_dir, last_month_file)):
                #     files.append(last_month_file)
                dfs = []
                for f in files:
                    file_path = os.path.join(symbol_dir, f)
                    try:
                        df = pd.read_csv(file_path)
                        if not df.empty and 'Open time' in df.columns:
                            df['Open time'] = pd.to_datetime(df['Open time'])
                            dfs.append(df)
                    except Exception as e:
                        logging.error(f"读取分月K线失败: {file_path}, {e}")
                if dfs:
                    all_df = pd.concat(dfs)
                    all_df = all_df.drop_duplicates(subset=['Open time'], keep='last')
                    all_df = all_df.sort_values(by='Open time')
                    # 按需筛选
                    if start_dt is not None:
                        all_df = all_df[all_df['Open time'] >= start_dt]
                    if end_dt is not None:
                        all_df = all_df[all_df['Open time'] <= end_dt]
                    return all_df.reset_index(drop=True)
                else:
                    return pd.DataFrame()
            else:
                file_path = self.get_kline_file_path(symbol, interval)
                if os.path.exists(file_path):
                    df = pd.read_csv(file_path)
                    if not df.empty and 'Open time' in df.columns:
                        df['Open time'] = pd.to_datetime(df['Open time'])
                        if start_dt is not None:
                            df = df[df['Open time'] >= start_dt]
                        if end_dt is not None:
                            df = df[df['Open time'] <= end_dt]
                        return df.reset_index(drop=True)
                return pd.DataFrame()
        except Exception as e:
            logging.error(f"加载本地K线数据失败: {str(e)}")
            return pd.DataFrame()

    def update_range_labels(self):
        """更新当前视图范围内的最高价和最低价标签"""
        try:
            if not hasattr(self, 'klines_df') or self.klines_df is None or self.klines_df.empty:
                return
            
            if not hasattr(self, 'ax_main') or not hasattr(self, 'canvas'):
                return
                
            # 获取当前x轴范围
            xlim = self.ax_main.get_xlim()
            
            # 删除旧的高低价标签
            if hasattr(self, 'high_price_line') and self.high_price_line:
                self.high_price_line.remove()
                self.high_price_line = None
            
            if hasattr(self, 'low_price_line') and self.low_price_line:
                self.low_price_line.remove()
                self.low_price_line = None
            
            if hasattr(self, 'high_price_label') and self.high_price_label:
                self.high_price_label.remove()
                self.high_price_label = None
            
            if hasattr(self, 'low_price_label') and self.low_price_label:
                self.low_price_label.remove()
                self.low_price_label = None
            
            # 删除调试信息标签
            if hasattr(self, 'debug_info_label') and self.debug_info_label:
                self.debug_info_label.remove()
                self.debug_info_label = None
            
            # 计算当前视图下可见的K线索引范围
            total_bars = len(self.klines_df)
            
            # 修改可见K线的计算逻辑
            # 只有当K线的中心点（整数索引）在视图范围内时才计算为可见
            # 向上取整，确保只有当K线中心点在边界内才算该K线可见
            start_index = math.ceil(xlim[0])
            # 向下取整，确保只有当K线中心点在边界内才算该K线可见
            end_index = math.floor(xlim[1])
            
            # 确保索引在有效范围内
            start_index = max(0, start_index)
            end_index = min(total_bars - 1, end_index)
            
            # 检查索引范围是否有效
            if start_index > end_index or start_index < 0 or end_index >= total_bars:
                return
            
            # 获取可见范围内的K线数据
            visible_bars = self.klines_df.iloc[start_index:end_index+1]
            
            if visible_bars.empty:
                return
            
            # 获取最高价和最低价
            high_price = visible_bars['High'].max()
            low_price = visible_bars['Low'].min()
            
            # 获取当前交易对的价格精度
            symbol = self.current_valid_symbol
            price_precision = self.symbol_config_manager.get_price_precision(symbol)
            
            # 获取微软雅黑字体
            font_path = 'C:/Windows/Fonts/msyh.ttc'
            font_prop = fm.FontProperties(fname=font_path)
            
            # 计算图表中点
            x_middle = (xlim[0] + xlim[1]) / 2
            
            # 找出最高价和最低价的位置（如果有多个相同的最高价或最低价，取最接近当前时间的一个）
            # 获取最后一根K线的索引（代表当前时间）
            latest_index = len(self.klines_df) - 1
            
            # 找到所有等于最高价的索引
            high_indices = visible_bars.index[visible_bars['High'] == high_price].tolist()
            # 找到所有等于最低价的索引
            low_indices = visible_bars.index[visible_bars['Low'] == low_price].tolist()
            
            # 如果有多个相同的最高价，选择最接近当前时间（最新K线）的一个
            if len(high_indices) > 0:
                high_index = min(high_indices, key=lambda idx: abs(idx - latest_index))
            else:
                high_index = visible_bars['High'].idxmax()
                
            # 如果有多个相同的最低价，选择最接近当前时间（最新K线）的一个
            if len(low_indices) > 0:
                low_index = min(low_indices, key=lambda idx: abs(idx - latest_index))
            else:
                low_index = visible_bars['Low'].idxmin()

            # 计算显示位置（相对于整个数据集的索引）
            high_pos = visible_bars.index.get_loc(high_index) + start_index
            low_pos = visible_bars.index.get_loc(low_index) + start_index
            
            # 获取Y轴范围
            ylim = self.ax_main.get_ylim()
            
            # K线理论宽度估计
            k_width = 0.5  # 假设K线宽度为0.5
            
            # 添加调试信息标签到左上角
            debug_info = (
                f"X轴范围: {xlim[0]:.1f}-{xlim[1]:.1f}\n"
                f"Y轴范围: {ylim[0]:.2f}-{ylim[1]:.2f}\n"
                f"可见K线: {start_index}-{end_index} (共{end_index-start_index+1}根)\n"
                f"有效K线: {len(self.klines_df)}\n"
                f"总K线数: {total_bars}根\n"
                f"最高价: {high_price:.{price_precision}f} (位于第{high_pos}根K线)\n"
                f"最低价: {low_price:.{price_precision}f} (位于第{low_pos}根K线)\n"
                # f"K线中心判定: 整数索引±{k_width/2:.2f}"
            )
            
            # 在左上角显示调试信息
            # self.debug_info_label = self.ax_main.text(
            #     0.02, 0.98, debug_info,
            #     transform=self.ax_main.transAxes,  # 使用轴的相对坐标系统
            #     verticalalignment='top',
            #     horizontalalignment='left',
            #     fontsize=9,
            #     bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
            #     zorder=10,  # 确保显示在最上层
            #     font='Microsoft YaHei'
            # )
            
            # 设置箭头属性 - 灰色水平细短线
            arrow_props = dict(
                arrowstyle='-',
                color='gray',
                linewidth=0.8,
                shrinkA=0,
                shrinkB=0
            )
            
            # 根据位置决定标签方向和连接点
            if high_pos < x_middle:  # 左半区
                high_ha = 'left'  # 水平对齐方式
                high_xt = (20, 0)  # 标签偏移量
                high_connect_x = high_pos  # 连接到K线位置
                high_position = (high_connect_x, high_price)
            else:  # 右半区
                high_ha = 'right'
                high_xt = (-20, 0)
                high_connect_x = high_pos
                high_position = (high_connect_x, high_price)
            
            if low_pos < x_middle:  # 左半区
                low_ha = 'left'
                low_xt = (20, 0)
                low_connect_x = low_pos
                low_position = (low_connect_x, low_price)
            else:  # 右半区
                low_ha = 'right'
                low_xt = (-20, 0)
                low_connect_x = low_pos
                low_position = (low_connect_x, low_price)
                
            # 创建最高价标签，使用灰色小字体，无边框，带箭头，添加千位分隔符
            self.high_price_label = self.ax_main.annotate(
                f"{high_price:,.{price_precision}f}", 
                xy=high_position,  # 箭头指向的位置
                xytext=high_xt,  # 文本相对于xy的偏移
                textcoords='offset points',
                color='gray',
                fontproperties=font_prop,  # 使用微软雅黑字体
                fontsize=8,
                ha=high_ha,  # 水平对齐方式
                va='center',  # 垂直对齐方式
                arrowprops=arrow_props
            )
            
            # 创建最低价标签，使用灰色小字体，无边框，带箭头，添加千位分隔符
            self.low_price_label = self.ax_main.annotate(
                f"{low_price:,.{price_precision}f}", 
                xy=low_position,
                xytext=low_xt,
                textcoords='offset points',
                color='gray',
                fontproperties=font_prop,  # 使用微软雅黑字体
                fontsize=8,
                ha=low_ha,
                va='center',
                arrowprops=arrow_props
            )
            
        except Exception as e:
            logging.error(f"更新价格范围标签失败: {str(e)}")
            traceback.print_exc()

    def on_mouse_release(self, event):
        """处理鼠标释放事件"""
        if self.pan_active or self.scale_active:
            # 结束平移或缩放
            self.pan_active = False
            self.scale_active = False
            self.canvas.get_tk_widget().config(cursor="crosshair")  # 恢复十字星光标
            
            # 更新价格范围标签（拖动结束后显示完整标签）
            self.update_range_labels()

    def load_kline_data(self):
        """加载K线数据，并重绘K线图（模仿测试程序的generate_chart_data）"""
        try:
            # 清除现有的图表内容（模仿测试程序的ax.clear()）
            self.ax_main.clear()

            symbol = self.current_valid_symbol
            interval = self.interval_var.get()

            # 初始加载显示的K线最大数量
            MAX_DISPLAY_KLINES = 1000
            # 初始视图中显示的K线数量
            INITIAL_VIEW_KLINES = 100

            # 首先尝试从本地加载历史K线数据
            local_klines = self.load_klines_from_local(symbol, interval)

            # 确定当前时间（东八区/北京时间）
            current_time = datetime.now()

            # 如果需要获取特定时间段的数据，可以设置startTime和endTime参数
            # 计算API请求的开始时间（可选）
            # start_time = int((current_time - timedelta(days=7)).timestamp() * 1000)  # 获取近7天数据

            # 标记API是否成功加载
            api_load_success = False

            # 从API获取最新的K线数据
            try:
                # 使用封装的超时控制方法
                api_klines, api_load_success = self._api_call_with_timeout(
                    self.client.klines,
                    symbol=symbol,
                    interval=interval,
                    limit=1000,
                    timeout=3.0
                    # startTime=start_time  # 如需指定开始时间可取消注释
                )

                if not api_load_success:
                    raise Exception("API调用失败")

            except Exception as e:
                logging.error(f"API获取K线数据失败: {str(e)}")
                api_klines = []  # 设置为空列表，后续代码会处理
                
            
            # 将API返回的K线数据转换为DataFrame
            api_df = pd.DataFrame(api_klines, columns=[
                'Open time', 'Open', 'High', 'Low', 'Close', 'Volume',
                'Close time', 'Quote asset volume', 'Number of trades',
                'Taker buy base asset volume', 'Taker buy quote asset volume', 'Ignore'
            ])

            # 只有当API数据不为空时才进行数据类型转换
            if not api_df.empty:
                # 转换数据类型
                # 将UTC毫秒时间戳转换为东八区datetime对象
                api_df['Open time'] = pd.to_datetime(api_df['Open time'], unit='ms') + pd.Timedelta(hours=8)
                api_df['Close time'] = pd.to_datetime(api_df['Close time'], unit='ms') + pd.Timedelta(hours=8)

                api_df = api_df.astype({
                    'Open': float, 'High': float, 'Low': float,
                    'Close': float, 'Volume': float
                })
            
            # 添加一个创建单根空K线的辅助函数
            def get_single_dummy_kline():
                # 将时间戳0转换为东八区时间
                open_time = pd.to_datetime(0, unit='ms') + pd.Timedelta(hours=8)
                close_time = pd.to_datetime(1, unit='ms') + pd.Timedelta(hours=8)
                data = {
                    'Open time': [open_time],
                    'Open': [0],
                    'High': [0],
                    'Low': [0],
                    'Close': [0],
                    'Volume': [0],
                    'Close time': [close_time],
                    'Quote asset volume': [0],
                    'Number of trades': [0],
                    'Taker buy base asset volume': [0],
                    'Taker buy quote asset volume': [0],
                    'Ignore': [0]
                }
                return pd.DataFrame(data)
                
            # 先判断API数据是否为空，再判断本地数据，简化逻辑结构
            if not api_df.empty:
                # API数据可用
                if not local_klines.empty:
                    # 合并API数据和本地数据
                    completed_klines = pd.concat([local_klines, api_df.iloc[:-1]])
                    all_klines = pd.concat([local_klines, api_df])
                    # 删除重复项
                    completed_klines = completed_klines.drop_duplicates(subset=['Open time'], keep='last')
                    all_klines = all_klines.drop_duplicates(subset=['Open time'], keep='last')
                    # 按时间排序
                    completed_klines = completed_klines.sort_values(by='Open time')
                    all_klines = all_klines.sort_values(by='Open time')
                else:
                    # 只有API数据，没有本地数据
                    completed_klines = api_df.iloc[:-1]
                    all_klines = api_df
                # 保存到本地
                self.save_klines_to_local(completed_klines, symbol, interval)
            else:
                # API数据为空，检查本地数据
                if not local_klines.empty:
                    # 只有本地数据
                    completed_klines = local_klines
                    all_klines = local_klines
                else:
                    # API获取失败且没有本地数据，创建一根空K线确保图表能够正常生成
                    completed_klines = get_single_dummy_kline()
                    all_klines = get_single_dummy_kline()
          
            # 检查需要显示的K线数量，确保第一根K线落在整数时间上
            if len(completed_klines) > MAX_DISPLAY_KLINES:
                # 初始显示最近的MAX_DISPLAY_KLINES条
                self.display_klines = completed_klines.tail(MAX_DISPLAY_KLINES)
                if not api_df.empty:
                    self.klines_df = all_klines.tail(MAX_DISPLAY_KLINES+1)
                else:
                    self.klines_df = all_klines.tail(MAX_DISPLAY_KLINES)
                
                # 检查第一根K线是否在整数时间上
                first_kline_time = self.display_klines.iloc[0]['Open time']
                
                # 定义需要增加的K线数量
                additional_klines = 0
                
                # 根据不同周期计算需要增加的K线数量，使第一根K线落在整数时间上
                if interval.endswith('m'):
                    # 分钟级别，计算到整点或半小时的距离
                    minutes = int(interval[:-1])
                    # 计算到前一个整点或半小时的分钟数
                    if first_kline_time.minute % 30 > 0:
                        # 分钟不在整点或半小时上
                        minutes_diff = first_kline_time.minute % 30
                        # 计算需要额外加载的K线数量
                        additional_klines = minutes_diff // minutes
                        if first_kline_time.second > 0:
                            additional_klines += 1  # 如果秒数不是0，需要再加一根
                
                elif interval.endswith('h'):
                    # 小时级别，计算到整点小时的距离
                    pass
                    # hours = int(interval[:-1])
                    # if first_kline_time.minute > 0 or first_kline_time.second > 0:
                    #     # 计算需要额外加载的K线数量，使其落在整点小时
                    #     minutes_diff = first_kline_time.minute
                    #     additional_klines = 1  # 至少需要多一根
                
                elif interval == '1d':
                    # 日线，计算到0点的距离
                    pass
                    # if first_kline_time.hour > 0 or first_kline_time.minute > 0 or first_kline_time.second > 0:
                    #     # 如果不在0点，需要增加K线
                    #     additional_klines = 1  # 至少需要多一根
                
                elif interval == '1w':
                    # 周线，计算到周一0点的距离
                    pass
                    # if first_kline_time.weekday() != 0:
                    #     # 如果不在周一0点，需要增加K线
                    #     additional_klines = 1  # 至少需要多一根
                
                else:
                    # 其他周期默认计算到整15分钟的距离
                    if first_kline_time.minute % 15 > 0 or first_kline_time.second > 0:
                        # 计算到前一个15分钟整点的距离
                        minutes_diff = first_kline_time.minute % 15
                        # 假设为15分钟周期
                        additional_klines = (minutes_diff + 14) // 15  # 向上取整
                
                # 如果需要增加K线数量，并且有足够的历史数据
                if additional_klines > 0 and len(completed_klines) >= MAX_DISPLAY_KLINES + additional_klines:
                    new_display_count = MAX_DISPLAY_KLINES + additional_klines
                    self.display_klines = completed_klines.tail(new_display_count).reset_index(drop=True)
                    if not api_df.empty:
                        self.klines_df = all_klines.tail(new_display_count+1).reset_index(drop=True)
                    else:
                        self.klines_df = all_klines.tail(new_display_count).reset_index(drop=True)
                    logging.info(f"增加显示K线 {additional_klines} 根，确保第一根K线在整数时间上")
                    logging.info(f"显示最近 {new_display_count} 根K线（总计有 {len(all_klines)} 根K线数据）")
            else:
                self.display_klines = completed_klines
                self.klines_df = all_klines
                logging.info(f"显示所有 {len(all_klines)} 根K线数据")
            
            # 准备绘图用的数据
            df_plot = self.display_klines.copy()
            df_plot.set_index('Open time', inplace=True)
            
            # 设置微软雅黑字体
            font_path = 'C:/Windows/Fonts/msyh.ttc'
            font_prop = fm.FontProperties(fname=font_path)

            # 清除现有的图表内容（模仿测试程序的ax.clear()）
            self.ax_main.clear()

            # 设置charles风格的图表样式
            self.setup_charles_style(self.ax_main, font_prop)

            # 使用优化的K线绘制方法，提高交互性能
            self.draw_klines_optimized(df_plot, self.ax_main, df_plot, color_up='g', color_down='r', alpha=0.5, candle_width=0.5, candle_linewidth=0.5)

            # 设置坐标轴格式化器
            self.setup_axis_formatters(self.ax_main, df_plot, font_prop)

            # 设置紧凑布局，减少空白
            self.setup_tight_layout(self.fig, self.ax_main)
            
            # 调整初始视图范围，只显示最近的100根K线，右侧留出10条K线的空白
            total_bars = len(df_plot)
            if total_bars > INITIAL_VIEW_KLINES:
                # 设置新的x轴范围，只显示最后100根K线，右侧留出10条K线的空白
                start_idx = total_bars - INITIAL_VIEW_KLINES
                end_idx = total_bars + 10  # 右侧留出10条K线的空白
                self.new_xlim = (start_idx - 0.5, end_idx + 0.5)
                self.ax_main.set_xlim(self.new_xlim)
                # 同时调整y轴范围，只考虑最近的100根K线
                last_100_bars = df_plot.iloc[-INITIAL_VIEW_KLINES:]
                y_min = last_100_bars['Low'].min() * 0.998  # 留出一点下方空间
                y_max = last_100_bars['High'].max() * 1.002  # 留出一点上方空间
                self.new_ylim = (y_min, y_max)
                self.ax_main.set_ylim(self.new_ylim)
            else:
                # 如果K线数量不足100根，也要在右侧留出10条K线的空白
                start_idx = -0.5
                end_idx = total_bars + 10  # 右侧留出10条K线的空白
                self.new_xlim = (start_idx, end_idx + 0.5)
                self.ax_main.set_xlim(self.new_xlim)
                # Y轴范围
                y_min = df_plot['Low'].min() * 0.998
                y_max = df_plot['High'].max() * 1.002
                self.new_ylim = (y_min, y_max)
                self.ax_main.set_ylim(self.new_ylim)
                logging.info(f"调整初始视图范围为最近 {INITIAL_VIEW_KLINES} 根K线（显示索引 {start_idx}-{total_bars-1}）")
                logging.info(f"调整y轴范围为 {y_min:.2f} - {y_max:.2f}，基于最近100根K线")
            
            # 根据API加载状态决定是否强制更新K线
            if api_load_success:
                self.update_kline_data(force_refresh=True)
            else:
                # 绘制技术指标
                self.on_indicator_change()

                # 更新高低价范围标签
                self.update_range_labels()

                # 刷新画布
                self.canvas.draw_idle()
            
        except Exception as e:
            logging.error(f"加载K线数据失败: {str(e)}")
            import traceback
            traceback.print_exc()  # 打印详细错误信息

    def create_chart(self):
        """创建图表（完全模仿DPI测试程序）"""
        print("开始创建图表...")

        if hasattr(self, 'canvas'):
            self.canvas.get_tk_widget().destroy()

        # 获取DPI缩放因子
        import ctypes
        try:
            scale_factor = ctypes.windll.shcore.GetScaleFactorForDevice(0) / 100
        except:
            scale_factor = 1.0

        # 计算figsize（使用动态chart宽度）
        chart_dpi = 100
        # 使用当前实际的chart宽度
        figsize_width = self.chart_frame_width / scale_factor / chart_dpi
        figsize_height = 1 / scale_factor / chart_dpi
        # print(f"figsize: ({figsize_width:.2f}, {figsize_height:.2f}), DPI: {chart_dpi}, chart_width: {current_chart_width}")

        # 创建Matplotlib图表（完全模仿DPI测试程序）
        self.fig = plt.Figure(figsize=(figsize_width, figsize_height), dpi=chart_dpi)
        self.ax_main = self.fig.add_subplot(111)

        # 创建单根空K线用于初始化
        import pandas as pd
        open_time = pd.to_datetime(0, unit='ms') + pd.Timedelta(hours=8)
        close_time = pd.to_datetime(1, unit='ms') + pd.Timedelta(hours=8)
        empty_data = {
            'Open time': [open_time],
            'Open': [0],
            'High': [0],
            'Low': [0],
            'Close': [0],
            'Volume': [0],
            'Close time': [close_time],
            'Quote asset volume': [0],
            'Number of trades': [0],
            'Taker buy base asset volume': [0],
            'Taker buy quote asset volume': [0],
            'Ignore': [0]
        }
        empty_df = pd.DataFrame(empty_data)
        self.display_klines = empty_df
        self.klines_df = empty_df

        # 准备绘图用的数据
        df_plot = empty_df.copy()
        df_plot.set_index('Open time', inplace=True)

        # 设置微软雅黑字体
        import matplotlib.font_manager as fm
        font_path = 'C:/Windows/Fonts/msyh.ttc'
        font_prop = fm.FontProperties(fname=font_path)

        # 设置charles风格的图表样式
        self.setup_charles_style(self.ax_main, font_prop)

        # 使用优化的K线绘制方法绘制空K线
        self.draw_klines_optimized(df_plot, self.ax_main, df_plot, color_up='g', color_down='r', alpha=0.5, candle_width=0.5, candle_linewidth=0.5)

        # 设置坐标轴格式化器
        self.setup_axis_formatters(self.ax_main, df_plot, font_prop)

        # 设置紧凑布局，减少空白
        self.setup_tight_layout(self.fig, self.ax_main)

        # 将图表嵌入Tkinter（完全模仿DPI测试程序）
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.chart_frame)

        # 添加工具栏
        from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk

        # 创建自定义工具栏类，移除不需要的按钮，并自定义home按钮功能
        class CustomNavigationToolbar(NavigationToolbar2Tk):
            # 创建没有configure subplots和拖动按钮的工具栏
            toolitems = [t for t in NavigationToolbar2Tk.toolitems if t[0] not in ('Subplots', 'Pan')]

            def __init__(self, canvas, parent, kline_page=None):
                self.kline_page = kline_page
                self.zoom_active = False  # 跟踪放大镜工具状态
                super().__init__(canvas, parent)

                # 移除默认的坐标显示标签
                self._remove_message_label()

                # 创建自定义状态栏和时间标签
                self.create_custom_status_widgets()

            def _remove_message_label(self):
                """移除默认的坐标显示标签"""
                for child in self.pack_slaves():
                    if isinstance(child, Label):
                        if hasattr(child, 'message') or child.winfo_name() == "Message":
                            child.pack_forget()

            def create_custom_status_widgets(self):
                """创建自定义状态栏和时间标签"""
                # 创建框架容器
                self.status_frame = Frame(self)
                self.status_frame.pack(side=RIGHT, fill=X, expand=True)

                # 创建状态栏（显示坐标信息）
                self.status_bar = Label(self.status_frame, text="准备就绪", bd=1, relief=SUNKEN, anchor=W)
                self.status_bar.pack(side=LEFT, fill=X, expand=True)

                # 创建时间标签
                self.time_label = Label(self.status_frame, text="", bd=1, relief=SUNKEN, anchor=E, width=20)
                self.time_label.pack(side=RIGHT)

                # 启动时间更新线程
                if self.kline_page:
                    start_time_update(self.time_label)

            def set_message(self, message):
                """重写设置消息方法，将消息显示到我们的状态栏"""
                if hasattr(self, 'status_bar'):
                    self.status_bar.config(text=message)

            # 重写home按钮的功能，使用reset_view代替
            def home(self, *args):
                """重置视图到初始状态（复位视图）"""
                if self.kline_page and hasattr(self.kline_page, 'reset_view'):
                    self.kline_page.reset_view()
                else:
                    # 如果无法访问reset_view，退回到原始的home功能
                    super().home(*args)

            # 重写zoom按钮的功能，增加对放大镜活动状态的跟踪
            def zoom(self, *args):
                """切换放大镜工具"""
                super().zoom(*args)
                self.zoom_active = not self.zoom_active
                # 更新状态栏
                if self.zoom_active:
                    self.status_bar.config(text="放大镜工具已激活 - 左键拖动选择区域进行放大")
                else:
                    self.status_bar.config(text="准备就绪")

        if hasattr(self, 'toolbar'):
            self.toolbar.destroy()
        self.toolbar = CustomNavigationToolbar(self.canvas, self.chart_frame, self)
        self.toolbar.update()

        # 先pack工具栏到底部，再pack canvas填充剩余空间
        self.toolbar.pack(side=BOTTOM, fill=X)
        self.canvas.get_tk_widget().pack(fill=BOTH, expand=True)

        # 设置交互
        self.setup_interactions()

        # 刷新画布
        self.canvas.draw_idle()
        print("图表创建完成")

        # 最后加载真实K线数据
        self.load_kline_data()

    def refresh_chart(self):
        """刷新图表（模仿测试程序的refresh_chart）"""
        print("刷新图表...")
        self.load_kline_data()
        print("图表刷新完成")

    def on_window_configure(self, event):
        """窗口大小变化时的处理"""
        # 只处理窗口本身的配置变化，忽略子组件
        if event.widget != self:
            return
        self.update_layout()

    def on_main_frame_configure(self, event):
        """主框架大小变化时的处理"""
        # 只处理main_frame的配置变化
        if event.widget != self.main_frame:
            return
        self.update_layout()

    def update_layout(self):
        """根据窗口宽度动态更新布局"""
        try:
            # 获取main_frame的实际宽度
            main_width = self.main_frame.winfo_width()
            main_height = self.main_frame.winfo_height()
            chart_width = self.chart_frame_width
            trade_width = self.trade_frame_width
            # 动态宽度分配逻辑
            if main_width <= (self.chart_frame_width):
                # 窗口宽度 <= 1000px：图表充满整个区域，隐藏交易面板
                self.chart_frame.place(x=0, y=0, width=main_width, height=main_height)

            elif main_width <= (self.chart_frame_width + self.trade_frame_width + 20):
                # 窗口宽度 1000-1320px
                self.chart_frame.place(x=0, y=0, width=chart_width, height=main_height)

            elif main_width > (self.chart_frame_width + self.trade_frame_width + 20):
                # 窗口宽度 > 1320px：交易面板固定300px，图表区域继续扩展
                chart_width = main_width - trade_width - 20
                self.chart_frame.place(x=0, y=0, width=chart_width, height=main_height)
            
            self.trade_frame.place(x=chart_width+10, y=5, width=trade_width, height=main_height)

            # 更新chart_frame_width用于figsize计算
            if hasattr(self, 'chart_frame'):
                self.current_chart_width = self.chart_frame.winfo_width()

        except Exception as e:
            # 静默处理布局更新错误，避免影响主要功能
            pass

    def _auto_fix_chart_display(self):
        """自动修复图表显示问题：模拟图表宽度微调操作"""
        try:
            # 获取当前图表宽度
            if hasattr(self, 'chart_frame'):
                current_width = self.chart_frame.winfo_width()
                current_height = self.chart_frame.winfo_height()

                # 微调图表宽度：-1像素然后+1像素，模拟拖动操作
                self.chart_frame.place(width=current_width - 1)
                self.chart_frame.update_idletasks()

                # 恢复原宽度
                self.chart_frame.place(width=current_width)

                # print(f"K线界面自动修复：图表宽度 {current_width} -> {current_width - 1} -> {current_width}")

        except Exception as e:
            # print(f"K线界面自动修复图表显示失败: {e}")
            pass

    def setup_charles_style(self, ax, font_prop):
        """设置charles风格的图表样式"""
        # 设置背景色为白色
        ax.set_facecolor('white')

        # 移除四周边框
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['bottom'].set_visible(False)
        ax.spines['left'].set_visible(False)

        # Y轴移到右侧
        ax.yaxis.tick_right()
        ax.yaxis.set_label_position("right")

        # 设置网格样式 - 仿照charles风格，使用点线
        ax.grid(True, which='major', axis='both',
               color='gray', linestyle=':', linewidth=0.4, alpha=0.7)
        # charles样式没有次要网格线，只有主要网格线
        ax.grid(False, which='minor')

        # 设置刻度样式
        ax.tick_params(axis='both', which='major',
                      labelsize=8, colors='gray',
                      length=4, width=0.5)
        ax.tick_params(axis='both', which='minor',
                      length=0, width=0)  # 隐藏次要刻度

        # 设置字体
        for label in ax.get_xticklabels() + ax.get_yticklabels():
            label.set_fontproperties(font_prop)
            label.set_fontsize(8)
            label.set_color('gray')

        # 移除轴标题
        ax.set_xlabel('')
        ax.set_ylabel('')

        # 设置紧凑的边距
        ax.margins(x=0.01, y=0.02)

    def setup_axis_formatters(self, ax, df_plot, font_prop):
        """设置坐标轴格式化器"""
        try:
            # 获取交易对和价格精度
            symbol = self.current_valid_symbol
            price_precision = self.symbol_config_manager.get_price_precision(symbol)

            # Y轴价格格式化器
            def price_formatter(x, pos):
                rounded_x = round(x, price_precision)
                return f'{rounded_x:,.{price_precision}f}'

            ax.yaxis.set_major_formatter(FuncFormatter(price_formatter))

            # X轴时间格式化器
            def x_date_formatter(x, pos):
                try:
                    x_index = int(round(x))
                    if x_index < 0 or x_index >= len(self.display_klines):
                        return ""

                    # 获取第一根K线的时间
                    first_kline_time = self.display_klines.iloc[0]['Open time']
                    interval = self.interval_var.get()

                    # 根据K线周期和索引计算对应的K线时间
                    if interval.endswith('m'):  # 分钟K线
                        minutes = int(interval[:-1])
                        kline_time = first_kline_time + pd.Timedelta(minutes=minutes * x_index)
                    elif interval.endswith('h'):  # 小时K线
                        hours = int(interval[:-1])
                        kline_time = first_kline_time + pd.Timedelta(hours=hours * x_index)
                    elif interval == '1d':  # 日线
                        kline_time = first_kline_time + pd.Timedelta(days=x_index)
                    elif interval == '1w':  # 周线
                        kline_time = first_kline_time + pd.Timedelta(weeks=x_index)
                    else:  # 默认15分钟
                        kline_time = first_kline_time + pd.Timedelta(minutes=15 * x_index)

                    # 格式化为字符串
                    return kline_time.strftime('%Y-%m-%d %H:%M')
                except:
                    return ""

            ax.xaxis.set_major_formatter(FuncFormatter(x_date_formatter))

            # 设置刻度定位器 - 仿照charles风格
            ax.xaxis.set_major_locator(MaxNLocator(integer=True, nbins=5))
            ax.yaxis.set_major_locator(MaxNLocator(nbins=8))

            # charles样式不使用次要刻度

        except Exception as e:
            logging.error(f"设置坐标轴格式化器失败: {str(e)}")

    def setup_tight_layout(self, fig, ax):
        """设置紧凑布局，减少空白"""
        try:
            # 方法1：使用subplots_adjust精确控制边距
            fig.subplots_adjust(
                left=0.02,    # 左边距2%
                right=0.92,   # 右边距5%（为右侧Y轴留空间）
                top=0.98,     # 上边距2%
                bottom=0.08,  # 下边距8%（为X轴标签留空间）
                hspace=0.0,   # 子图间垂直间距
                wspace=0.0    # 子图间水平间距
            )

            # 方法2：设置更紧凑的轴边距
            # ax.margins(x=0.005, y=0.01)  # 进一步减少轴边距

            # 方法3：调整刻度标签的位置，避免被截断
            # ax.tick_params(axis='x', pad=2)  # X轴标签与轴的距离
            # ax.tick_params(axis='y', pad=2)  # Y轴标签与轴的距离

            # logging.info("紧凑布局设置完成")

        except Exception as e:
            logging.error(f"设置紧凑布局失败: {str(e)}")

    def draw_klines_optimized(self, df_candlesticks, ax, reference_df=None, color_up='g', color_down='r', alpha=0.5, candle_width=0.5, candle_linewidth=0.5, label=None):
        """
        优化的K线绘制方法，合并draw_candles的效果与Collections的性能优化
        参数:
            df_candlesticks: 包含K线数据的DataFrame，需要有Open,High,Low,Close列和时间索引
            ax: matplotlib坐标轴对象
            reference_df: 参考DataFrame，用于确定时间索引在x轴上的位置（可选，默认使用df_candlesticks）
            color_up: 阳线颜色
            color_down: 阴线颜色
            alpha: 箱体透明度
            candle_width: 箱体宽度
            candle_linewidth: 上下影线和边框宽度
            label: 图例标签
        """
        try:
            from matplotlib.collections import LineCollection, PatchCollection
            import matplotlib.patches as patches

            # 如果没有提供reference_df，使用df_candlesticks本身
            if reference_df is None:
                reference_df = df_candlesticks

            # 确保df_candlesticks有正确的时间索引
            if not df_candlesticks.index.name == 'Open time' and 'Open time' in df_candlesticks.columns:
                df_candlesticks = df_candlesticks.set_index('Open time')

            # 准备数据集合
            shadow_lines = []  # 存储影线
            body_rects = []    # 存储K线实体
            border_rects = []  # 存储K线边框
            shadow_colors = []  # 影线颜色
            body_colors = []   # 实体颜色
            border_colors = [] # 边框颜色

            # 遍历每根K线
            for idx, row in df_candlesticks.iterrows():
                # 只处理在reference_df中存在的时间索引
                if idx in reference_df.index:
                    # 获取在reference_df中的x坐标位置
                    x_idx = list(reference_df.index).index(idx)

                    # 获取K线数据
                    open_price = float(row['Open'])
                    high_price = float(row['High'])
                    low_price = float(row['Low'])
                    close_price = float(row['Close'])

                    # 确定K线颜色
                    color = color_up if close_price >= open_price else color_down

                    # 添加上下影线（分为两段，与draw_candles保持一致）
                    # 下影线：从最低价到实体底部
                    shadow_lines.append([(x_idx, low_price), (x_idx, min(open_price, close_price))])
                    shadow_colors.append(color)

                    # 上影线：从实体顶部到最高价
                    shadow_lines.append([(x_idx, max(open_price, close_price)), (x_idx, high_price)])
                    shadow_colors.append(color)

                    # K线实体
                    body_height = abs(close_price - open_price)
                    body_bottom = min(open_price, close_price)

                    # 实体矩形（填充）
                    body_rect = patches.Rectangle(
                        (x_idx - candle_width/2, body_bottom),
                        candle_width,
                        body_height
                    )
                    body_rects.append(body_rect)
                    body_colors.append(color)

                    # 边框矩形（只有边框，无填充）
                    border_rect = patches.Rectangle(
                        (x_idx - candle_width/2, body_bottom),
                        candle_width,
                        body_height
                    )
                    border_rects.append(border_rect)
                    border_colors.append(color)

            # 使用LineCollection批量绘制影线
            if shadow_lines:
                shadow_collection = LineCollection(shadow_lines, colors=shadow_colors,
                                                 linewidths=candle_linewidth, alpha=1.0)
                ax.add_collection(shadow_collection)

            # 使用PatchCollection批量绘制K线实体（填充）
            if body_rects:
                body_collection = PatchCollection(body_rects,
                                                facecolors=body_colors,
                                                edgecolors=body_colors,
                                                alpha=alpha, linewidths=0)
                ax.add_collection(body_collection)

            # 使用PatchCollection批量绘制K线边框
            if border_rects:
                border_collection = PatchCollection(border_rects,
                                                  facecolors='none',
                                                  edgecolors=border_colors,
                                                  alpha=1.0, linewidths=candle_linewidth)
                ax.add_collection(border_collection)

            # 设置坐标轴范围
            # if len(df_candlesticks) > 0:
            #     ax.set_xlim(-0.5, len(reference_df) - 0.5)

            #     # 设置Y轴范围，留出适当空间
            #     y_min = df_candlesticks['Low'].min()
            #     y_max = df_candlesticks['High'].max()
            #     y_range = y_max - y_min
            #     ax.set_ylim(y_min - y_range * 0.05, y_max + y_range * 0.05)

            # logging.info(f"优化K线绘制完成，共绘制{len(df_candlesticks)}根K线")

        except Exception as e:
            logging.error(f"优化K线绘制失败: {str(e)}")
            # 如果优化方法失败，显示错误信息
            ax.text(0.5, 0.5, '图表加载失败', transform=ax.transAxes,
                   ha='center', va='center', fontsize=12, color='red')

    def draw_kline_removable(self, df_klines, ax, reference_df, color_up='g', color_down='r', alpha=0.8, candle_width=0.5, candle_linewidth=0.5):
        """
        绘制可移除的K线，返回绘制对象列表用于后续移除
        用于所有需要动态更新的K线（包括单根或多根）

        参数:
            df_klines: K线数据的DataFrame（可以是单根或多根）
            ax: matplotlib坐标轴对象
            reference_df: 参考DataFrame，用于确定时间索引在x轴上的位置
            color_up: 阳线颜色
            color_down: 阴线颜色
            alpha: 透明度
            candle_width: K线宽度
            candle_linewidth: 线宽

        返回:
            绘制对象列表，可用于移除
        """
        try:
            import matplotlib.patches as patches

            kline_objects = []

            # 确保df_klines有正确的时间索引
            if not df_klines.index.name == 'Open time' and 'Open time' in df_klines.columns:
                df_klines = df_klines.set_index('Open time')

            # 遍历K线数据
            for idx, row in df_klines.iterrows():
                if idx in reference_df.index:
                    # 获取x坐标位置
                    x_idx = list(reference_df.index).index(idx)

                    # 获取K线数据
                    open_price = float(row['Open'])
                    high_price = float(row['High'])
                    low_price = float(row['Low'])
                    close_price = float(row['Close'])

                    # 确定颜色
                    color = color_up if close_price >= open_price else color_down

                    # 绘制上下影线（分为两段）
                    # 下影线
                    line1 = ax.plot([x_idx, x_idx], [low_price, min(open_price, close_price)],
                                   color=color, linewidth=candle_linewidth, alpha=1.0, zorder=10)[0]
                    kline_objects.append(line1)

                    # 上影线
                    line2 = ax.plot([x_idx, x_idx], [max(open_price, close_price), high_price],
                                   color=color, linewidth=candle_linewidth, alpha=1.0, zorder=10)[0]
                    kline_objects.append(line2)

                    # K线实体
                    body_height = abs(close_price - open_price)
                    body_bottom = min(open_price, close_price)

                    # 实体矩形（填充）
                    body_rect = patches.Rectangle(
                        (x_idx - candle_width/2, body_bottom),
                        candle_width, body_height,
                        facecolor=color, edgecolor=color,
                        alpha=alpha, linewidth=0, zorder=10
                    )
                    kline_objects.append(ax.add_patch(body_rect))

                    # 边框矩形
                    border_rect = patches.Rectangle(
                        (x_idx - candle_width/2, body_bottom),
                        candle_width, body_height,
                        facecolor='none', edgecolor=color,
                        alpha=1.0, linewidth=candle_linewidth, zorder=10
                    )
                    kline_objects.append(ax.add_patch(border_rect))

            return kline_objects

        except Exception as e:
            logging.error(f"绘制单根可移除K线失败: {str(e)}")
            return []

    def remove_kline_objects(self, kline_objects):
        """移除K线对象列表"""
        try:
            for obj in kline_objects:
                # 检查对象是否为None或已被清除
                if obj is None:
                    continue

                # 检查对象是否还有axes属性（可能已被清除）
                if not hasattr(obj, 'axes') or obj.axes is None:
                    continue

                # 检查对象是否还在axes中
                try:
                    if hasattr(obj.axes, 'lines') and obj in obj.axes.lines:
                        obj.remove()
                    elif hasattr(obj.axes, 'patches') and obj in obj.axes.patches:
                        obj.remove()
                except (AttributeError, ValueError):
                    # 对象可能已经被移除或axes已被清除
                    pass
        except Exception as e:
            logging.error(f"移除K线对象失败: {str(e)}")



    def setup_interactions(self):
        """设置图表交互功能"""
        # 记录初始状态便于重置
        self.original_xlim = self.ax_main.get_xlim()
        self.original_ylim = self.ax_main.get_ylim()
        
        # 设置默认光标为十字星
        if hasattr(self, 'canvas'):
            self.canvas.get_tk_widget().config(cursor="crosshair")
        
        # 保存当前平移状态
        self.pan_active = False
        self.pan_start_x = None
        self.pan_start_y = None
        
        # 保存当前尺度调整状态
        self.scale_active = False
        self.scale_start_x = None
        self.scale_start_y = None
        self.last_pixel_dx = 0
        self.last_pixel_dy = 0
        
        # 添加用户交互状态跟踪
        self.user_interacting = False
        self.last_refresh_time = time.time()
        
        # 绑定窗口交互事件 - 简化实现
        self.root = self.winfo_toplevel()
        # 检测窗口拖动/调整大小开始（鼠标按下时）
        self.root.bind("<ButtonPress-1>", self.on_window_interaction_start)
        # 检测窗口拖动/调整大小结束（鼠标松开时）
        self.root.bind("<ButtonRelease-1>", self.on_window_interaction_end)
        
        # 绑定鼠标事件
        self.cid_press = self.canvas.mpl_connect('button_press_event', self.on_mouse_press)
        self.cid_motion = self.canvas.mpl_connect('motion_notify_event', self.on_mouse_move)
        self.cid_scroll = self.canvas.mpl_connect('scroll_event', self.on_scroll)
        
        # 绑定鼠标释放事件到根窗口，以便捕获图表外的鼠标释放
        self.root.bind("<ButtonRelease-1>", self.on_root_mouse_release)
        self.root.bind("<ButtonRelease-3>", self.on_root_mouse_release)
        
    def on_window_interaction_start(self, event):
        """当用户开始拖动窗口或调整窗口大小时"""
        # 检查鼠标是否在窗口边缘或标题栏
        # 这里不需要复杂的计算，直接检查鼠标当前位置是否在Canvas小部件内
        if not self.canvas.get_tk_widget().winfo_containing(event.x_root, event.y_root):
            # 如果鼠标不在Canvas内，说明可能在拖动窗口或调整窗口大小
            self.user_interacting = True
            print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}, 检测到窗口拖动/调整大小，暂停刷新")
    
    def on_window_interaction_end(self, event):
        """当用户结束拖动窗口或调整窗口大小时"""
        # 鼠标释放时，延迟一小段时间后设置交互状态为False
        self.after(200, self._end_user_interaction)
        
    def _end_user_interaction(self):
        """结束用户交互状态"""
        self.user_interacting = False

    def on_mouse_press(self, event):
        """处理鼠标按下事件"""
        # 标记用户正在交互
        self.user_interacting = True
        
        # 检查放大镜工具是否激活
        if hasattr(self, 'toolbar') and hasattr(self.toolbar, 'zoom_active') and self.toolbar.zoom_active:
            # 放大镜工具激活时，不执行自定义的拖拽功能
            return
        
        if event.button == 1 and event.inaxes == self.ax_main:  # 左键且在主图轴内
            # 开始平移
            self.pan_active = True
            self.pan_start_x = event.xdata
            self.pan_start_y = event.ydata
            self.canvas.get_tk_widget().config(cursor="fleur")  # 更改光标为手型
        elif event.button == 3 and event.inaxes == self.ax_main:  # 右键且在主图轴内
            # 开始调整尺度
            self.scale_active = True
            # 记录鼠标在屏幕上的位置（像素坐标）
            self.scale_start_x = event.x
            self.scale_start_y = event.y
            # 记录当前图表范围作为初始状态
            self.scale_xlim = self.ax_main.get_xlim()
            self.scale_ylim = self.ax_main.get_ylim()
            # 重置上次像素偏移量
            self.last_pixel_dx = 0
            self.last_pixel_dy = 0
            self.canvas.get_tk_widget().config(cursor="sizing")  # 更改光标为调整尺寸
            
    def on_root_mouse_release(self, event):
        """处理鼠标释放事件（全窗口范围）"""
        if event.num == 1:  # 左键
            # 结束平移
            self.pan_active = False
            self.canvas.get_tk_widget().config(cursor="crosshair")  # 恢复为十字星光标
        elif event.num == 3:  # 右键
            # 结束调整尺度
            self.scale_active = False
            self.canvas.get_tk_widget().config(cursor="crosshair")  # 恢复为十字星光标
        # 给一点延迟再标记用户交互结束，避免立即刷新
        self.user_interacting = False

    def handle_kline_interval_input_finished(self, event=None):
        """处理K线刷新间隔输入框的输入完成事件 (回车或失焦)"""
        try:
            current_input = self.kline_refresh_interval_var.get()
            new_interval_s = float(current_input)

            if new_interval_s < 0.5:
                messagebox.showinfo("提示", "K线刷新间隔不能小于0.5秒，已自动调整为0.5秒")
                new_interval_s = 0.5
            
            self.active_kline_refresh_s = new_interval_s
            self.kline_refresh_interval_var.set(str(self.active_kline_refresh_s))  # 更新输入框为有效或修正后的值

        except ValueError:
            messagebox.showerror("输入错误", "请输入有效的K线刷新间隔时间（例如 1 或 0.5）。")
            # 输入无效，将输入框恢复为上一个有效的激活间隔
            self.kline_refresh_interval_var.set(str(self.active_kline_refresh_s))

    def auto_refresh_price_line(self):
        # 自动刷新价格线和相关UI元素 (此任务始终运行)
        try:
            self.update_price_line()
            self.canvas.draw_idle()
            if not self.user_interacting:  
                self.canvas.get_tk_widget().config(cursor="crosshair") # 保持光标样式
            
            # 重新调度下一次价格线刷新
            # # 在设置新的任务前，取消任何已存在的旧价格线刷新任务
            if hasattr(self, 'price_refresh_job'):
                self.after_cancel(self.price_refresh_job)
            self.price_refresh_job = self.after(200, self.auto_refresh_price_line)

        except Exception as e:
            logging.error(f"价格线自动刷新失败: {str(e)}")
            # 尝试重新调度下一次刷新，确保刷新循环不会中断
            self.after(1000, self.auto_refresh_price_line)

    def update_price_line(self):
        """更新最新价格线和标签"""
        try:
            # 确保有K线数据和图表
            if not hasattr(self, 'klines_df') or self.klines_df.empty or not hasattr(self, 'ax_main'):
                return
                
            # 获取最新价格
            latest_price = float(self.klines_df.iloc[-1]['Close'])
            
            # 获取当前交易对的价格精度
            symbol = self.current_valid_symbol
            price_precision = self.symbol_config_manager.get_price_precision(symbol)
            
            # 删除旧的价格线和标签
            if hasattr(self, 'price_line') and self.price_line:
                self.price_line.remove()
                self.price_line = None
            
            if hasattr(self, 'price_label') and self.price_label:
                self.price_label.remove()
                self.price_label = None
            
            # 创建新的价格线（虚线），使用灰色并减小线宽
            self.price_line = self.ax_main.axhline(y=latest_price, color='gray', linestyle='--', linewidth=0.5)
            
            # 获取微软雅黑字体
            font_path = 'C:/Windows/Fonts/msyh.ttc'
            if not os.path.exists(font_path):
                font_path = 'C:/Windows/Fonts/simsun.ttc'  # 宋体
            
            font_prop = fm.FontProperties(fname=font_path)
            
            # 生成价格标签文本
            price_text = self.symbol_config_manager.format_price(symbol, latest_price)
            
            # 获取图表的x轴范围，确保标签在最右侧
            xmax = 1.049  # 使用相对坐标，1.0表示最右侧
            
            # 创建包含价格和倒计时的组合标签       
            self.price_label = self.ax_main.text(
                xmax, latest_price,  # 放在最右侧
                f"{price_text}\n--:--",  # 初始文本，第一行是价格，第二行是倒计时
                color='white',  # 标签文字改为白色
                fontproperties=font_prop,
                fontsize=8,  # 与最大最小值标签字号一致
                va='center',  # 垂直居中对齐
                ha='center',  # 水平居中对齐
                transform=self.ax_main.get_yaxis_transform(),  # 使用y轴坐标系统
                bbox=dict(boxstyle='round', fc='gray', ec='white', alpha=0.7, pad=0.5),  # 增加内边距使文字更居中
                linespacing=1.2  # 行间距
            )

            # 获取当前K线间隔
            interval = self.interval_var.get()

            # 将价格标签同时作为倒计时标签
            self.countdown_label = self.price_label
            time_manager.add_countdown_label(self.countdown_label, interval)
            label_text = self.countdown_label.get_text() if hasattr(self.countdown_label, 'get_text') else ""
            countdown_part = label_text.split('\n')[1] if '\n' in label_text else label_text
            # 如果倒计时为00:00或00:00:00，且未开启自动刷新，则强制更新K线
            if (countdown_part == "00:00" or countdown_part == "00:00:00") and \
                hasattr(self, 'auto_refresh_kline_var') and not self.auto_refresh_kline_var.get():
                # 延迟1秒后刷新K线
                # self.after(2000, lambda: self.update_kline_data(force_refresh=True))
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                # print(f'{current_time}, 倒计时归零，更新K线数据')
                return  # 已经安排了延迟刷新K线，不需要继续定时器
            
        except Exception as e:
            logging.error(f"更新价格线失败: {str(e)}")
            traceback.print_exc()

    def start_kline_data_update(self):
        """启动K线数据更新线程"""
        self.stop_kline_data_update()
        # 设置可用资金更新线程并启动
        self.kline_data_update_job = threading.Event()
        self.kline_data_update_job.set()
        if not hasattr(self, 'kline_data_update_thread') or not self.kline_data_update_thread.is_alive():
            self.kline_data_update_thread = threading.Thread(target=self.update_kline_data_loop, daemon=True)
            self.kline_data_update_thread.start()

    def stop_kline_data_update(self):
        """停止K线数据更新线程"""
        if hasattr(self, 'kline_data_update_job'):
            self.kline_data_update_job.clear()

    def toggle_kline_update(self):
        """开启或停止K线刷新线程"""
        if self.auto_refresh_kline_var.get() == 1:
            self.start_kline_data_update()
        else:
            self.stop_kline_data_update()

    def update_kline_data_loop(self):
        # 自动刷新K线数据
        while self.kline_data_update_job.is_set():   
            try:
                self.update_kline_data()
            except Exception as e:
                logging.error(f"K线数据自动刷新失败: {str(e)}")
            time.sleep(self.active_kline_refresh_s)


    def update_kline_data(self, force_refresh=False):
        """只更新新的K线数据，不重新生成整个图表"""
        try:
            # 获取标签和时间周期
            symbol = self.current_valid_symbol
            interval = self.interval_var.get()
            
            # 标记是否有数据更新
            data_updated = False
            
            # 检查是否已经初始化了图表
            if not hasattr(self, 'fig') or not hasattr(self, 'ax_main') or not hasattr(self, 'klines_df'):
                # 如果图表还没有初始化，那么调用完整的加载方法
                # 注意：这里需要在主线程中执行，所以使用after方法
                self.after(0, self.load_kline_data)
                return True

            # 从API获取最新的K线数据（带超时控制）
            api_klines, api_success = self._api_call_with_timeout(
                self.client.klines,
                symbol=symbol,
                interval=interval,
                limit=10,  # 只获取最近的几条记录
                timeout=3.0
            )

            if not api_success:
                logging.error("更新K线数据API调用失败")
                return False
            
            # 将API返回的K线数据转换为DataFrame
            api_df = pd.DataFrame(api_klines, columns=[
                'Open time', 'Open', 'High', 'Low', 'Close', 'Volume',
                'Close time', 'Quote asset volume', 'Number of trades',
                'Taker buy base asset volume', 'Taker buy quote asset volume', 'Ignore'
            ])
            
            # 转换数据类型
            api_df['Open time'] = pd.to_datetime(api_df['Open time'], unit='ms') + pd.Timedelta(hours=8)
            api_df['Close time'] = pd.to_datetime(api_df['Close time'], unit='ms') + pd.Timedelta(hours=8)
            
            api_df = api_df.astype({
                'Open': float, 'High': float, 'Low': float,
                'Close': float, 'Volume': float
            })
            
            # 如果成功获取K线数据
            if not api_df.empty:
                # 获取当前图表上最新K线的时间
                if not self.klines_df.empty:
                    last_kline_time = self.klines_df['Open time'].max()
                    
                    # 找出比最新K线更新的K线
                    new_klines = api_df[api_df['Open time'] > last_kline_time]
                    
                    # 初始化updated_klines为空DataFrame
                    updated_klines = pd.DataFrame(columns=api_df.columns)
                    current_kline = pd.DataFrame(columns=api_df.columns)
                                        
                    # 如果发现了新K线
                    if not new_klines.empty:                     
                        # 更新上一根K线的状态
                        last_kline = api_df[api_df['Open time'] == last_kline_time]
                        
                        # 这样可以确保它在klines_df中有最新的状态
                        if not last_kline.empty:
                            # 从集合中移除旧版本
                            self.klines_df = self.klines_df[self.klines_df['Open time'] != last_kline_time]
       
                            # 在updated_klines列表中，添加last_kline
                            updated_klines = last_kline.copy()
                        
                        # 排除最新的一根K线（当前正在形成的）
                        new_klines_except_last = new_klines.iloc[:-1]
                        
                        if not new_klines_except_last.empty:
                            # 将new_klines_except_last添加到updated_klines中
                            if updated_klines.empty:
                                updated_klines = new_klines_except_last.copy()
                            else:
                                updated_klines = pd.concat([updated_klines, new_klines_except_last])
                            # 确保没有重复
                            updated_klines = updated_klines.drop_duplicates(subset=['Open time'], keep='last')
                            # 按时间排序
                            updated_klines = updated_klines.sort_values(by='Open time')

                        # 将updated_klines更新到K线集合
                        self.klines_df = pd.concat([self.klines_df, updated_klines])
                        # 确保klines_df没有重复
                        self.klines_df = self.klines_df.drop_duplicates(subset=['Open time'], keep='last')
                        # 按时间排序
                        self.klines_df = self.klines_df.sort_values(by='Open time')
                        # 将updated_klines保存到本地
                        self.save_klines_to_local(updated_klines, symbol, interval)
                        logging.info(f"已保存 {len(updated_klines)} 根已收盘K线，添加新的K线")

                        # 添加当前K线到数据集
                        current_kline = new_klines.iloc[-1:]
                        self.klines_df = pd.concat([self.klines_df, current_kline])
                        # 确保没有重复
                        self.klines_df = self.klines_df.drop_duplicates(subset=['Open time'], keep='last')
                        # 按时间排序
                        self.klines_df = self.klines_df.sort_values(by='Open time')
                        # 重置索引
                        self.klines_df = self.klines_df.reset_index(drop=True)

                        data_updated = True
                
                    else:
                        # 检查当前正在形成的K线（api中的最新一根）
                        current_kline = api_df.iloc[-1:]
                        
                        # 找到需要更新的K线在DataFrame中的索引
                        last_index = self.klines_df['Open time'] == last_kline_time
                        
                        # 检查值是否有变化
                        high_changed = float(current_kline['High'].iloc[0]) > float(self.klines_df.loc[last_index, 'High'].iloc[0])
                        low_changed = float(current_kline['Low'].iloc[0]) < float(self.klines_df.loc[last_index, 'Low'].iloc[0])
                        close_changed = float(current_kline['Close'].iloc[0]) != float(self.klines_df.loc[last_index, 'Close'].iloc[0])
                        
                        # 只在数据有变化时更新
                        if high_changed or low_changed or close_changed:
                            # 更新最高价、最低价和收盘价
                            if high_changed:
                                self.klines_df.loc[last_index, 'High'] = float(current_kline['High'].iloc[0])
                            
                            if low_changed:
                                self.klines_df.loc[last_index, 'Low'] = float(current_kline['Low'].iloc[0])
                            
                            self.klines_df.loc[last_index, 'Close'] = float(current_kline['Close'].iloc[0])
                            self.klines_df.loc[last_index, 'Volume'] = float(current_kline['Volume'].iloc[0])
                            data_updated = True
            # 强制刷新
            if force_refresh:
                data_updated = True

            # 如果有数据更新，在主线程中更新UI
            if data_updated or force_refresh:
                # 使用after方法将UI更新操作放回主线程
                self.after(0, lambda: self.update_new_klines(updated_klines if 'updated_klines' in locals() else None, 
                                                           current_kline if 'current_kline' in locals() else None))
                return True
            
            # 如果没有任何更新，返回False
            logging.info("自动刷新：无新数据")
            return False
            
        except Exception as e:
            logging.error(f"更新K线数据失败: {str(e)}")
            import traceback
            traceback.print_exc()  # 打印详细错误信息
            return False

    def update_new_klines(self, updated_klines=None, current_kline=None):
        """在更新图表UI"""
        try:
            # 首先绘制主图上所有K线和指标
            # 然后单独绘制updated_klines
            # 最后单独绘制current_kline
            df_plot = self.klines_df.copy()
            df_plot.set_index('Open time', inplace=True)
            
            # 1. 如果有updated_klines，单独绘制它们
            if updated_klines is not None and not updated_klines.empty:
                logging.info(f"绘制 {len(updated_klines)} 根更新的K线")

                # 使用可移除的方法绘制updated_klines
                updated_candles = self.draw_kline_removable(
                    updated_klines,
                    self.ax_main,
                    df_plot,
                    color_up='g',
                    color_down='r',
                    alpha=0.5,
                    candle_width=0.5,
                    candle_linewidth=0.5   # 已收盘K线
                )
                # 存储对象引用以便后续可以移除
                self.updated_candles_objects = updated_candles
                
            # 2. 如果有current_kline，单独绘制它
            if hasattr(self, 'current_kline_objects') and self.current_kline_objects:
                # 移除旧的current_kline对象（只在ax没有被清除的情况下）
                # 由于load_kline_data调用了ax.clear()，这些对象已经被清除，无需手动移除
                if hasattr(self.ax_main, 'lines') and len(self.ax_main.lines) > 0:
                    self.remove_kline_objects(self.current_kline_objects)
                self.current_kline_objects = []

            if not current_kline.empty:
                # logging.info(f"绘制当前K线")

                # 使用可移除的方法绘制current_kline
                self.current_kline_objects = self.draw_kline_removable(
                    current_kline,
                    self.ax_main,
                    df_plot,
                    color_up='g',
                    color_down='r',
                    alpha=0.8,  # 当前K线透明度稍低
                    candle_width=0.5,
                    candle_linewidth=0.5
                )

            # 绘制技术指标
            self.on_indicator_change()
            
            # 更新高低价范围标签
            self.update_range_labels()

            # 更新价格线
            self.update_price_line()
            
            # 刷新画布
            self.canvas.draw_idle()
            # 恢复十字星光标，防止K线更新导致光标变成默认箭头
            self.canvas.get_tk_widget().config(cursor="crosshair")
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            logging.info(f'更新K线数据和指标')

        except Exception as e:
            logging.error(f"更新K线数据失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_mouse_move(self, event):
        """处理鼠标移动事件"""
        # 更新状态栏显示坐标
        if event.inaxes == self.ax_main and hasattr(self, 'display_klines') and not self.display_klines.empty:
            try:
                # 获取鼠标位置的x坐标，四舍五入到最近的整数索引
                x_index = round(event.xdata)
                
                # 获取第一根K线的时间
                first_kline_time = self.display_klines.iloc[0]['Open time']
                interval = self.interval_var.get()
                
                # 根据K线周期和索引计算对应的K线时间
                if interval.endswith('m'):  # 分钟K线
                    minutes = int(interval[:-1])
                    kline_time = first_kline_time + pd.Timedelta(minutes=minutes * x_index)
                elif interval.endswith('h'):  # 小时K线
                    hours = int(interval[:-1])
                    kline_time = first_kline_time + pd.Timedelta(hours=hours * x_index)
                elif interval == '1d':  # 日线
                    kline_time = first_kline_time + pd.Timedelta(days=x_index)
                elif interval == '1w':  # 周线
                    kline_time = first_kline_time + pd.Timedelta(weeks=x_index)
                else:  # 默认15分钟
                    kline_time = first_kline_time + pd.Timedelta(minutes=15 * x_index)
                
                # 格式化为字符串
                date_str = kline_time.strftime('%Y-%m-%d %H:%M')
                
                # 获取价格
                price = event.ydata
                
                # 更新状态栏
                if hasattr(self, 'toolbar') and hasattr(self.toolbar, 'status_bar'):
                    self.toolbar.status_bar.config(text=f"K线: {date_str} | 价格: {price:.2f} | 索引: {x_index}")
            except Exception as e:
                # 出错时显示原始坐标
                if hasattr(self, 'toolbar') and hasattr(self.toolbar, 'status_bar'):
                    self.toolbar.status_bar.config(text=f"x: {event.xdata:.2f} | 价格: {event.ydata:.2f}")
        
        # 处理左键平移
        if self.pan_active and event.xdata is not None and event.ydata is not None and event.inaxes == self.ax_main:
            # 计算平移量
            dx = self.pan_start_x - event.xdata
            dy = self.pan_start_y - event.ydata
            
            # 获取当前轴范围
            x_min, x_max = self.ax_main.get_xlim()
            y_min, y_max = self.ax_main.get_ylim()
            
            # 平移
            self.ax_main.set_xlim(x_min + dx, x_max + dx)
            self.ax_main.set_ylim(y_min + dy, y_max + dy)
            
            # 更新图表
            self.update_range_labels()
            self.canvas.draw_idle()
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f'{current_time}, 平移中')
            
        # 处理右键缩放 - 即使鼠标移出图表区域也继续工作
        elif self.scale_active:
            # 计算鼠标在屏幕上的移动距离（像素）
            if hasattr(event, 'x') and hasattr(event, 'y'):
                pixel_dx = event.x - self.scale_start_x
                pixel_dy = event.y - self.scale_start_y
                # 保存最后的像素偏移量
                self.last_pixel_dx = pixel_dx
                self.last_pixel_dy = pixel_dy
            else:
                # 没有像素坐标时，使用最后记录的值
                pixel_dx = self.last_pixel_dx
                pixel_dy = self.last_pixel_dy
            
            # 获取右键按下时保存的初始坐标范围
            x_min, x_max = self.scale_xlim
            y_min, y_max = self.scale_ylim
            
            # 计算初始坐标范围
            x_range = x_max - x_min
            y_range = y_max - y_min
            
            # 设定缩放系数 - 拖拽像素与缩放比例的转换
            x_pixels_for_half = 100  # 需要拖动多少像素才能让范围减半
            y_pixels_for_half = 100
            
            # 计算缩放比例（使用指数函数，实现线性感知的缩放）
            x_scale = 2 ** (-pixel_dx / x_pixels_for_half)  # 右拖100像素，范围变为原来的1/2
            y_scale = 2 ** (-pixel_dy / y_pixels_for_half)  # 下拖100像素，范围变为原来的1/2
            
            # 计算缩放中心点
            x_center = (x_min + x_max) / 2
            y_center = (y_min + y_max) / 2
            
            # 保持中心点不变，调整坐标范围
            new_x_range = x_range * x_scale
            new_y_range = y_range * y_scale
            
            # 确保范围为正值，避免反转或收缩为零
            min_scale = 0.01  # 允许的最小缩放比例，防止范围太小
            new_x_range = max(new_x_range, x_range * min_scale)
            new_y_range = max(new_y_range, y_range * min_scale)
            
            # 计算新的坐标边界
            x_min_new = x_center - new_x_range / 2
            x_max_new = x_center + new_x_range / 2
            y_min_new = y_center - new_y_range / 2
            y_max_new = y_center + new_y_range / 2
            
            # 更新图表坐标范围
            self.ax_main.set_xlim(x_min_new, x_max_new)
            self.ax_main.set_ylim(y_min_new, y_max_new)
            
            # 更新状态栏显示当前缩放比例和范围
            # 计算当前视图下可见的K线索引范围
            xlim = self.ax_main.get_xlim()
            ylim = self.ax_main.get_ylim()
            
            # 修改可见K线的计算逻辑
            # 只有当K线的中心点（整数索引）在视图范围内时才计算为可见
            # 向上取整，确保只有当K线中心点在边界内才算该K线可见
            start_index = math.ceil(xlim[0])
            # 向下取整，确保只有当K线中心点在边界内才算该K线可见
            end_index = math.floor(xlim[1])
            
            # 确保索引在有效范围内
            total_bars = len(self.klines_df) if hasattr(self, 'klines_df') else 0
            start_index = max(0, start_index)
            end_index = min(total_bars - 1, end_index) if total_bars > 0 else 0
            
            # 获取可见范围内的价格和时间
            if hasattr(self, 'klines_df') and not self.klines_df.empty and start_index <= end_index and end_index < total_bars:
                visible_bars = self.klines_df.iloc[start_index:end_index+1]
                if not visible_bars.empty:
                    # 使用正确的列名
                    first_time = visible_bars.iloc[0]['Open time'].strftime('%Y-%m-%d %H:%M') if 'Open time' in visible_bars.columns else "N/A"
                    last_time = visible_bars.iloc[-1]['Open time'].strftime('%Y-%m-%d %H:%M') if 'Open time' in visible_bars.columns else "N/A"
                    min_price = visible_bars['Low'].min() if 'Low' in visible_bars.columns else 0
                    max_price = visible_bars['High'].max() if 'High' in visible_bars.columns else 0
                    
                    # 更新状态栏信息
                    scale_info = f"缩放比例: X: {1/x_scale:.2f}x, Y: {1/y_scale:.2f}x | 时间: {first_time} - {last_time} | 价格: {min_price} - {max_price}"
                else:
                    scale_info = f"缩放比例: X: {1/x_scale:.2f}x, Y: {1/y_scale:.2f}x | 无可见数据"
            else:
                scale_info = f"缩放比例: X: {1/x_scale:.2f}x, Y: {1/y_scale:.2f}x | 范围: X: {new_x_range:.2f}, Y: {new_y_range:.2f}"
            
            if hasattr(self, 'toolbar') and hasattr(self.toolbar, 'status_bar'):
                self.toolbar.status_bar.config(text=scale_info)
            
            # 刷新Canvas
            self.update_range_labels()
            self.canvas.draw_idle()
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f'{current_time}, 缩放中')

    def on_scroll(self, event):
        """处理鼠标滚轮事件"""
        # 标记用户正在交互
        self.user_interacting = True
        
        if event.inaxes != self.ax_main:
            # 设置一个短暂的延时后将交互状态设为False
            self.after(500, self._end_user_interaction)
            return
        
        # 获取当前的x和y范围
        xlim = self.ax_main.get_xlim()
        ylim = self.ax_main.get_ylim()
        
        # 计算鼠标位置
        x_center = event.xdata
        y_center = event.ydata
        
        # 计算当前视图范围的宽度和高度
        x_width = xlim[1] - xlim[0]
        y_height = ylim[1] - ylim[0]
        
        # 缩放因子
        scale_factor = 1.2
        
        # 根据滚轮方向确定是放大还是缩小
        if event.button == 'up':  # 放大
            # 计算新的x轴范围
            new_x_width = x_width / scale_factor
            # 确保放大时保持鼠标位置不变
            new_xlim = (
                x_center - new_x_width * (x_center - xlim[0]) / x_width,
                x_center + new_x_width * (xlim[1] - x_center) / x_width
            )
            
            # 计算新的y轴范围
            new_y_height = y_height / scale_factor
            # 确保放大时保持鼠标位置不变
            new_ylim = (
                y_center - new_y_height * (y_center - ylim[0]) / y_height,
                y_center + new_y_height * (ylim[1] - y_center) / y_height
            )
        else:  # 缩小
            # 计算新的x轴范围
            new_x_width = x_width * scale_factor
            # 确保缩小时保持鼠标位置不变
            new_xlim = (
                x_center - new_x_width * (x_center - xlim[0]) / x_width,
                x_center + new_x_width * (xlim[1] - x_center) / x_width
            )
            
            # 计算新的y轴范围
            new_y_height = y_height * scale_factor
            # 确保缩小时保持鼠标位置不变
            new_ylim = (
                y_center - new_y_height * (y_center - ylim[0]) / y_height,
                y_center + new_y_height * (ylim[1] - y_center) / y_height
            )
            
        # 更新图表范围
        self.ax_main.set_xlim(new_xlim)
        self.ax_main.set_ylim(new_ylim)
        
        # 刷新Canvas
        self.update_range_labels()
        self.canvas.draw_idle()
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f'{current_time}, 滚轮缩放中')

        # 设置一个短暂的延时后将交互状态设为False
        self.after(500, self._end_user_interaction)
        
        # 防止事件传递
        return "break"

    def _end_user_interaction(self):
        """结束用户交互状态"""
        self.user_interacting = False

    def start_balance_update(self):
        """启动可用资金更新"""
        self.stop_balance_update()
        # 设置可用资金更新线程并启动
        self.balance_update_job = threading.Event()
        self.balance_update_job.set()
        if not hasattr(self, 'balance_update_thread') or not self.balance_update_thread.is_alive():
            self.balance_update_thread = threading.Thread(target=self.balance_update_loop, daemon=True)
            self.balance_update_thread.start()

    def stop_balance_update(self):
        """停止可用资金更新"""
        if hasattr(self, 'balance_update_job'):
            self.balance_update_job.clear()

    def balance_update_loop(self):
        """在线程中执行可用资金更新"""
        while self.balance_update_job.is_set():
            try:
                self.balance_update()
            except Exception as e:
                logging.error(f"更新可用资金失败")
            time.sleep(2)  # 每2秒更新一次

    def balance_update(self):
        """线程中执行的可用资金更新操作"""
        try:
            account_info = self.client.account()
            available_balance = float(account_info.get('availableBalance', 0))
            # 使用after方法将UI更新操作放回主线程
            self.after(0, lambda: self.balance_label.config(text=f"{available_balance:.2f} USDT"))
        except Exception as e:
            logging.error(f"更新可用资金失败")

    def update_margin_leverage_info(self):
        """更新保证金模式和杠杆倍数信息"""
        try:
            symbol = self.current_valid_symbol
            # 从本地配置系统获取信息
            config = self.symbol_config_manager.get_symbol_config(symbol)
            
            # 获取保证金模式
            margin_type = config.get('marginType', 'CROSSED')
            margin_text = "全仓" if margin_type == "CROSSED" else "逐仓"
            self.margin_button.config(text=f"{margin_text}")

            # 获取杠杆倍数
            leverage = config.get('leverage', 1)
            self.leverage_button.config(text=f"{leverage}x")
            
            # 保存当前状态
            self.current_margin_type = margin_type
            self.current_leverage = leverage
            
        except Exception as e:
            logging.error(f"更新保证金和杠杆信息失败: {str(e)}")
            self.margin_button.config(text="获取失败")
            self.leverage_button.config(text="获取失败")

    def change_margin_type(self):
        """切换保证金模式"""
        try:
            symbol = self.current_valid_symbol
            if not hasattr(self, 'current_margin_type'):
                self.update_margin_leverage_info()
            
            # 创建对话框
            dialog = Toplevel(self)
            dialog.title("切换保证金模式")
            dialog.geometry("300x150")
            dialog.transient(self)  # 设置为主窗口的临时窗口
            dialog.grab_set()  # 模态对话框
            
            # 使用Frame包装单选按钮，方便设置背景色
            radio_frame = Frame(dialog, bg='white')
            radio_frame.pack(fill='both', expand=True, padx=20, pady=10)
            
            # 设置当前模式
            mode_var = StringVar(value=self.current_margin_type)
            
            # 创建单选按钮组
            style = ttk.Style()
            style.configure('Selected.TRadiobutton', background='#e6e6e6')
            
            crossed_radio = ttk.Radiobutton(radio_frame, text="全仓", value="CROSSED",
                          variable=mode_var, style='Selected.TRadiobutton' if self.current_margin_type == "CROSSED" else '')
            crossed_radio.pack(pady=5)
            
            isolated_radio = ttk.Radiobutton(radio_frame, text="逐仓", value="ISOLATED",
                          variable=mode_var, style='Selected.TRadiobutton' if self.current_margin_type == "ISOLATED" else '')
            isolated_radio.pack(pady=5)
            
            def apply_change():
                try:
                    new_mode = mode_var.get()
                    if new_mode != self.current_margin_type:  # 只在发生改变时发送请求
                        self.client.change_margin_type(symbol=symbol, marginType=new_mode)
                        messagebox.showinfo("成功", f"已切换到{'全仓' if new_mode == 'CROSSED' else '逐仓'}模式")
                        
                        # 更新配置系统
                        self.symbol_config_manager.update_leverage_margin(symbol, self.current_leverage, new_mode)
                        self.update_margin_leverage_info()  # 更新按钮显示
                    dialog.destroy()
                except Exception as e:
                    messagebox.showerror("错误", str(e))
                    dialog.destroy()
            
            Button(dialog, text="确定", command=apply_change).pack(pady=10)
            
        except Exception as e:
            messagebox.showerror("错误", str(e))

    def change_leverage(self):
        """调整杠杆倍数"""
        try:
            symbol = self.current_valid_symbol
            if not hasattr(self, 'current_leverage'):
                self.update_margin_leverage_info()
            
            # 获取最大杠杆倍数
            config = self.symbol_config_manager.get_symbol_config(symbol)
            max_leverage = config.get('maxLeverage')
            
            # 创建对话框
            dialog = Toplevel(self)
            dialog.title("调整杠杆倍数")
            dialog.geometry("300x200")
            dialog.transient(self)  # 设置为主窗口的临时窗口
            dialog.grab_set()  # 模态对话框
            
            # 使用Frame包装内容，设置统一背景色
            content_frame = Frame(dialog, bg='white')
            content_frame.pack(fill='both', expand=True, padx=20, pady=10)
            
            Label(content_frame, text=f"当前杠杆: {self.current_leverage}x", bg='white').pack(pady=5)
            Label(content_frame, text=f"最大杠杆: {max_leverage}x", bg='white').pack(pady=5)
            
            leverage_var = IntVar(value=self.current_leverage)
            scale = ttk.Scale(content_frame, from_=1, to=max_leverage, 
                            variable=leverage_var, orient='horizontal')
            scale.set(self.current_leverage)  # 设置当前值
            scale.pack(fill='x', pady=10)
            
            leverage_label = Label(content_frame, text=f"选择杠杆: {self.current_leverage}x", bg='white')
            leverage_label.pack(pady=5)
            
            def update_label(*args):
                leverage_label.config(text=f"选择杠杆: {leverage_var.get()}x")
            
            leverage_var.trace('w', update_label)
            
            def apply_leverage():
                try:
                    new_leverage = leverage_var.get()
                    if new_leverage != self.current_leverage:  # 只在发生改变时发送请求
                        self.client.change_leverage(
                            symbol=symbol, leverage=new_leverage)
                        messagebox.showinfo("成功", f"杠杆已调整为{new_leverage}x")
                        
                        # 更新配置系统
                        self.symbol_config_manager.update_leverage_margin(symbol, new_leverage, self.current_margin_type)
                        self.update_margin_leverage_info()  # 更新按钮显示
                    dialog.destroy()
                except Exception as e:
                    messagebox.showerror("错误", str(e))
                    dialog.destroy()
            
            Button(dialog, text="确定", command=apply_leverage).pack(pady=10)
            
        except Exception as e:
            messagebox.showerror("错误", str(e))

    def on_unit_change(self):
        """数量单位切换时自动转换数值"""
        try:
            if not self.amount_var.get():
                return
                
            amount = float(self.amount_var.get())
            symbol = self.current_valid_symbol
            
            if self.order_type_var.get() == "LIMIT":
                try:
                    price = float(self.price_var.get())
                except ValueError:
                    return
            else:
                # 使用K线数据获取最新价格
                if hasattr(self, 'klines_df') and self.klines_df is not None and not self.klines_df.empty:
                    price = float(self.klines_df.iloc[-1]['Close'])
                else:
                    ticker = self.client.ticker_price(self.current_valid_symbol)
                    price = float(ticker['price'])
            
            # 转换数量
            if self.amount_unit_var.get() == "USDT":
                # 从币种转换为USDT
                new_amount = amount * price
            else:
                # 从USDT转换为币种
                new_amount = amount / price
            
            # 获取精度
            if self.amount_unit_var.get() == "USDT":
                precision = self.symbol_config_manager.get_price_precision(symbol)
            else:
                precision = self.symbol_config_manager.get_quantity_precision(symbol)
            
            # 格式化并更新数量
            self.amount_var.set(f"{new_amount:.{precision}f}")
            
            # 重新应用输入验证
            vcmd_amount = (self.register(self.validate_amount_input), '%P')
            self.amount_entry.config(validate="key", validatecommand=vcmd_amount)
            
        except Exception as e:
            logging.error(f"单位转换失败: {str(e)}")

    def on_indicator_change(self, *args):
        """指标变化时的处理，负责更新图表上的技术指标显示"""
        try:
            # 获取当前选择的指标
            indicator = self.indicator_var.get()
            
            # 移除旧的指标线
            if hasattr(self, 'indicator_lines') and self.indicator_lines:
                for line in self.indicator_lines:
                    try:
                        line.remove()
                    except:
                        pass
                self.indicator_lines = []
            
            # 初始化指标线列表
            self.indicator_lines = []

            # 获取绘图数据
            df_plot = self.klines_df.copy()
            df_plot.set_index('Open time', inplace=True)

            # 计算MA指标
            df_plot['MA5'] = df_plot['Close'].rolling(window=5).mean()
            df_plot['MA10'] = df_plot['Close'].rolling(window=10).mean()
            df_plot['MA20'] = df_plot['Close'].rolling(window=20).mean()
                
            # 计算BOLL指标
            df_plot['BOLL_MID'] = df_plot['Close'].rolling(window=20).mean()
            df_plot['BOLL_STD'] = df_plot['Close'].rolling(window=20).std()
            df_plot['BOLL_UPPER'] = df_plot['BOLL_MID'] + 2 * df_plot['BOLL_STD']
            df_plot['BOLL_LOWER'] = df_plot['BOLL_MID'] - 2 * df_plot['BOLL_STD']
            
            # 创建x值数组，应与K线图的x轴对应
            x_values = np.arange(len(df_plot))
            
            # 根据选择的指标绘制相应的线条
            if indicator == "MA":
                # 绘制均线，使用x_values确保与K线对齐
                line1 = self.ax_main.plot(x_values, df_plot['MA5'].values, 'b-', label='MA5', linewidth=1.0, alpha=0.8)[0]
                line2 = self.ax_main.plot(x_values, df_plot['MA10'].values, 'y-', label='MA10', linewidth=1.0, alpha=0.8)[0]
                line3 = self.ax_main.plot(x_values, df_plot['MA20'].values, 'm-', label='MA20', linewidth=1.0, alpha=0.8)[0]
                self.indicator_lines.extend([line1, line2, line3])
  
            elif indicator == "BOLL":
                # 绘制BOLL线，使用x_values确保与K线对齐
                line1 = self.ax_main.plot(x_values, df_plot['BOLL_UPPER'].values, 'r-', label='BOLL上轨', linewidth=1.0, alpha=0.8)[0]
                line2 = self.ax_main.plot(x_values, df_plot['BOLL_MID'].values, 'b-', label='BOLL中轨', linewidth=1.0, alpha=0.8)[0]
                line3 = self.ax_main.plot(x_values, df_plot['BOLL_LOWER'].values, 'g-', label='BOLL下轨', linewidth=1.0, alpha=0.8)[0]
                self.indicator_lines.extend([line1, line2, line3])
            
        except Exception as e:
            logging.error(f"更新指标显示失败: {str(e)}")
            import traceback
            traceback.print_exc()  # 打印详细错误信息

    def on_interval_change(self, *args):
        """切换K线周期"""
        self.load_kline_data()
    
    def reset_view(self):
        """重置视图到初始状态"""
        if hasattr(self, 'ax_main') and hasattr(self, 'canvas'):
            if hasattr(self, 'new_xlim') and hasattr(self, 'new_ylim'):
                self.ax_main.set_xlim(self.new_xlim)
                self.ax_main.set_ylim(self.new_ylim)
                self.canvas.draw_idle()
                if hasattr(self, 'toolbar') and hasattr(self.toolbar, 'status_bar'):
                    self.toolbar.status_bar.config(text="视图已重置")
                self.update_range_labels()
            else:
                # fallback: autoscale
                self.ax_main.autoscale(True)
                self.ax_main.relim()
                self.canvas.draw_idle()

    def display_symbol_config(self, symbol=None):
        """显示当前交易对的配置信息"""
        try:
            if symbol is None:
                symbol = self.current_valid_symbol
            
            # 获取当前交易对的配置
            config = self.symbol_config_manager.get_symbol_config(symbol)
            
            # 创建对话框
            dialog = Toplevel(self)
            dialog.title(f"{symbol} 交易配置")
            dialog.geometry("400x400")
            dialog.transient(self)  # 设置为主窗口的临时窗口
            dialog.grab_set()  # 模态对话框

            # 将对话框定位到鼠标位置
            x = self.winfo_pointerx()
            y = self.winfo_pointery()
            dialog.geometry(f"400x400+{x}+{y}")
            
            # 使用Frame包装内容，设置统一背景色
            main_frame = Frame(dialog, bg='#f0f0f0')
            main_frame.pack(fill='both', expand=True, padx=20, pady=10)

            # 创建内容区域（用于切换显示）
            content_frame = Frame(main_frame, bg='white', height=20)
            content_frame.pack(fill='both', expand=True, pady=(0, 0))
            content_frame.pack_propagate(False)  # 防止内容撑大框架

            # 创建按钮区域（固定在底部）
            button_frame = Frame(main_frame, bg='#f0f0f0')
            button_frame.pack(fill='x', pady=10)
            
            # 显示配置信息
            Label(content_frame, text=f"交易对: {symbol}", font=("Microsoft YaHei", 12, "bold"), bg='white').pack(pady=5, anchor='w')
            Label(content_frame, text=f"价格步长: {config.get('tickSize', 'N/A')}", bg='white').pack(pady=3, anchor='w')
            Label(content_frame, text=f"数量步长: {config.get('stepSize', 'N/A')}", bg='white').pack(pady=3, anchor='w')
            Label(content_frame, text=f"最小下单数量: {config.get('minQty', 'N/A')}", bg='white').pack(pady=3, anchor='w')
            Label(content_frame, text=f"最小名义价值: {config.get('minNotional', 'N/A')}", bg='white').pack(pady=3, anchor='w')
            Label(content_frame, text=f"当前杠杆: {config.get('leverage', 'N/A')}x", bg='white').pack(pady=3, anchor='w')
            Label(content_frame, text=f"最大杠杆: {config.get('maxLeverage', 'N/A')}x", bg='white').pack(pady=3, anchor='w')

            margin_type = config.get('marginType', 'N/A')
            margin_text = "全仓" if margin_type == "CROSSED" else "逐仓" if margin_type == "ISOLATED" else margin_type
            Label(content_frame, text=f"保证金模式: {margin_text}", bg='white').pack(pady=3, anchor='w')

            position_side = config.get('positionSide', 'N/A')
            position_text = "双向持仓" if position_side == "BOTH" else "单向持仓" if position_side in ["LONG", "SHORT"] else position_side
            Label(content_frame, text=f"仓位模式: {position_text}", bg='white').pack(pady=3, anchor='w')

            last_updated = config.get('lastUpdated', 'N/A')
            Label(content_frame, text=f"最后更新时间: {last_updated}", bg='white').pack(pady=5, anchor='w')

            # 更新按钮
            def update_config():
                try:
                    success, error_message = self.symbol_config_manager.update_symbol_precision(symbol, client=self.client)
                    if success:
                        messagebox.showinfo("成功", "配置更新成功！")
                        # 刷新配置信息显示，而不是关闭重开窗口
                        refresh_config_display()
                    else:
                        messagebox.showerror("错误", error_message)
                except Exception as e:
                    messagebox.showerror("错误", f"配置更新失败: {str(e)}")

            # 刷新配置显示的函数
            def refresh_config_display():
                if is_detail_view:
                    # 如果当前是详细视图，刷新详细信息
                    if hasattr(dialog, 'detail_text'):
                        updated_config = self.symbol_config_manager.get_symbol_config(symbol)
                        dialog.detail_text.config(state='normal')
                        dialog.detail_text.delete('1.0', 'end')
                        dialog.detail_text.insert('end', f"=== {symbol} 详细配置信息 ===\n\n")
                        for key, value in updated_config.items():
                            dialog.detail_text.insert('end', f"{key}: {value}\n")
                        dialog.detail_text.config(state='disabled')
                else:
                    # 如果当前是简略视图，刷新简略信息
                    updated_config = self.symbol_config_manager.get_symbol_config(symbol)

                    # 更新显示的标签
                    for widget in content_frame.winfo_children():
                        if isinstance(widget, Label):
                            text = widget.cget('text')
                            if text.startswith('价格步长:'):
                                widget.config(text=f"价格步长: {updated_config.get('tickSize', 'N/A')}")
                            elif text.startswith('数量步长:'):
                                widget.config(text=f"数量步长: {updated_config.get('stepSize', 'N/A')}")
                            elif text.startswith('最小下单数量:'):
                                widget.config(text=f"最小下单数量: {updated_config.get('minQty', 'N/A')}")
                            elif text.startswith('最小名义价值:'):
                                widget.config(text=f"最小名义价值: {updated_config.get('minNotional', 'N/A')}")
                            elif text.startswith('当前杠杆:'):
                                widget.config(text=f"当前杠杆: {updated_config.get('leverage', 'N/A')}x")
                            elif text.startswith('最大杠杆:'):
                                widget.config(text=f"最大杠杆: {updated_config.get('maxLeverage', 'N/A')}x")
                            elif text.startswith('保证金模式:'):
                                margin_type = updated_config.get('marginType', 'N/A')
                                margin_text = "全仓" if margin_type == "CROSSED" else "逐仓" if margin_type == "ISOLATED" else margin_type
                                widget.config(text=f"保证金模式: {margin_text}")
                            elif text.startswith('仓位模式:'):
                                position_side = updated_config.get('positionSide', 'N/A')
                                position_text = "双向持仓" if position_side == "BOTH" else "单向持仓" if position_side in ["LONG", "SHORT"] else position_side
                                widget.config(text=f"仓位模式: {position_text}")
                            elif text.startswith('最后更新时间:'):
                                widget.config(text=f"最后更新时间: {updated_config.get('lastUpdated', 'N/A')}")

            Button(button_frame, text="更新", command=update_config, bg='#4CAF50', fg='white').pack(side='left', padx=5)

            # 详细信息切换功能
            is_detail_view = False

            def toggle_details():
                nonlocal is_detail_view
                if not is_detail_view:
                    # 切换到详细视图
                    show_detail_view()
                    is_detail_view = True
                    detail_button.config(text="简略信息")
                else:
                    # 切换回简略视图
                    show_simple_view()
                    is_detail_view = False
                    detail_button.config(text="详细信息")

            def show_detail_view():
                # 清除当前内容
                for widget in content_frame.winfo_children():
                    widget.destroy()

                # 创建可滚动的文本框显示详细信息
                scrollbar = Scrollbar(content_frame)
                scrollbar.pack(side='right', fill='y')

                detail_text = Text(content_frame, wrap='word', yscrollcommand=scrollbar.set)
                detail_text.pack(fill='both', expand=True)
                scrollbar.config(command=detail_text.yview)

                # 显示所有配置信息
                current_config = self.symbol_config_manager.get_symbol_config(symbol)
                detail_text.insert('end', f"=== {symbol} 详细配置信息 ===\n\n")
                for key, value in current_config.items():
                    detail_text.insert('end', f"{key}: {value}\n")

                detail_text.config(state='disabled')  # 设置为只读

                # 保存引用以便刷新时更新
                dialog.detail_text = detail_text

            def show_simple_view():
                # 清除当前内容
                for widget in content_frame.winfo_children():
                    widget.destroy()

                # 重新创建简略视图
                current_config = self.symbol_config_manager.get_symbol_config(symbol)

                Label(content_frame, text=f"交易对: {symbol}", font=("Microsoft YaHei", 12, "bold"), bg='white').pack(pady=5, anchor='w')
                Label(content_frame, text=f"价格步长: {current_config.get('tickSize', 'N/A')}", bg='white').pack(pady=3, anchor='w')
                Label(content_frame, text=f"数量步长: {current_config.get('stepSize', 'N/A')}", bg='white').pack(pady=3, anchor='w')
                Label(content_frame, text=f"最小下单数量: {current_config.get('minQty', 'N/A')}", bg='white').pack(pady=3, anchor='w')
                Label(content_frame, text=f"最小名义价值: {current_config.get('minNotional', 'N/A')}", bg='white').pack(pady=3, anchor='w')
                Label(content_frame, text=f"当前杠杆: {current_config.get('leverage', 'N/A')}x", bg='white').pack(pady=3, anchor='w')
                Label(content_frame, text=f"最大杠杆: {current_config.get('maxLeverage', 'N/A')}x", bg='white').pack(pady=3, anchor='w')

                margin_type = current_config.get('marginType', 'N/A')
                margin_text = "全仓" if margin_type == "CROSSED" else "逐仓" if margin_type == "ISOLATED" else margin_type
                Label(content_frame, text=f"保证金模式: {margin_text}", bg='white').pack(pady=3, anchor='w')

                position_side = current_config.get('positionSide', 'N/A')
                position_text = "双向持仓" if position_side == "BOTH" else "单向持仓" if position_side in ["LONG", "SHORT"] else position_side
                Label(content_frame, text=f"仓位模式: {position_text}", bg='white').pack(pady=3, anchor='w')

                last_updated = current_config.get('lastUpdated', 'N/A')
                Label(content_frame, text=f"最后更新时间: {last_updated}", bg='white').pack(pady=5, anchor='w')

                # 清除detail_text引用
                if hasattr(dialog, 'detail_text'):
                    delattr(dialog, 'detail_text')

            detail_button = Button(button_frame, text="详细信息", command=toggle_details, bg='#2196F3', fg='white')
            detail_button.pack(side='left', padx=5)

            # 关闭按钮
            Button(button_frame, text="关闭", command=dialog.destroy).pack(side='right', padx=5)
            
        except Exception as e:
            logging.error(f"显示交易对配置信息失败: {str(e)}")
            messagebox.showerror("错误", str(e))



    def validate_price_input(self, new_value):
        """验证价格输入是否符合精度要求"""
        # 允许空值和小数点
        if not new_value or new_value == '.':
            return True
        
        # 检查是否是有效数字
        try:
            # 如果包含多个小数点，不允许
            if new_value.count('.') > 1:
                return False
                
            # 如果是纯数字或者有效的小数，继续检查精度
            if '.' in new_value:
                integer_part, decimal_part = new_value.split('.')
                
                # 获取当前交易对的价格精度
                symbol = self.current_valid_symbol
                price_precision = self.symbol_config_manager.get_price_precision(symbol)
                
                # 检查小数部分长度是否超过精度
                if len(decimal_part) > price_precision:
                    return False
            
            # 尝试转换为浮点数
            float(new_value)
            return True
        except ValueError:
            return False
    
    def validate_amount_input(self, new_value):
        """验证数量输入是否符合精度要求"""
        # 允许空值和小数点
        if not new_value or new_value == '.':
            return True
        
        # 检查是否是有效数字
        try:
            # 如果包含多个小数点，不允许
            if new_value.count('.') > 1:
                return False
                
            # 如果是纯数字或者有效的小数，继续检查精度
            if '.' in new_value:
                integer_part, decimal_part = new_value.split('.')
                
                # 获取当前交易对的数量精度
                symbol = self.current_valid_symbol

                # 根据当前单位选择使用的精度
                if self.amount_unit_var.get() == "USDT":
                    # 如果是USDT单位，使用价格精度
                    precision = self.symbol_config_manager.get_price_precision(symbol)
                else:
                    # 如果是币种单位，使用数量精度
                    precision = self.symbol_config_manager.get_quantity_precision(symbol)
                
                # 检查小数部分长度是否超过精度
                if len(decimal_part) > precision:
                    return False
            
            # 尝试转换为浮点数
            float(new_value)
            return True
        except ValueError:
            return False

    def _setup_timeout_adapter(self, timeout=3.0):
        """设置HTTP请求超时适配器"""
        if not hasattr(self.client, 'session'):
            return False

        import requests
        # 创建一个临时的适配器来设置超时
        class TimeoutHTTPAdapter(requests.adapters.HTTPAdapter):
            def __init__(self, timeout=3.0, *args, **kwargs):
                self.timeout = timeout
                super().__init__(*args, **kwargs)

            def send(self, request, **kwargs):
                kwargs['timeout'] = self.timeout
                return super().send(request, **kwargs)

        # 保存原始适配器
        self._original_adapters = {}
        for prefix in ['http://', 'https://']:
            if prefix in self.client.session.adapters:
                self._original_adapters[prefix] = self.client.session.adapters[prefix]

        # 安装超时适配器
        self._timeout_adapter = TimeoutHTTPAdapter(timeout=timeout)
        self.client.session.mount('http://', self._timeout_adapter)
        self.client.session.mount('https://', self._timeout_adapter)
        return True

    def _restore_adapters(self):
        """恢复原始HTTP适配器"""
        if not hasattr(self.client, 'session') or not self._original_adapters:
            return

        # 恢复原始适配器
        for prefix, adapter in self._original_adapters.items():
            self.client.session.mount(prefix, adapter)
        self._original_adapters = {}

    def _api_call_with_timeout(self, api_func, *args, timeout=3.0, **kwargs):
        """执行API调用，带超时控制

        参数:
            api_func: 要调用的API函数
            *args: 传递给API函数的位置参数
            timeout: 超时时间（秒）
            **kwargs: 传递给API函数的关键字参数

        返回:
            (result, success): API调用结果和是否成功的标志
        """
        result = None
        success = False

        try:
            # 设置超时适配器
            adapter_set = self._setup_timeout_adapter(timeout)

            try:
                # 调用API函数
                result = api_func(*args, **kwargs)
                success = True
            finally:
                # 恢复原始适配器
                if adapter_set:
                    self._restore_adapters()

        except Exception as e:
            logging.error(f"API调用失败: {str(e)}")
            result = None
            success = False

        return result, success

    def sync_time_with_ntplib(self):
        """使用ntplib同步系统时间"""
        if sync_time_with_ntplib(self.client):
            messagebox.showinfo("同步成功", "使用ntplib成功同步系统时间和交易所时间")
            # 立即更新时间显示
            time_label = None
            if hasattr(self, 'toolbar') and hasattr(self.toolbar, 'time_label'):
                time_label = self.toolbar.time_label
                if time_label and time_label.winfo_exists():
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    time_label.config(text=f" {current_time}")
        else:
            messagebox.showerror("同步失败", "同步系统时间失败，请检查网络连接或尝试手动同步")



    def destroy(self):
        """销毁页面，释放资源"""       
        # 移除倒计时标签
        if hasattr(self, 'countdown_label') and self.countdown_label:
            try:
                if self.countdown_label in time_manager.countdown_labels:
                    time_manager.remove_countdown_label()
            except Exception as e:
                logging.error(f"移除倒计时标签失败: {str(e)}")
        
        # 调用父类的destroy方法
        super().destroy()

    def start_price_and_countdown_refresh(self):
        # 启动价格线和倒计时刷新

        # 停止之前的价格刷新任务（如果有）
        if hasattr(self, 'price_refresh_job'):
            self.after_cancel(self.price_refresh_job)
        self.price_refresh_job = self.after(200, self.auto_refresh_price_line)

        # 重新添加倒计时标签
        if hasattr(self, 'countdown_label') and self.countdown_label:
            interval = self.interval_var.get()
            time_manager.add_countdown_label(self.countdown_label, interval)

    def stop_price_and_countdown_refresh(self):
        # 停止价格线刷新
        if hasattr(self, 'price_refresh_job'):
            self.after_cancel(self.price_refresh_job)
            del self.price_refresh_job
        # 移除倒计时标签
        time_manager.remove_countdown_label()


class KlineDataManagerPage(Frame):
    """K线数据管理界面"""
    def __init__(self, master, switch_page_callback, client):
        super().__init__(master)
        self.switch_page_callback = switch_page_callback
        self.client = client
        self.symbols = []  # 存储所有交易对
        self.symbol_var = StringVar()
        self.interval_var = StringVar(value="1m")  # 设置默认值
        self.data_checked = False  # 新增：完整性检查标志
        self.last_checked_range = None  # 新增：记录上次检查的对齐时间范围
        
        # 先加载交易对
        self.load_trading_symbols()
        if not self.symbols:  # 如果加载失败，使用默认值
            self.symbols = ['BTCUSDT']
            self.symbol_var.set('BTCUSDT')
        else:
            self.symbol_var.set(self.symbols[0])  # 设置默认交易对
            
        self.create_widgets()

    def create_widgets(self):
        # 创建顶部控制区域
        control_frame = Frame(self)
        control_frame.pack(fill='x', padx=5, pady=5)

        # 返回按钮
        back_btn = Button(control_frame, text="返回主页", command=lambda: self.switch_page_callback("main"))
        back_btn.pack(side='left', padx=5)

        # 标的选择区域
        symbol_frame = Frame(control_frame)
        symbol_frame.pack(side='left', padx=5)
        
        Label(symbol_frame, text="交易对:").pack(side='left')
        self.symbol_combobox = ttk.Combobox(symbol_frame, textvariable=self.symbol_var, values=self.symbols, width=15)
        self.symbol_combobox.pack(side='left', padx=5)
        
        # 设置初始值
        if self.symbols:
            self.symbol_combobox.set(self.symbols[0])
        
        # 绑定交易对选择框事件
        self.symbol_var.trace('w', self.on_symbol_var_change)  # 监听变量变化
        self.symbol_combobox.bind('<<ComboboxSelected>>', self.on_symbol_select)
        self.symbol_combobox.bind('<FocusIn>', self.on_symbol_focus)
        self.symbol_combobox.bind('<Return>', self.on_symbol_return)

        # 时间周期选择区域
        interval_frame = Frame(control_frame)
        interval_frame.pack(side='left', padx=5)
        
        Label(interval_frame, text="周期:").pack(side='left')
        intervals = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w']
        self.interval_combobox = ttk.Combobox(interval_frame, textvariable=self.interval_var, values=intervals, width=10)
        self.interval_combobox.pack(side='left', padx=5)
        self.interval_combobox.set('')  # 初始为空
        self.interval_combobox.bind('<<ComboboxSelected>>', self.on_interval_change)

        # ====== 新增：自定义时间范围输入（分段） ======
        custom_time_frame = Frame(control_frame)
        custom_time_frame.pack(side='left', padx=5)
        Label(custom_time_frame, text="自定义时间:").pack(side='left')
        # 注册验证函数
        vcmd_year = (self.register(lambda char: validate_time_input(char, 4)), '%P')
        vcmd_other = (self.register(lambda char: validate_time_input(char, 2)), '%P')
        # 起始时间
        self.start_year = StringVar(); self.start_month = StringVar(); self.start_day = StringVar()
        self.start_hour = StringVar(); self.start_minute = StringVar()
        Entry(custom_time_frame, textvariable=self.start_year, width=4, validate='key', validatecommand=vcmd_year).pack(side='left'); Label(custom_time_frame, text="/").pack(side='left')
        Entry(custom_time_frame, textvariable=self.start_month, width=2, validate='key', validatecommand=vcmd_other).pack(side='left'); Label(custom_time_frame, text="/").pack(side='left')
        Entry(custom_time_frame, textvariable=self.start_day, width=2, validate='key', validatecommand=vcmd_other).pack(side='left'); Label(custom_time_frame, text=" ").pack(side='left')
        Entry(custom_time_frame, textvariable=self.start_hour, width=2, validate='key', validatecommand=vcmd_other).pack(side='left'); Label(custom_time_frame, text=":").pack(side='left')
        Entry(custom_time_frame, textvariable=self.start_minute, width=2, validate='key', validatecommand=vcmd_other).pack(side='left')
        Label(custom_time_frame, text="-").pack(side='left')
        # 结束时间
        self.end_year = StringVar(); self.end_month = StringVar(); self.end_day = StringVar()
        self.end_hour = StringVar(); self.end_minute = StringVar()
        Entry(custom_time_frame, textvariable=self.end_year, width=4, validate='key', validatecommand=vcmd_year).pack(side='left'); Label(custom_time_frame, text="/").pack(side='left')
        Entry(custom_time_frame, textvariable=self.end_month, width=2, validate='key', validatecommand=vcmd_other).pack(side='left'); Label(custom_time_frame, text="/").pack(side='left')
        Entry(custom_time_frame, textvariable=self.end_day, width=2, validate='key', validatecommand=vcmd_other).pack(side='left'); Label(custom_time_frame, text=" ").pack(side='left')
        Entry(custom_time_frame, textvariable=self.end_hour, width=2, validate='key', validatecommand=vcmd_other).pack(side='left'); Label(custom_time_frame, text=":").pack(side='left')
        Entry(custom_time_frame, textvariable=self.end_minute, width=2, validate='key', validatecommand=vcmd_other).pack(side='left')
        # 绑定回车和失焦事件，自动刷新数据范围显示
        for var in [self.start_year, self.start_month, self.start_day, self.start_hour, self.start_minute,
                    self.end_year, self.end_month, self.end_day, self.end_hour, self.end_minute]:
            # 只在失焦时才更新数据范围
            pass  # 不再trace写入，改为绑定失焦事件
        # 绑定失焦事件
        for entry in custom_time_frame.winfo_children():
            if isinstance(entry, Entry):
                entry.bind('<FocusOut>', lambda event: self._on_timebox_change())
        # ====== 新增结束 ======

        # 创建数据信息显示区域
        info_frame = Frame(self)
        info_frame.pack(fill='x', padx=5, pady=0)
        
        self.data_info_label = Label(info_frame, text="请选择交易对和时间周期查看数据信息", height=2, anchor='center', justify='center')
        self.data_info_label.pack(pady=0, fill='x')

        # 创建按钮区域
        button_frame = Frame(self)
        button_frame.pack(fill='x', padx=5, pady=5)
        
        self.check_btn = Button(button_frame, text="检查数据完整性", command=self.check_data_integrity)
        self.check_btn.pack(side='left', padx=5)
        
        self.repair_btn = Button(button_frame, text="修复数据", command=self.repair_data, state=DISABLED)
        self.repair_btn.pack(side='left', padx=5)

        # 新增停止修复按钮
        self.stop_repair_btn = Button(button_frame, text="停止修复", command=self.stop_repair, state=DISABLED)
        self.stop_repair_btn.pack(side='left', padx=5)

        # 新增：测试最大涨跌幅按钮
        self.test_btn = Button(button_frame, text="测试最大涨跌幅", command=self.open_test_dialog, state=DISABLED)
        self.test_btn.pack(side='left', padx=5)
        # 新增停止测试按钮
        self.stop_test_btn = Button(button_frame, text="停止测试", command=self.stop_test, state=DISABLED)
        self.stop_test_btn.pack(side='left', padx=5)

        # 进入界面后自动显示当前默认交易对和周期的数据及时间范围
        # self.update_data_info(auto_fill=True)

        # ====== 新增：可折叠K线图表区域 ======
        self.chart_area_frame = Frame(self)
        self.chart_area_frame.pack(fill='x', padx=0, pady=0)
        self.toggle_chart_btn = Button(self.chart_area_frame, text="展开K线图表", command=self.toggle_chart_panel)
        self.toggle_chart_btn.pack(fill='x', padx=5, pady=2)
        self.chart_collapsed = True
        self._last_sash_pos = 450  # 记忆分割条高度
        # 分割条区域
        self.paned = PanedWindow(self, orient='vertical', sashrelief='raised', sashwidth=6)
        # self.paned.pack(fill=BOTH, expand=True, padx=0, pady=0)
        # 图表面板
        self.chart_panel = Frame(self.paned)
        # 图表顶部栏（仅保留必要控件）
        chart_top_frame = Frame(self.chart_panel)
        chart_top_frame.pack(fill='x', pady=2)
        # 指标选择
        self.chart_indicator_var = StringVar(value="MA")
        chart_indicator_menu = ttk.Combobox(chart_top_frame, textvariable=self.chart_indicator_var, values=["None", "MA", "BOLL"], width=6)
        chart_indicator_menu.pack(side='left', padx=5)
        # 绑定指标选择变化事件
        self.chart_indicator_var.trace('w', lambda *args: self.draw_chart_indicators() if hasattr(self, 'ax_main') and hasattr(self, 'klines_df') and self.ax_main is not None and self.klines_df is not None else None)
        # 交易对选择
        self.chart_symbol_var = StringVar(value=self.symbols[0] if self.symbols else "BTCUSDT")
        chart_symbol_menu = ttk.Combobox(chart_top_frame, textvariable=self.chart_symbol_var, values=self.symbols, width=15)
        chart_symbol_menu.pack(side='left', padx=5)
        # 周期选择
        self.chart_interval_var = StringVar(value="1m")
        chart_interval_menu = ttk.Combobox(chart_top_frame, textvariable=self.chart_interval_var, values=['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w'], width=10)
        chart_interval_menu.pack(side='left', padx=5)
        # 自定义时间区间（独立变量）
        chart_time_frame = Frame(chart_top_frame)
        chart_time_frame.pack(side='left', padx=10)
        Label(chart_time_frame, text="自定义时间:").pack(side='left')
        # 注册验证函数
        chart_vcmd_year = (self.register(lambda char: validate_time_input(char, 4)), '%P')
        chart_vcmd_other = (self.register(lambda char: validate_time_input(char, 2)), '%P')
        self.chart_start_year = StringVar(); self.chart_start_month = StringVar(); self.chart_start_day = StringVar()
        self.chart_start_hour = StringVar(); self.chart_start_minute = StringVar()
        Entry(chart_time_frame, textvariable=self.chart_start_year, width=4, validate='key', validatecommand=chart_vcmd_year).pack(side='left'); Label(chart_time_frame, text="/").pack(side='left')
        Entry(chart_time_frame, textvariable=self.chart_start_month, width=2, validate='key', validatecommand=chart_vcmd_other).pack(side='left'); Label(chart_time_frame, text="/").pack(side='left')
        Entry(chart_time_frame, textvariable=self.chart_start_day, width=2, validate='key', validatecommand=chart_vcmd_other).pack(side='left'); Label(chart_time_frame, text=" ").pack(side='left')
        Entry(chart_time_frame, textvariable=self.chart_start_hour, width=2, validate='key', validatecommand=chart_vcmd_other).pack(side='left'); Label(chart_time_frame, text=":").pack(side='left')
        Entry(chart_time_frame, textvariable=self.chart_start_minute, width=2, validate='key', validatecommand=chart_vcmd_other).pack(side='left')
        Label(chart_time_frame, text="-").pack(side='left')
        self.chart_end_year = StringVar(); self.chart_end_month = StringVar(); self.chart_end_day = StringVar()
        self.chart_end_hour = StringVar(); self.chart_end_minute = StringVar()
        Entry(chart_time_frame, textvariable=self.chart_end_year, width=4, validate='key', validatecommand=chart_vcmd_year).pack(side='left'); Label(chart_time_frame, text="/").pack(side='left')
        Entry(chart_time_frame, textvariable=self.chart_end_month, width=2, validate='key', validatecommand=chart_vcmd_other).pack(side='left'); Label(chart_time_frame, text="/").pack(side='left')
        Entry(chart_time_frame, textvariable=self.chart_end_day, width=2, validate='key', validatecommand=chart_vcmd_other).pack(side='left'); Label(chart_time_frame, text=" ").pack(side='left')
        Entry(chart_time_frame, textvariable=self.chart_end_hour, width=2, validate='key', validatecommand=chart_vcmd_other).pack(side='left'); Label(chart_time_frame, text=":").pack(side='left')
        Entry(chart_time_frame, textvariable=self.chart_end_minute, width=2, validate='key', validatecommand=chart_vcmd_other).pack(side='left')
        # 生成图表按钮
        self.chart_generate_btn = Button(chart_top_frame, text="生成图表", command=self.generate_chart)
        self.chart_generate_btn.pack(side='left', padx=10)

        # 图表区
        self.chart_canvas_frame = Frame(self.chart_panel)
        self.chart_canvas_frame.pack(fill=BOTH, expand=True)
        # ====== 新增结束 ======

        # 创建日志显示区域（始终在最底部）
        self.log_frame = Frame(self)
        self.log_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)

        self.log_text = Text(self.log_frame, height=10, wrap='word')
        scrollbar = Scrollbar(self.log_frame, command=self.log_text.yview)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.log_text.pack(side=LEFT, fill=BOTH, expand=True)
        self.log_text.config(yscrollcommand=scrollbar.set)
        self.log_text.config(state=DISABLED)

        # 进入界面后自动创建图表（只调用一次create_chart）
        self.create_chart()

        # 进入界面后自动折叠图表
        # self.toggle_chart_panel()

    def load_trading_symbols(self):
        """从全局交易对管理器加载所有交易对（包括已下架的）"""
        try:
            global trading_symbols_manager
            if 'trading_symbols_manager' in globals():
                # 获取所有交易对（包括已下架的）
                self.symbols = trading_symbols_manager.get_symbols(tradable_only=False)
                logging.info(f"K线管理页面加载了 {len(self.symbols)} 个交易对（包括已下架）")

                # 设置默认选中的交易对
                if self.symbols:
                    self.symbol_var.set(self.symbols[0])
            else:
                logging.warning("全局交易对管理器未初始化，使用默认交易对")
                self.symbols = ['BTCUSDT']
                self.symbol_var.set('BTCUSDT')

        except Exception as e:
            logging.error(f"K线管理页面加载交易对失败: {str(e)}")
            self.symbols = ['BTCUSDT']  # 加载失败时使用默认值
            self.symbol_var.set('BTCUSDT')

    def on_symbol_select(self, event=None):
        """当用户从下拉列表中选择一个交易对时"""
        self.on_symbol_change()
        self.symbol_combobox.selection_clear()  # 清除选择，避免重复触发
        
    def on_symbol_focus(self, event=None):
        """当交易对选择框获得焦点时"""
        # 直接使用已经排序好的交易对列表（包含交割合约）
        self.symbol_combobox['values'] = self.symbols

        # 展开下拉列表
        if not self.symbol_combobox.winfo_ismapped():
            self.symbol_combobox.event_generate('<Down>')  # 展开下拉列表
            
    def on_symbol_var_change(self, *args):
        """当输入内容变化时触发"""
        current_text = self.symbol_var.get().upper()

        # 如果输入框为空，显示所有交易对（保持原有排序）
        if not current_text:
            self.symbol_combobox['values'] = self.symbols
        else:
            # 有输入内容时，只显示匹配的交易对（不区分大小写），保持原有排序
            filtered_symbols = [s for s in self.symbols if current_text in s.upper()]
            self.symbol_combobox['values'] = filtered_symbols
        
        # 如果有匹配结果且列表未展开，则展开列表
        if self.symbol_combobox['values'] and not self.symbol_combobox.winfo_ismapped():
            self.symbol_combobox.event_generate('<Down>')
        
        # 保持输入框焦点，光标在末尾
        self.after(10, lambda: self.symbol_combobox.icursor('end'))
        self.after(10, lambda: self.symbol_combobox.focus_set())

    def on_symbol_return(self, event=None):
        """当用户按下回车键时"""
        if self.symbol_combobox['values']:
            # 如果没有选中项但有匹配结果，选择第一个
            if not self.symbol_combobox.get() in self.symbol_combobox['values']:
                self.symbol_var.set(self.symbol_combobox['values'][0])
            self.on_symbol_change()
        return 'break'

    def on_symbol_change(self, event=None):
        """当选择的交易对改变时"""
        try:
            symbol = self.symbol_var.get().strip().upper()

            # 检查是否是有效的交易对（包括交割合约）
            if symbol not in self.symbols:
                # 如果不在列表中，尝试添加USDT后缀（兼容旧的输入方式）
                if not symbol.endswith('USDT') and not '_' in symbol:
                    symbol = f"{symbol}USDT"
                    if symbol not in self.symbols:
                        # 如果还是不在列表中，使用默认值
                        symbol = self.symbols[0] if self.symbols else 'BTCUSDT'

            self.symbol_var.set(symbol)  # 更新为标准格式
            self.data_checked = False  # 只要切换交易对就重置检查标志
            self.update_data_info(auto_fill=True)
        except Exception as e:
            self.log_message(f"更新交易对信息失败: {str(e)}")

    def on_interval_change(self, event=None):
        """当选择的时间周期改变时"""
        # 只有选中有效周期时才允许检查数据完整性
        if self.interval_var.get() in ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w']:
            self.check_btn.config(state=NORMAL)
        else:
            self.check_btn.config(state=DISABLED)
        self.data_checked = False  # 只要切换周期就重置检查标志
        self.update_data_info(auto_fill=True)

    def get_safe_end(self, interval):
        if interval.endswith('m'):
            minutes = int(interval[:-1])
            now = datetime.now()
            prev_time = now - pd.Timedelta(minutes=minutes)
            total_minutes = prev_time.hour * 60 + prev_time.minute
            safe_minutes = (total_minutes // minutes) * minutes
            last_open = prev_time.replace(hour=safe_minutes // 60, minute=safe_minutes % 60, second=0, microsecond=0)
            safe_end = last_open + pd.Timedelta(minutes=minutes)
            return pd.Timestamp(safe_end)
        elif interval.endswith('h'):
            hours = int(interval[:-1])
            now = datetime.now()
            prev_time = now - pd.Timedelta(hours=hours)
            safe_hour = (prev_time.hour // hours) * hours
            last_open = prev_time.replace(hour=safe_hour, minute=0, second=0, microsecond=0)
            safe_end = last_open + pd.Timedelta(hours=hours)
            return pd.Timestamp(safe_end)
        elif interval == '1d':
            now = datetime.now()
            base = now.replace(hour=8, minute=0, second=0, microsecond=0)
            if now < base:
                last_open = base - pd.Timedelta(days=1)
            else:
                last_open = base
                while last_open + pd.Timedelta(days=1) <= now:
                    last_open += pd.Timedelta(days=1)
            safe_end = last_open
            return pd.Timestamp(safe_end)
        elif interval == '1w':
            now = datetime.now()
            # 币安期货周线对齐到每周一08:00（北京时间）
            # 找到本周一8:00
            weekday = now.weekday()  # 周一为0
            base = now.replace(hour=8, minute=0, second=0, microsecond=0) - pd.Timedelta(days=weekday)
            if now < base:
                last_open = base - pd.Timedelta(weeks=1)
            else:
                last_open = base
                while last_open + pd.Timedelta(weeks=1) <= now:
                    last_open += pd.Timedelta(weeks=1)
            safe_end = last_open
            return pd.Timestamp(safe_end)
        else:
            return pd.Timestamp(datetime.now())

    def update_data_info(self, auto_fill=False):
        if getattr(self, '_auto_filling_timebox', False):
            return
        symbol = self.symbol_var.get()
        interval = self.interval_var.get()
        if not symbol or not interval:
            return
        try:
            df = self.load_klines_from_local(symbol, interval)
            if not df.empty:
                min_time = pd.to_datetime(df['Open time'].min())
                # 计算最后一根K线的收盘时间 = 最后一根K线的Open time + 周期长度
                max_open_time = pd.to_datetime(df['Open time'].max())
                # 解析周期长度
                if interval.endswith('m'):
                    minutes = int(interval[:-1])
                    max_time = max_open_time + pd.Timedelta(minutes=minutes)
                elif interval.endswith('h'):
                    hours = int(interval[:-1])
                    max_time = max_open_time + pd.Timedelta(hours=hours)
                elif interval == '1d':
                    max_time = max_open_time + pd.Timedelta(days=1)
                elif interval == '1w':
                    max_time = max_open_time + pd.Timedelta(weeks=1)
                else:
                    # 默认按1分钟处理
                    max_time = max_open_time + pd.Timedelta(minutes=1)
                if auto_fill:
                    self._auto_filling_timebox = True
                    self.start_year.set(str(min_time.year))
                    self.start_month.set(f"{min_time.month:02d}")
                    self.start_day.set(f"{min_time.day:02d}")
                    self.start_hour.set(f"{min_time.hour:02d}")
                    self.start_minute.set(f"{min_time.minute:02d}")
                    self.end_year.set(str(max_time.year))
                    self.end_month.set(f"{max_time.month:02d}")
                    self.end_day.set(f"{max_time.day:02d}")
                    self.end_hour.set(f"{max_time.hour:02d}")
                    self.end_minute.set(f"{max_time.minute:02d}")
                    self._auto_filling_timebox = False
                self.data_checked = False
                start_dt, end_dt = self.get_custom_time_range()
                aligned_start = self.align_time_to_interval(start_dt, interval, align_forward=True) if start_dt else None
                aligned_end = self.align_time_to_interval(end_dt, interval, align_forward=False) if end_dt else None
                # 应用safe_end
                safe_end = self.get_safe_end(interval)
                if aligned_end and aligned_end > safe_end:
                    aligned_end = safe_end
                range_str = "--"
                if aligned_start and aligned_end:
                    range_str = f"{aligned_start.strftime('%Y/%m/%d %H:%M')} - {aligned_end.strftime('%Y/%m/%d %H:%M')}"
                # 只判断时间范围本身是否有效，不再判断本地数据是否有K线
                invalid_range = False
                if not aligned_start or not aligned_end or aligned_start >= aligned_end:
                    invalid_range = True

                if invalid_range:
                    self.data_info_label.config(
                        text=f"{range_str}\n⚠️ 该时间范围不存在K线（请检查输入的时间和周期）"
                    )
                    self.repair_btn.config(state=DISABLED)
                    self.test_btn.config(state=DISABLED)
                else:
                    self.test_btn.config(state=DISABLED)
                    self.data_info_label.config(
                        text=f"{range_str}\n❌ 未经过完整性检查"
                    )
            else:
                self.data_checked = False
                self.data_info_label.config(text="未找到本地数据", height=2, anchor='center', justify='center')
                self.data_info_label.pack(pady=0, fill='x')
        except Exception as e:
            self.log_message(f"更新数据信息失败: {str(e)}")

    def filter_data_by_input_time(self, df):
        """根据当前输入框内容识别时间范围并筛选本地数据，返回筛选后的DataFrame"""
        start_dt, end_dt = self.get_custom_time_range()
        if not df.empty:
            if start_dt is not None:
                df = df[df['Open time'] >= start_dt]
            if end_dt is not None:
                df = df[df['Open time'] <= end_dt]
        return df

    def detect_missing_ranges(self, df, start_dt, end_dt, interval_minutes):
        """检测缺失区间，返回[(start, end), ...]，含首尾和中间缺口"""
        missing_ranges = []
        if df.empty:
            if start_dt and end_dt:
                missing_ranges.append((start_dt, end_dt))
            return missing_ranges
        df = df.sort_values(by='Open time')
        # 首区间
        if start_dt is not None and df['Open time'].min() > start_dt:
            first_time = df['Open time'].min()
            missing_start = start_dt
            missing_end = first_time
            missing_ranges.append((missing_start, missing_end))
        # 中间缺口
        expected_diff = pd.Timedelta(minutes=interval_minutes)
        time_diffs = df['Open time'].diff()
        for idx in time_diffs[time_diffs > expected_diff].index:
            prev_time = df.loc[idx-1, 'Open time']
            curr_time = df.loc[idx, 'Open time']
            missing_start = prev_time + expected_diff
            missing_end = curr_time
            missing_ranges.append((missing_start, missing_end))
        # 尾区间
        if end_dt is not None and df['Open time'].max() < end_dt:
            last_time = df['Open time'].max()
            missing_start = last_time + pd.Timedelta(minutes=interval_minutes)
            missing_end = end_dt
            if missing_start <= missing_end - pd.Timedelta(minutes=interval_minutes):
                missing_ranges.append((missing_start, missing_end))
        return missing_ranges

    def check_data_integrity(self):
        symbol = self.symbol_var.get()
        interval = self.interval_var.get()
        if not symbol or not interval:
            return
        try:
            df = self.load_klines_from_local(symbol, interval)
            start_dt, end_dt = self.get_custom_time_range()
            aligned_start = self.align_time_to_interval(start_dt, interval, align_forward=True) if start_dt else None
            aligned_end = self.align_time_to_interval(end_dt, interval, align_forward=False) if end_dt else None
            # 应用safe_end
            safe_end = self.get_safe_end(interval)
            if aligned_end and aligned_end > safe_end:
                aligned_end = safe_end
            range_str = "--"
            if aligned_start and aligned_end:
                range_str = f"{aligned_start.strftime('%Y/%m/%d %H:%M')} - {aligned_end.strftime('%Y/%m/%d %H:%M')}"
            # 判断时间范围有效性
            invalid_range = (
                not aligned_start or not aligned_end or aligned_start > aligned_end or aligned_start == aligned_end
            )
            if invalid_range:
                self.data_checked = False
                self.data_info_label.config(
                    text=f"{range_str}\n⚠️ 该时间范围不存在K线（请检查输入的时间和周期）"
                )
                self.repair_btn.config(state=DISABLED)
                self.test_btn.config(state=DISABLED)
                self.log_message(f"该时间范围不存在K线（请检查输入的时间和周期）")
                return
            interval_minutes = self.get_interval_minutes(interval)
            df = self.filter_data_by_input_time(df)
            missing_ranges = self.detect_missing_ranges(df, aligned_start, aligned_end, interval_minutes)
            if not missing_ranges:
                self.log_message("✅ 数据完整，没有缺失")
                self.repair_btn.config(state=DISABLED)
                self.data_checked = True
                self.last_checked_range = (aligned_start, aligned_end)
                self.data_info_label.config(
                    text=f"{range_str}\n✅ 已通过完整性检查"
                )
                # 完整性通过，允许测试最大涨跌幅
                self.test_btn.config(state=NORMAL)
                return
            # 有缺失区间
            self.log_message(f"❌ 数据不完整，缺失区间如下：")
            self.repair_btn.config(state=NORMAL)
            self.test_btn.config(state=DISABLED)
            self.data_checked = False
            self.last_checked_range = (aligned_start, aligned_end)
            self.data_info_label.config(
                text=f"{range_str}\n❌ 未通过完整性检查"
            )
            # 计算缺失区间右端（收盘时间）
            for rng in missing_ranges:
                start_dt = self.align_time_to_interval(rng[0], interval, align_forward=True) if rng[0] else None
                end_dt = self.align_time_to_interval(rng[1], interval, align_forward=False) if rng[1] else None
                total_minutes = int((end_dt - start_dt).total_seconds() // 60)
                missing_count = max(0, total_minutes // interval_minutes)
                self.log_message(f"缺失区间: {start_dt.strftime('%Y/%m/%d %H:%M')} - {end_dt.strftime('%Y/%m/%d %H:%M')}  缺失K线: {missing_count} 条")
        except Exception as e:
            self.data_checked = False
            self.data_info_label.config(text=f"--\n❌ 未通过完整性检查")
            self.log_message(f"检查数据完整性失败: {str(e)}")
            self.test_btn.config(state=DISABLED)

    def repair_data(self):
        symbol = self.symbol_var.get()
        interval = self.interval_var.get()
        if not symbol or not interval:
            return
        if getattr(self, '_repair_running', False):
            self.log_message("修复任务正在进行中，请勿重复启动！")
            return
        self._repair_running = True
        self._repair_stop_flag = False
        self.check_btn.config(state=DISABLED)
        self.stop_repair_btn.config(state=NORMAL)
        self.repair_btn.config(state=DISABLED)
        def do_repair():
            try:
                df = self.load_klines_from_local(symbol, interval)
                start_dt, end_dt = self.get_custom_time_range()
                interval_minutes = self.get_interval_minutes(interval)
                aligned_start = self.align_time_to_interval(start_dt, interval, align_forward=True) if start_dt else None
                aligned_end = self.align_time_to_interval(end_dt, interval, align_forward=False) if end_dt else None
                # 应用safe_end
                safe_end = self.get_safe_end(interval)
                if aligned_end and aligned_end > safe_end:
                    aligned_end = safe_end
                df = self.filter_data_by_input_time(df)
                missing_ranges = self.detect_missing_ranges(df, aligned_start, aligned_end, interval_minutes)
                if not missing_ranges:
                    self.log_text.after(0, lambda: self.log_message("数据已完整，无需修复"))
                    self.repair_btn.after(0, lambda: self.repair_btn.config(state=NORMAL))
                    self.check_btn.after(0, lambda: self.check_btn.config(state=NORMAL))
                    self.stop_repair_btn.after(0, lambda: self.stop_repair_btn.config(state=DISABLED))
                    self._repair_running = False
                    return
                for rng in missing_ranges:
                    # 区间对齐与check_data_integrity一致
                    start_time = self.align_time_to_interval(rng[0], interval, align_forward=True) if rng[0] else None
                    end_time = self.align_time_to_interval(rng[1], interval, align_forward=False) if rng[1] else None
                    total_minutes = int((end_time - start_time).total_seconds() // 60)
                    missing_count = max(0, total_minutes // interval_minutes)
                    # 日志区间严格与检查一致
                    self.log_text.after(0, lambda s=start_time, e=end_time, n=missing_count: self.log_message(
                        f"正在修复区间: {s.strftime('%Y/%m/%d %H:%M')} - {e.strftime('%Y/%m/%d %H:%M')}"
                    ))
                    current_start = start_time
                    while current_start < end_time:
                        if getattr(self, '_repair_stop_flag', False):
                            self.log_text.after(0, lambda: self.log_message("用户已请求停止修复，正在退出..."))
                            self.repair_btn.after(0, lambda: self.repair_btn.config(state=NORMAL))
                            self.check_btn.after(0, lambda: self.check_btn.config(state=NORMAL))
                            self.stop_repair_btn.after(0, lambda: self.stop_repair_btn.config(state=DISABLED))
                            self._repair_running = False
                            self.log_text.after(0, lambda: self.check_data_integrity())
                            return
                        max_span = pd.Timedelta(minutes=interval_minutes * 1000)
                        batch_end = min(current_start + max_span, end_time)
                        batch_end = pd.to_datetime(batch_end)
                        start_utc = current_start - pd.Timedelta(hours=8)
                        end_utc = batch_end - pd.Timedelta(hours=8)
                        start_ms = int(start_utc.timestamp() * 1000)
                        end_ms = int(end_utc.timestamp() * 1000) - 1
                        klines = self.client.klines(
                            symbol=symbol,
                            interval=interval,
                            startTime=start_ms,
                            endTime=end_ms,
                            limit=1000
                        )
                        if klines:
                            repair_df = pd.DataFrame(klines, columns=[
                                'Open time', 'Open', 'High', 'Low', 'Close', 'Volume',
                                'Close time', 'Quote asset volume', 'Number of trades',
                                'Taker buy base asset volume', 'Taker buy quote asset volume', 'Ignore'
                            ])
                            repair_df['Open time'] = pd.to_datetime(repair_df['Open time'], unit='ms') + pd.Timedelta(hours=8)
                            repair_df['Close time'] = pd.to_datetime(repair_df['Close time'], unit='ms') + pd.Timedelta(hours=8)
                            # 只保留 current_start <= kline['Open time'] < batch_end
                            repair_df = repair_df[(repair_df['Open time'] >= current_start) & (repair_df['Open time'] < batch_end)]
                            self.save_klines_to_local(repair_df, symbol, interval)
                            n = len(repair_df)
                            self.log_text.after(0, lambda cs=current_start, ce=batch_end, nn=n: self.log_message(
                                f"成功补充了 {nn} 条数据: {cs.strftime('%Y/%m/%d %H:%M')} - {ce.strftime('%Y/%m/%d %H:%M')}"
                            ))
                        else:
                            self.log_text.after(0, lambda cs=current_start, ce=batch_end: self.log_message(
                                f"API未返回任何K线数据，区间: {cs.strftime('%Y/%m/%d %H:%M')} - {ce.strftime('%Y/%m/%d %H:%M')}"
                            ))
                        current_start = batch_end
                self.log_text.after(0, lambda: self.check_data_integrity())
                self.log_text.after(0, lambda: self.update_data_info())
                self.repair_btn.after(0, lambda: self.repair_btn.config(state=NORMAL))
                self.check_btn.after(0, lambda: self.check_btn.config(state=NORMAL))
                self.stop_repair_btn.after(0, lambda: self.stop_repair_btn.config(state=DISABLED))
                self._repair_running = False
            except Exception as e:
                self.log_text.after(0, lambda: self.log_message(f"修复数据失败: {str(e)}"))
                self.repair_btn.after(0, lambda: self.repair_btn.config(state=NORMAL))
                self.check_btn.after(0, lambda: self.check_btn.config(state=NORMAL))
                self.stop_repair_btn.after(0, lambda: self.stop_repair_btn.config(state=DISABLED))
                self._repair_running = False
        threading.Thread(target=do_repair, daemon=True).start()

    def get_interval_minutes(self, interval):
        """将时间周期转换为分钟数"""
        interval_map = {
            '1m': 1,
            '5m': 5,
            '15m': 15,
            '30m': 30,
            '1h': 60,
            '4h': 240,
            '1d': 1440,
            '1w': 10080
        }
        return interval_map.get(interval, 1)

    def log_message(self, message):
        """在日志区域显示消息，只有在最底部时才自动滚动"""
        self.log_text.config(state=NORMAL)
        # 判断当前是否在最底部
        yview = self.log_text.yview()
        at_bottom = yview[1] >= 0.999  # 允许微小误差
        self.log_text.insert(END, f"{datetime.now().strftime('%H:%M:%S')} - {message}\n")
        if at_bottom:
            self.log_text.see(END)
        self.log_text.config(state=DISABLED)

    def get_kline_file_path(self, symbol, interval, dt=None):
        symbol_dir = os.path.join(KLINE_DATA_DIR, symbol)
        os.makedirs(symbol_dir, exist_ok=True)
        if interval == '1m' and dt is not None:
            month_str = get_month_str(dt)
            return os.path.join(symbol_dir, f"{month_str}.csv")
        else:
            return os.path.join(symbol_dir, f"{interval}.csv")

    def load_klines_from_local(self, symbol, interval, start_dt=None, end_dt=None):
        try:
            symbol_dir = os.path.join(KLINE_DATA_DIR, symbol)
            if interval == '1m':
                # 自动合并所需月份
                if start_dt is None or end_dt is None:
                    # 没有指定范围，合并所有1m分月csv
                    files = [f for f in os.listdir(symbol_dir) if f.endswith('.csv') and len(f) == 11]
                else:
                    months = get_month_range(start_dt, end_dt)
                    files = [f"{m}.csv" for m in months if os.path.exists(os.path.join(symbol_dir, f"{m}.csv"))]
                dfs = []
                for f in files:
                    file_path = os.path.join(symbol_dir, f)
                    try:
                        df = pd.read_csv(file_path)
                        if not df.empty and 'Open time' in df.columns:
                            df['Open time'] = pd.to_datetime(df['Open time'])
                            dfs.append(df)
                    except Exception as e:
                        logging.error(f"读取分月K线失败: {file_path}, {e}")
                if dfs:
                    all_df = pd.concat(dfs)
                    all_df = all_df.drop_duplicates(subset=['Open time'], keep='last')
                    all_df = all_df.sort_values(by='Open time')
                    # 按需筛选
                    if start_dt is not None:
                        all_df = all_df[all_df['Open time'] >= start_dt]
                    if end_dt is not None:
                        all_df = all_df[all_df['Open time'] <= end_dt]
                    return all_df.reset_index(drop=True)
                else:
                    return pd.DataFrame()
            else:
                file_path = self.get_kline_file_path(symbol, interval)
                if os.path.exists(file_path):
                    df = pd.read_csv(file_path)
                    if not df.empty and 'Open time' in df.columns:
                        df['Open time'] = pd.to_datetime(df['Open time'])
                        if start_dt is not None:
                            df = df[df['Open time'] >= start_dt]
                        if end_dt is not None:
                            df = df[df['Open time'] <= end_dt]
                        return df.reset_index(drop=True)
                return pd.DataFrame()
        except Exception as e:
            logging.error(f"加载本地K线数据失败: {str(e)}")
            return pd.DataFrame()

    def save_klines_to_local(self, klines_df, symbol, interval):
        try:
            # 自动创建symbol目录，防止目录不存在导致保存失败
            symbol_dir = os.path.join(KLINE_DATA_DIR, symbol)
            os.makedirs(symbol_dir, exist_ok=True)
            df = klines_df.copy()
            if 'Open time' in df.columns:
                df['Open time'] = pd.to_datetime(df['Open time'])
            if interval == '1m':
                if df.empty:
                    return
                df['month'] = df['Open time'].dt.strftime('%Y-%m')
                for month, group in df.groupby('month'):
                    file_path = os.path.join(KLINE_DATA_DIR, symbol, f"{month}.csv")
                    if os.path.exists(file_path):
                        try:
                            existing_df = pd.read_csv(file_path)
                            if 'Open time' in existing_df.columns:
                                existing_df['Open time'] = pd.to_datetime(existing_df['Open time'])
                            combined_df = pd.concat([existing_df, group.drop(columns=['month'])])
                            combined_df = combined_df.drop_duplicates(subset=['Open time'], keep='last')
                            combined_df = combined_df.sort_values(by='Open time')
                            combined_df.to_csv(file_path, index=False)
                        except Exception as e:
                            group.drop(columns=['month']).to_csv(file_path, index=False)
                    else:
                        group.drop(columns=['month']).to_csv(file_path, index=False)
            else:
                file_path = self.get_kline_file_path(symbol, interval)
                if os.path.exists(file_path):
                    try:
                        existing_df = pd.read_csv(file_path)
                        if 'Open time' in existing_df.columns:
                            existing_df['Open time'] = pd.to_datetime(existing_df['Open time'])
                        combined_df = pd.concat([existing_df, df])
                        combined_df = combined_df.drop_duplicates(subset=['Open time'], keep='last')
                        combined_df = combined_df.sort_values(by='Open time')
                        combined_df.to_csv(file_path, index=False)
                    except Exception as e:
                        df.to_csv(file_path, index=False)
                else:
                    df.to_csv(file_path, index=False)
        except Exception as e:
            self.log_message(f"保存K线数据失败: {str(e)}")

    def get_custom_time_range(self):
        """获取自定义时间范围，返回(start_dt, end_dt)，无效返回None"""
        def parse_time(y, m, d, h, mi):
            try:
                if all([y, m, d, h, mi]):
                    return pd.to_datetime(f"{y}/{m}/{d} {h}:{mi}", format="%Y/%m/%d %H:%M")
            except Exception:
                return None
            return None
        start_dt = parse_time(self.start_year.get().strip(), self.start_month.get().strip(), self.start_day.get().strip(),
                              self.start_hour.get().strip(), self.start_minute.get().strip())
        end_dt = parse_time(self.end_year.get().strip(), self.end_month.get().strip(), self.end_day.get().strip(),
                            self.end_hour.get().strip(), self.end_minute.get().strip())
        return start_dt, end_dt

    def stop_repair(self):
        """停止修复数据的后台线程"""
        self._repair_stop_flag = True

    def align_time_to_interval(self, dt, interval, align_forward=False):
        if dt is None:
            return None
        if interval.endswith('m'):
            minutes = int(interval[:-1])
            total_minutes = dt.hour * 60 + dt.minute
            if align_forward:
                aligned_minutes = ((total_minutes + minutes - 1) // minutes) * minutes
            else:
                aligned_minutes = (total_minutes // minutes) * minutes
            hour = aligned_minutes // 60
            minute = aligned_minutes % 60
            aligned = dt.replace(hour=hour, minute=minute, second=0, microsecond=0)
            if align_forward and aligned < dt:
                aligned += pd.Timedelta(minutes=minutes)
            if not align_forward and aligned > dt:
                aligned -= pd.Timedelta(minutes=minutes)
            return aligned
        elif interval.endswith('h'):
            hours = int(interval[:-1])
            if align_forward:
                aligned_hour = ((dt.hour + hours - 1) // hours) * hours
            else:
                aligned_hour = (dt.hour // hours) * hours
            aligned = dt.replace(hour=aligned_hour, minute=0, second=0, microsecond=0)
            if align_forward and aligned < dt:
                aligned += pd.Timedelta(hours=hours)
            if not align_forward and aligned > dt:
                aligned -= pd.Timedelta(hours=hours)
            return aligned
        elif interval == '1d':
            base = dt.replace(hour=8, minute=0, second=0, microsecond=0)
            if align_forward:
                if dt < base:
                    aligned = base
                else:
                    days = ((dt - base).days + (1 if dt.time() > base.time() else 0))
                    aligned = base + pd.Timedelta(days=days)
            else:
                if dt < base:
                    aligned = base - pd.Timedelta(days=1)
                else:
                    days = (dt - base).days
                    aligned = base + pd.Timedelta(days=days)
            return aligned
        elif interval == '1w':
            # 币安期货周线对齐到每周一08:00（北京时间）
            weekday = dt.weekday()  # 周一为0
            base = dt.replace(hour=8, minute=0, second=0, microsecond=0) - pd.Timedelta(days=weekday)
            if align_forward:
                if dt < base:
                    aligned = base
                else:
                    weeks = ((dt - base).days // 7) + (1 if dt > base else 0)
                    aligned = base + pd.Timedelta(weeks=weeks)
            else:
                if dt < base:
                    aligned = base - pd.Timedelta(weeks=1)
                else:
                    weeks = (dt - base).days // 7
                    aligned = base + pd.Timedelta(weeks=weeks)
            return aligned
        else:
            return dt

    def _on_timebox_change(self):
        self.data_checked = False
        self.update_data_info(auto_fill=False)

    def open_test_dialog(self):
        """弹出输入百分比的窗口"""
        dialog = Toplevel(self)
        dialog.title("测试最大涨跌幅参数设置")
        dialog.geometry("300x120")
        dialog.transient(self)
        dialog.grab_set()
        Label(dialog, text="请输入反弹/回调百分比（如5表示5%）:").pack(pady=10)
        percent_var = StringVar(value="5")
        entry = Entry(dialog, textvariable=percent_var, width=10)
        entry.pack(pady=5)
        entry.focus_set()
        def on_confirm():
            try:
                percent = float(percent_var.get())
                dialog.destroy()
                self.log_message(f"开始测试最大涨跌幅，百分比={percent}%...")
                self._test_running = True
                self._test_stop_flag = False
                self.test_btn.config(state=DISABLED)
                self.stop_test_btn.config(state=NORMAL)
                threading.Thread(target=lambda: self.test_max_rebound_drawdown(percent), daemon=True).start()
            except Exception as e:
                self.log_message(f"输入无效: {e}")
        Button(dialog, text="确定", command=on_confirm).pack(pady=10)

    def stop_test(self):
        """停止最大涨跌幅测试线程"""
        self._test_stop_flag = True

    def test_max_rebound_drawdown(self, percent):
        """遍历K线，测试最大涨跌幅，保存结果到csv（线程安全）"""
        import os, csv
        symbol = self.symbol_var.get()
        interval = self.interval_var.get()
        start_dt, end_dt = self.get_custom_time_range()
        df = self.load_klines_from_local(symbol, interval, start_dt, end_dt)
        if df.empty:
            self.log_message("无K线数据，无法测试")
            self.test_btn.after(0, lambda: self.test_btn.config(state=NORMAL))
            self.stop_test_btn.after(0, lambda: self.stop_test_btn.config(state=DISABLED))
            self._test_running = False
            return
        percent = float(percent) / 100.0
        results = []
        i = 0
        total = len(df)
        while i < total - 1:
            if getattr(self, '_test_stop_flag', False):
                self.log_text.after(0, lambda: self.log_message("用户已请求停止测试，正在退出..."))
                self.test_btn.after(0, lambda: self.test_btn.config(state=NORMAL))
                self.stop_test_btn.after(0, lambda: self.stop_test_btn.config(state=DISABLED))
                self._test_running = False
                return
            first_idx = i
            first_row = df.iloc[first_idx]
            min_price = first_row['Low']
            max_price = first_row['High']
            max_price_idx = first_idx
            min_price_idx = first_idx
            min_price_time = str(first_row['Open time'])
            for j in range(i+1, total):
                row = df.iloc[j]
                max_updated = False
                case_type = 1
                prev_max = max_price
                prev_min = min_price
                prev_max_idx = max_price_idx
                prev_min_idx = min_price_idx
                # 判断最高最低价是否更新
                if row['Low'] < min_price and row['High'] > max_price:
                    # 最低价和最高价都更新
                    min_price = row['Low']
                    max_price = row['High']
                    min_price_idx = j
                    max_price_idx = j
                    min_price_time = str(row['Open time'])
                    case_type = 4
                    max_updated = True
                elif row['Low'] < min_price:
                    # 最低价更新，最高价未更新
                    min_price = row['Low']
                    min_price_idx = j
                    min_price_time = str(row['Open time'])
                    case_type = 2
                elif row['High'] > max_price:
                    # 最高价更新，最低价未更新
                    max_price = row['High']
                    max_price_idx = j
                    case_type = 3
                    max_updated = True
                else:
                    # 最低价和最高价都未更新
                    case_type = 1
                if case_type == 2:
                    rise = (row['Close'] - min_price) / min_price
                else:
                    rise = (row['High'] - min_price) / min_price
                if rise > percent:
                    max_drawdown = (min_price - prev_max) / prev_max if prev_max != 0 else 0
                    start_time = str(df.iloc[first_idx]['Open time'])
                    end_time = str(row['Open time'])
                    # 计算下跌时长和反弹时长
                    fall_duration = min_price_idx - first_idx
                    rebound_duration = j - min_price_idx
                    period = j - first_idx
                    results.append({
                        'start_time': start_time,
                        'min_price_time': min_price_time,
                        'end_time': end_time,
                        'type': case_type,
                        'drawdown': max_drawdown,
                        'rebound': rise,
                        'fall_duration': fall_duration,
                        'rebound_duration': rebound_duration,
                        'period': period
                    })
                    # 日志输出百分比和时长
                    log_msg = f"[{len(results)}] {start_time} ~ {end_time} 类型{case_type} 跌幅: {max_drawdown*100:.2f}% 反弹: {rise*100:.2f}% 时长: {period}分钟"
                    self.log_text.after(0, lambda msg=log_msg: self.log_message(msg))
                    i = j
                    break
                elif rise <= percent and max_updated:
                    i = j
                    break
            else:
                break
        self.save_test_results(symbol, interval, percent, results)
        self.log_text.after(0, lambda: self.log_message(f"测试完成，结果已保存，共{len(results)}条"))
        self.test_btn.after(0, lambda: self.test_btn.config(state=NORMAL))
        self.stop_test_btn.after(0, lambda: self.stop_test_btn.config(state=DISABLED))
        self._test_running = False

    def save_test_results(self, symbol, interval, percent, results):
        import os, csv
        result_dir = os.path.join(os.getcwd(), "kline_test_results")
        os.makedirs(result_dir, exist_ok=True)
        percent_int = int(percent*100)
        percent_str = str(percent).replace('.', '_')
        filename = f"{symbol}-{interval}-{percent_str}%.csv"
        filepath = os.path.join(result_dir, filename)
        # 新增字段
        fieldnames = ['start_time', 'min_price_time', 'end_time', 'type', 'drawdown', 'rebound', 'fall_duration', 'rebound_duration', 'period']
        with open(filepath, "w", newline='', encoding="utf-8") as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            for row in results:
                writer.writerow(row)

    def toggle_chart_panel(self):
        if not self.chart_collapsed:
            # 收起前，先记住当前分割条位置
            try:
                self._last_sash_pos = int(self.paned.sash_coord(0)[1])-1
                # print("记录分割条高度:", self._last_sash_pos)
            except Exception as e:
                # print("获取分割条高度失败:", e)
                pass

        # 移除所有子项
        for pane in self.paned.panes():
            self.paned.forget(pane)
        if self.chart_collapsed:
            # 展开时，先加图表区，再加日志区
            if not self.paned.winfo_ismapped():
                self.paned.pack(fill=BOTH, expand=True, padx=0, pady=0)
            self.paned.add(self.chart_panel, height=self._last_sash_pos)
            self.paned.add(self.log_frame, stretch='always')

            # 智能修复：模拟分割条拖动来修复图表显示问题
            # 既然拖动分割条能恢复正常，我们就自动执行这个操作
            try:
                # 延迟执行，确保图表完全加载
                self.after(1, self._auto_fix_chart_display)
            except Exception as e:
                print(f"自动修复图表显示失败: {e}")

            self.toggle_chart_btn.config(text="收起K线图表")
            self.chart_collapsed = False
        else:
            # 收起时，只加日志区
            self.paned.add(self.log_frame, stretch='always')
            self.toggle_chart_btn.config(text="展开K线图表")
            self.chart_collapsed = True

    def get_chart_time_range(self):
        def parse_time(y, m, d, h, mi):
            try:
                if all([y, m, d, h, mi]):
                    return pd.to_datetime(f"{y}/{m}/{d} {h}:{mi}", format="%Y/%m/%d %H:%M")
            except Exception:
                return None
            return None
        start_dt = parse_time(self.chart_start_year.get().strip(), self.chart_start_month.get().strip(), self.chart_start_day.get().strip(),
                              self.chart_start_hour.get().strip(), self.chart_start_minute.get().strip())
        end_dt = parse_time(self.chart_end_year.get().strip(), self.chart_end_month.get().strip(), self.chart_end_day.get().strip(),
                            self.chart_end_hour.get().strip(), self.chart_end_minute.get().strip())
        return start_dt, end_dt

    def setup_charles_style_for_manager(self, ax, font_prop, symbol):
        """为K线管理界面设置charles风格的图表样式"""
        # 设置背景色为白色
        ax.set_facecolor('white')

        # 移除四周边框
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['bottom'].set_visible(False)
        ax.spines['left'].set_visible(False)

        # Y轴移到右侧
        ax.yaxis.tick_right()
        ax.yaxis.set_label_position("right")

        # 设置网格样式 - 仿照charles风格，使用点线
        ax.grid(True, which='major', axis='both',
               color='gray', linestyle=':', linewidth=0.4, alpha=0.7)
        # charles样式没有次要网格线，只有主要网格线
        ax.grid(False, which='minor')

        # 设置刻度样式
        ax.tick_params(axis='both', which='major',
                      labelsize=8, colors='gray',
                      length=4, width=0.5)
        ax.tick_params(axis='both', which='minor',
                      length=0, width=0)  # 隐藏次要刻度

        # 设置坐标轴格式化器
        self.setup_axis_formatters_for_manager(ax, font_prop, symbol)

        # 移除轴标题
        ax.set_xlabel('')
        ax.set_ylabel('')

        # 设置紧凑的边距
        ax.margins(x=0.01, y=0.02)

    def setup_axis_formatters_for_manager(self, ax, font_prop, symbol):
        """为K线管理界面设置坐标轴格式化器"""
        try:
            # Y轴价格格式化器
            config_manager = SymbolConfig()
            price_precision = config_manager.get_price_precision(symbol)

            def price_formatter(x, pos):
                rounded_x = round(x, price_precision)
                return f'{rounded_x:,.{price_precision}f}'

            ax.yaxis.set_major_formatter(FuncFormatter(price_formatter))

            # X轴时间格式化器
            def x_date_formatter(x, pos):
                try:
                    x_index = int(round(x))
                    if x_index < 0 or x_index >= len(self.display_klines):
                        return ""
                    kline_time = self.display_klines.iloc[x_index]['Open time']
                    return pd.to_datetime(kline_time).strftime('%Y-%m-%d %H:%M')
                except:
                    return ""

            ax.xaxis.set_major_formatter(FuncFormatter(x_date_formatter))

            # 设置刻度定位器 - 仿照charles风格
            ax.xaxis.set_major_locator(MaxNLocator(integer=True, nbins=5))
            ax.yaxis.set_major_locator(MaxNLocator(nbins=8))

            # 设置字体
            for label in ax.get_xticklabels() + ax.get_yticklabels():
                label.set_fontproperties(font_prop)
                label.set_fontsize(8)
                label.set_color('gray')
                label.set_rotation(0)

        except Exception as e:
            logging.error(f"K线管理界面设置坐标轴格式化器失败: {str(e)}")

    def setup_tight_layout_for_manager(self, fig, ax):
        """为K线管理界面设置紧凑布局，减少空白"""
        try:
            # 方法1：使用subplots_adjust精确控制边距
            fig.subplots_adjust(
                left=0.02,    # 左边距2%
                right=0.92,   # 右边距5%（为右侧Y轴留空间）
                top=0.98,     # 上边距2%
                bottom=0.1,  # 下边距8%（为X轴标签留空间）
                hspace=0.0,   # 子图间垂直间距
                wspace=0.0    # 子图间水平间距
            )

            # 方法2：设置更紧凑的轴边距
            ax.margins(x=0.005, y=0.01)  # 进一步减少轴边距

            # 方法3：调整刻度标签的位置，避免被截断
            ax.tick_params(axis='x', pad=2)  # X轴标签与轴的距离
            ax.tick_params(axis='y', pad=2)  # Y轴标签与轴的距离

            # logging.info("K线管理界面紧凑布局设置完成")

        except Exception as e:
            logging.error(f"K线管理界面设置紧凑布局失败: {str(e)}")

    def create_chart(self):
        """创建K线管理界面的初始图表（模仿主K线界面）"""
        print("开始创建K线管理界面图表...")

        # 清理旧图和matplotlib figure，避免内存泄漏
        for w in self.chart_canvas_frame.winfo_children():
            w.destroy()

        # 关闭之前的matplotlib figure，避免内存泄漏
        if hasattr(self, 'fig') and self.fig is not None:
            import matplotlib.pyplot as plt
            plt.close(self.fig)

        # 创建单根空K线用于初始化
        import pandas as pd
        open_time = pd.to_datetime(0, unit='ms') + pd.Timedelta(hours=8)
        close_time = pd.to_datetime(1, unit='ms') + pd.Timedelta(hours=8)
        empty_data = {
            'Open time': [open_time],
            'Open': [0],
            'High': [0],
            'Low': [0],
            'Close': [0],
            'Volume': [0],
            'Close time': [close_time],
            'Quote asset volume': [0],
            'Number of trades': [0],
            'Taker buy base asset volume': [0],
            'Taker buy quote asset volume': [0],
            'Ignore': [0]
        }
        empty_df = pd.DataFrame(empty_data)

        # 设置字体
        import matplotlib.font_manager as fm
        font_path = 'C:/Windows/Fonts/msyh.ttc'
        font_prop = fm.FontProperties(fname=font_path)

        # 获取DPI缩放因子
        import ctypes
        try:
            scale_factor = ctypes.windll.shcore.GetScaleFactorForDevice(0) / 100
        except:
            scale_factor = 1.0

        chart_dpi = int(100)

        # 智能方案：通过画布大小倒推figsize设定，确保图表完全适配容器
        # 1. 强制更新布局，获取准确的frame尺寸
        self.chart_canvas_frame.update_idletasks()

        # 2. 获取chart_canvas_frame的实际尺寸
        frame_width = self.chart_canvas_frame.winfo_width()
        frame_height = self.chart_canvas_frame.winfo_height()

        # 3. 先使用经验值，稍后用实际值更新
        if scale_factor == 1.0:
            toolbar_height = 36
        elif scale_factor == 1.25:
            toolbar_height = 44
        elif scale_factor == 1.5:
            toolbar_height = 50
        elif scale_factor == 1.75:
            toolbar_height = 56
        else:
            toolbar_height = 64

        # 4. 计算图表可用空间
        available_width = frame_width
        available_height = frame_height - toolbar_height

        # 5. 通过可用空间反推figsize
        if frame_width > 1 and frame_height > 1:  # 确保frame尺寸有效
            figsize_width = available_width / chart_dpi
            figsize_height = available_height / chart_dpi
        else:
            # 如果无法获取frame尺寸，使用默认值
            figsize_width, figsize_height = 10, 7

        import matplotlib.pyplot as plt
        self.fig, self.ax_main = plt.subplots(figsize=(0.01, 0.01), dpi=chart_dpi)

        # 设置charles风格的图表样式
        self.setup_charles_style_for_manager(self.ax_main, font_prop, "BTCUSDT")

        # 处理空数据
        df_plot = empty_df.copy()
        df_plot.set_index('Open time', inplace=True)
        self.display_klines = df_plot.reset_index()  # 用于索引和时间映射
        self.klines_df = df_plot  # 用于高低价标签

        # 绘制空K线
        self._draw_klines_for_manager(df_plot, self.ax_main)

        # 设置紧凑布局，减少空白
        self.setup_tight_layout_for_manager(self.fig, self.ax_main)

        # 将图表放在frame中
        from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
        self.chart_canvas = FigureCanvasTkAgg(self.fig, master=self.chart_canvas_frame)

        # 创建工具栏
        self._create_chart_toolbar()

        # 先pack工具栏到底部，再pack canvas填充剩余空间
        from tkinter import BOTTOM, X, BOTH
        self.toolbar.pack(side=BOTTOM, fill=X)
        self.chart_canvas.get_tk_widget().pack(fill=BOTH, expand=True)

        # 获取并缓存工具栏的确切高度
        self.toolbar.update_idletasks()
        actual_toolbar_height = self.toolbar.winfo_reqheight()

        # 缓存工具栏高度供下次使用
        if not hasattr(self, '_cached_toolbar_height'):
            self._cached_toolbar_height = actual_toolbar_height

        # 为交互绑定准备必要的变量
        config_manager = SymbolConfig()
        price_precision = config_manager.get_price_precision("BTCUSDT")  # 使用默认值

        # 设置字体
        import matplotlib.font_manager as fm
        font_path = 'C:/Windows/Fonts/msyh.ttc'
        font_prop = fm.FontProperties(fname=font_path)

        # 交互绑定
        self.setup_chart_interactions(self.chart_canvas, self.ax_main, self.fig, font_prop, price_precision, "1m")

        self.chart_canvas.draw_idle()

        print("K线管理界面图表创建完成")
        # 最后加载真实数据
        self.generate_chart()

    def generate_chart(self):
        """重绘K线管理界面图表（模仿主K线界面的load_kline_data）"""
        try:
            # 清除现有的图表内容（模仿测试程序的ax.clear()）
            self.ax_main.clear()

            # 1. 获取参数
            symbol = self.chart_symbol_var.get().strip().upper()
            interval = self.chart_interval_var.get()
            indicator = self.chart_indicator_var.get()
            start_dt, end_dt = self.get_chart_time_range()
            import pandas as pd
            def get_single_dummy_kline():
                data = {
                    'Open time': [0],
                    'Open': [0],
                    'High': [0],
                    'Low': [0],
                    'Close': [0],
                    'Volume': [0],
                    'Close time': [1],
                    'Quote asset volume': [0],
                    'Number of trades': [0],
                    'Taker buy base asset volume': [0],
                    'Taker buy quote asset volume': [0],
                    'Ignore': [0]
                }
                return pd.DataFrame(data)
            # 2. 校验区间完整性
            if not symbol or not interval or not start_dt or not end_dt or start_dt >= end_dt:
                # 构造一根空K线
                df = get_single_dummy_kline()
            else:
                # 3. 加载本地K线
                df = self.load_klines_from_local(symbol, interval, start_dt, end_dt)
                if df.empty:
                    # 构造一根空K线
                    df = get_single_dummy_kline()

            # 设置字体
            import matplotlib.font_manager as fm
            font_path = 'C:/Windows/Fonts/msyh.ttc'
            font_prop = fm.FontProperties(fname=font_path)

            # 重新设置charles风格的图表样式（因为ax被清除了）
            self.setup_charles_style_for_manager(self.ax_main, font_prop, symbol)

            # 处理数据
            df_plot = df.copy()
            df_plot.set_index('Open time', inplace=True)
            self.display_klines = df_plot.reset_index()  # 用于索引和时间映射
            self.klines_df = df_plot  # 用于高低价标签

            # 绘制K线
            self._draw_klines_for_manager(df_plot, self.ax_main)

            # 绘制指标线
            self.draw_chart_indicators()

            # 信息栏内容
            # 只有有效K线数据时才显示区间信息
            if not df.empty and not (len(df) == 1 and df.iloc[0].get('Open', 0) == 0 and df.iloc[0].get('Close', 0) == 0):
                self.toolbar.status_bar.config(text=f"显示{len(df_plot)}根K线  区间: {df_plot.index[0]} ~ {df_plot.index[-1]}")
            elif not symbol or not interval or not start_dt or not end_dt or start_dt >= end_dt:
                self.toolbar.status_bar.config(text="请填写完整且有效的自定义时间区间")
            else:
                self.toolbar.status_bar.config(text="本地无数据或区间无K线")

            # 初始高低价标签
            self.update_chart_range_labels()

            # 刷新画布（模仿测试程序的canvas.draw()）
            self.chart_canvas.draw_idle()

        except Exception as e:
            import logging
            logging.error(f"重绘K线管理界面图表失败: {str(e)}")
            import traceback
            traceback.print_exc()  # 打印详细错误信息

    def _create_chart_toolbar(self):
        """创建K线管理界面的工具栏"""
        from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
        from tkinter import RIGHT, X, LEFT, Frame, Label, SUNKEN, W, E

        class CustomNavigationToolbar(NavigationToolbar2Tk):
            toolitems = [t for t in NavigationToolbar2Tk.toolitems if t[0] not in ('Subplots', 'Pan')]
            def __init__(self, canvas, parent, kline_page=None):
                self.kline_page = kline_page
                self.zoom_active = False
                super().__init__(canvas, parent)
                self._remove_message_label()
                self.create_custom_status_widgets()
            def _remove_message_label(self):
                for child in self.pack_slaves():
                    if isinstance(child, Label):
                        if hasattr(child, 'message') or child.winfo_name() == "Message":
                            child.pack_forget()
            def create_custom_status_widgets(self):
                self.status_frame = Frame(self)
                self.status_frame.pack(side=RIGHT, fill=X, expand=True)
                self.status_bar = Label(self.status_frame, text="", bd=1, relief=SUNKEN, anchor=W)
                self.status_bar.pack(side=LEFT, fill=X, expand=True)
                self.time_label = Label(self.status_frame, text="", bd=1, relief=SUNKEN, anchor=E, width=20)
                self.time_label.pack(side=RIGHT)
                if self.kline_page:
                    start_time_update(self.time_label)
            def set_message(self, message):
                if hasattr(self, 'status_bar'):
                    self.status_bar.config(text=message)
            def home(self, *args):
                if self.kline_page and hasattr(self.kline_page, 'reset_view'):
                    self.kline_page.reset_view()
                else:
                    super().home(*args)
            def zoom(self, *args):
                super().zoom(*args)
                self.zoom_active = not self.zoom_active
                if self.zoom_active:
                    self.status_bar.config(text="放大镜工具已激活 - 左键拖动选择区域进行放大")
                else:
                    pass

        if hasattr(self, 'toolbar'):
            self.toolbar.destroy()
        self.toolbar = CustomNavigationToolbar(self.chart_canvas, self.chart_canvas_frame, self)
        self.toolbar.update()

    def _draw_klines_for_manager(self, df_plot, ax):
        """为K线管理界面绘制K线"""
        try:
            from matplotlib.collections import LineCollection, PatchCollection
            import matplotlib.patches as patches

            # 直接在这里实现优化的绘制逻辑
            shadow_lines = []
            body_rects = []
            border_rects = []
            shadow_colors = []
            body_colors = []
            border_colors = []

            for i, (index, row) in enumerate(df_plot.iterrows()):
                open_price = float(row['Open'])
                high_price = float(row['High'])
                low_price = float(row['Low'])
                close_price = float(row['Close'])

                color = 'g' if close_price >= open_price else 'r'

                # 添加上下影线（分为两段）
                shadow_lines.append([(i, low_price), (i, min(open_price, close_price))])
                shadow_colors.append(color)
                shadow_lines.append([(i, max(open_price, close_price)), (i, high_price)])
                shadow_colors.append(color)

                # K线实体和边框
                body_height = abs(close_price - open_price)
                body_bottom = min(open_price, close_price)

                body_rect = patches.Rectangle((i - 0.25, body_bottom), 0.5, body_height)
                body_rects.append(body_rect)
                body_colors.append(color)

                border_rect = patches.Rectangle((i - 0.25, body_bottom), 0.5, body_height)
                border_rects.append(border_rect)
                border_colors.append(color)

            # 批量绘制
            if shadow_lines:
                shadow_collection = LineCollection(shadow_lines, colors=shadow_colors, linewidths=0.5, alpha=1.0)
                ax.add_collection(shadow_collection)

            if body_rects:
                body_collection = PatchCollection(body_rects, facecolors=body_colors, edgecolors=body_colors, alpha=0.5, linewidths=0)
                ax.add_collection(body_collection)

            if border_rects:
                border_collection = PatchCollection(border_rects, facecolors='none', edgecolors=border_colors, alpha=1.0, linewidths=0.5)
                ax.add_collection(border_collection)

        except Exception as e:
            import logging
            logging.error(f"K线管理界面优化绘制失败: {str(e)}")
            # 如果失败，显示错误信息
            ax.text(0.5, 0.5, '图表加载失败', transform=ax.transAxes, ha='center', va='center', fontsize=12, color='red')

    def _auto_fix_chart_display(self):
        """自动修复图表显示问题：模拟分割条微调操作"""
        try:
            # 获取当前分割条位置
            current_pos = self.paned.sash_coord(0)[1]

            # 微调分割条位置：+1像素然后-1像素，模拟拖动操作
            self.paned.sash_place(0, 0, current_pos + 1)
            self.paned.update_idletasks()

            # 恢复原位置
            self.paned.sash_place(0, 0, current_pos)

            # print(f"自动修复：分割条位置 {current_pos} -> {current_pos + 1} -> {current_pos}")

        except Exception as e:
            # print(f"自动修复分割条失败: {e}")
            pass

    def setup_chart_interactions(self, canvas, ax, fig, font_prop, price_precision, interval):
        # 记录初始状态便于重置
        self.original_xlim = ax.get_xlim()
        self.original_ylim = ax.get_ylim()
        self.pan_active = False
        self.pan_start_x = None
        self.pan_start_y = None
        self.scale_active = False
        self.scale_start_x = None
        self.scale_start_y = None
        self.last_pixel_dx = 0
        self.last_pixel_dy = 0
        self.user_interacting = False
        self._drag_start = None
        # 设置默认光标为十字星
        canvas.get_tk_widget().config(cursor="crosshair")
        # 鼠标事件
        def on_motion(event):
            if event.inaxes == ax:
                x, y = event.xdata, event.ydata
                # 信息栏显示索引、时间、价格（始终显示，超出范围自动推算时间）
                if x is not None:
                    idx = int(round(x))
                    price = y
                    if 0 <= idx < len(self.display_klines):
                        kline_time = self.display_klines.iloc[idx]['Open time']
                        time_str = pd.to_datetime(kline_time).strftime('%Y-%m-%d %H:%M')
                    else:
                        # 超出范围，自动推算时间
                        if len(self.display_klines) > 0:
                            first_time = pd.to_datetime(self.display_klines.iloc[0]['Open time'])
                            if interval.endswith('m'):
                                minutes = int(interval[:-1])
                                kline_time = first_time + pd.Timedelta(minutes=minutes * idx)
                            elif interval.endswith('h'):
                                hours = int(interval[:-1])
                                kline_time = first_time + pd.Timedelta(hours=hours * idx)
                            elif interval == '1d':
                                kline_time = first_time + pd.Timedelta(days=idx)
                            elif interval == '1w':
                                kline_time = first_time + pd.Timedelta(weeks=idx)
                            else:
                                kline_time = first_time + pd.Timedelta(minutes=15 * idx)
                            time_str = kline_time.strftime('%Y-%m-%d %H:%M')
                else:
                    time_str = "-"
                self.toolbar.status_bar.config(text=f"索引: {idx}  时间: {time_str}  价格: {price:.{price_precision}f}")
                canvas.get_tk_widget().config(cursor="crosshair")
            else:
                self.toolbar.status_bar.config(text="")
                canvas.get_tk_widget().config(cursor="crosshair")
        canvas.mpl_connect('motion_notify_event', on_motion)
        def on_scroll(event):
            if event.inaxes != ax:
                return
            xlim = ax.get_xlim()
            ylim = ax.get_ylim()
            x_center = event.xdata
            y_center = event.ydata
            x_width = xlim[1] - xlim[0]
            y_height = ylim[1] - ylim[0]
            scale_factor = 1.2
            if event.button == 'up':  # 放大
                new_x_width = x_width / scale_factor
                new_y_height = y_height / scale_factor
            elif event.button == 'down':  # 缩小
                new_x_width = x_width * scale_factor
                new_y_height = y_height * scale_factor
            else:
                return
            # x轴
            new_xlim = (
                x_center - new_x_width * (x_center - xlim[0]) / x_width,
                x_center + new_x_width * (xlim[1] - x_center) / x_width
            )
            # y轴
            new_ylim = (
                y_center - new_y_height * (y_center - ylim[0]) / y_height,
                y_center + new_y_height * (ylim[1] - y_center) / y_height
            )
            ax.set_xlim(*new_xlim)
            ax.set_ylim(*new_ylim)
            fig.canvas.draw_idle()
            self.update_chart_range_labels()
            canvas.get_tk_widget().config(cursor="crosshair")
        canvas.mpl_connect('scroll_event', on_scroll)
        def on_press(event):
            if event.inaxes == ax:
                # 检查放大镜工具是否激活
                if hasattr(self, 'toolbar') and hasattr(self.toolbar, 'zoom_active') and self.toolbar.zoom_active:
                    # 放大镜工具激活时，不执行自定义的拖拽功能
                    return
                    
                self.user_interacting = True
                if event.button == 1:
                    self.pan_active = True
                    self.pan_start_x = event.xdata
                    self.pan_start_y = event.ydata
                    canvas.get_tk_widget().config(cursor="fleur")
                elif event.button == 3:
                    self.scale_active = True
                    self.scale_start_x = event.x
                    self.scale_start_y = event.y
                    self.scale_xlim = ax.get_xlim()
                    self.scale_ylim = ax.get_ylim()
                    self.last_pixel_dx = 0
                    self.last_pixel_dy = 0
                    canvas.get_tk_widget().config(cursor="sizing")
        def on_release(event):
            if event.inaxes == ax:
                if self.pan_active:
                    self.pan_active = False
                    canvas.get_tk_widget().config(cursor="crosshair")
                if self.scale_active:
                    self.scale_active = False
                    canvas.get_tk_widget().config(cursor="crosshair")
                self.user_interacting = False
            elif self.pan_active or self.scale_active:
                self.pan_active = False
                self.scale_active = False
                canvas.get_tk_widget().config(cursor="crosshair")
                self.user_interacting = False
        canvas.mpl_connect('button_press_event', on_press)
        canvas.mpl_connect('button_release_event', on_release)
        # 平移和缩放逻辑
        def on_motion_pan_zoom(event):
            if self.pan_active and event.xdata is not None and event.ydata is not None and event.inaxes == ax:
                dx = self.pan_start_x - event.xdata
                dy = self.pan_start_y - event.ydata
                x_min, x_max = ax.get_xlim()
                y_min, y_max = ax.get_ylim()
                ax.set_xlim(x_min + dx, x_max + dx)
                ax.set_ylim(y_min + dy, y_max + dy)
                fig.canvas.draw_idle()
                self.update_chart_range_labels()
                canvas.get_tk_widget().config(cursor="fleur")
            elif self.scale_active:
                if hasattr(event, 'x') and hasattr(event, 'y'):
                    pixel_dx = event.x - self.scale_start_x
                    pixel_dy = event.y - self.scale_start_y
                    self.last_pixel_dx = pixel_dx
                    self.last_pixel_dy = pixel_dy
                else:
                    pixel_dx = self.last_pixel_dx
                    pixel_dy = self.last_pixel_dy
                x_min, x_max = self.scale_xlim
                y_min, y_max = self.scale_ylim
                x_range = x_max - x_min
                y_range = y_max - y_min
                x_pixels_for_half = 100
                y_pixels_for_half = 100
                x_scale = 2 ** (-pixel_dx / x_pixels_for_half)
                y_scale = 2 ** (-pixel_dy / y_pixels_for_half)
                x_center = (x_min + x_max) / 2
                y_center = (y_min + y_max) / 2
                new_x_range = x_range * x_scale
                new_y_range = y_range * y_scale
                min_scale = 0.01
                new_x_range = max(new_x_range, x_range * min_scale)
                new_y_range = max(new_y_range, y_range * min_scale)
                x_min_new = x_center - new_x_range / 2
                x_max_new = x_center + new_x_range / 2
                y_min_new = y_center - new_y_range / 2
                y_max_new = y_center + new_y_range / 2
                ax.set_xlim(x_min_new, x_max_new)
                ax.set_ylim(y_min_new, y_max_new)
                fig.canvas.draw_idle()
                self.update_chart_range_labels()
                canvas.get_tk_widget().config(cursor="sizing")
        canvas.mpl_connect('motion_notify_event', on_motion_pan_zoom)

    def update_chart_range_labels(self):
        """更新当前视图范围内的最高价和最低价标签，完全参照主K线界面实现"""
        try:
            if not hasattr(self, 'klines_df') or self.klines_df is None or self.klines_df.empty:
                return
            if not hasattr(self, 'ax_main'):
                return
            xlim = self.ax_main.get_xlim()
            # 删除旧的高低价标签
            if hasattr(self, 'high_price_line') and self.high_price_line:
                self.high_price_line.remove()
                self.high_price_line = None
            if hasattr(self, 'low_price_line') and self.low_price_line:
                self.low_price_line.remove()
                self.low_price_line = None
            if hasattr(self, 'high_price_label') and self.high_price_label:
                self.high_price_label.remove()
                self.high_price_label = None
            if hasattr(self, 'low_price_label') and self.low_price_label:
                self.low_price_label.remove()
                self.low_price_label = None
            if hasattr(self, 'debug_info_label') and self.debug_info_label:
                self.debug_info_label.remove()
                self.debug_info_label = None
            total_bars = len(self.klines_df)
            start_index = math.ceil(xlim[0])
            end_index = math.floor(xlim[1])
            start_index = max(0, start_index)
            end_index = min(total_bars - 1, end_index)
            if start_index > end_index or start_index < 0 or end_index >= total_bars:
                return
            visible_bars = self.klines_df.iloc[start_index:end_index+1]
            if visible_bars.empty:
                return
            high_price = visible_bars['High'].max()
            low_price = visible_bars['Low'].min()
            symbol = self.chart_symbol_var.get().strip().upper()
            config = SymbolConfig().get_symbol_config(symbol)
            price_precision = config.get('price_precision', 2)
            font_path = 'C:/Windows/Fonts/msyh.ttc'
            import matplotlib.font_manager as fm
            font_prop = fm.FontProperties(fname=font_path)
            x_middle = (xlim[0] + xlim[1]) / 2
            latest_index = len(self.klines_df) - 1
            # 修正：将 index 转为整数再做距离比较
            high_indices = visible_bars.index[visible_bars['High'] == high_price].tolist()
            low_indices = visible_bars.index[visible_bars['Low'] == low_price].tolist()
            if len(high_indices) > 0:
                high_index = min(high_indices, key=lambda idx: abs(self.klines_df.index.get_loc(idx) - latest_index))
                high_pos = self.klines_df.index.get_loc(high_index)
            else:
                high_index = visible_bars['High'].idxmax()
                high_pos = self.klines_df.index.get_loc(high_index)
            if len(low_indices) > 0:
                low_index = min(low_indices, key=lambda idx: abs(self.klines_df.index.get_loc(idx) - latest_index))
                low_pos = self.klines_df.index.get_loc(low_index)
            else:
                low_index = visible_bars['Low'].idxmin()
                low_pos = self.klines_df.index.get_loc(low_index)
            ylim = self.ax_main.get_ylim()
            k_width = 0.5
            arrow_props = dict(
                arrowstyle='-',
                color='gray',
                linewidth=0.8,
                shrinkA=0,
                shrinkB=0
            )
            if high_pos < x_middle:
                high_ha = 'left'
                high_xt = (20, 0)
                high_connect_x = high_pos
                high_position = (high_connect_x, high_price)
            else:
                high_ha = 'right'
                high_xt = (-20, 0)
                high_connect_x = high_pos
                high_position = (high_connect_x, high_price)
            if low_pos < x_middle:
                low_ha = 'left'
                low_xt = (20, 0)
                low_connect_x = low_pos
                low_position = (low_connect_x, low_price)
            else:
                low_ha = 'right'
                low_xt = (-20, 0)
                low_connect_x = low_pos
                low_position = (low_connect_x, low_price)
            self.high_price_label = self.ax_main.annotate(
                f"{high_price:,.{price_precision}f}",
                xy=high_position,
                xytext=high_xt,
                textcoords='offset points',
                color='gray',
                fontproperties=font_prop,
                fontsize=8,
                ha=high_ha,
                va='center',
                arrowprops=arrow_props
            )
            self.low_price_label = self.ax_main.annotate(
                f"{low_price:,.{price_precision}f}",
                xy=low_position,
                xytext=low_xt,
                textcoords='offset points',
                color='gray',
                fontproperties=font_prop,
                fontsize=8,
                ha=low_ha,
                va='center',
                arrowprops=arrow_props
            )
        except Exception as e:
            import traceback, logging
            logging.error(f"更新价格范围标签失败: {str(e)}")
            traceback.print_exc()

    def draw_chart_indicators(self, *args):
        """根据当前K线数据和指标选择绘制指标线，并刷新界面"""
        try:
            # 检查K线图和数据是否已生成
            if not hasattr(self, 'ax_main') or not hasattr(self, 'klines_df') or self.ax_main is None or self.klines_df is None:
                return
            indicator = self.chart_indicator_var.get()
            ax = self.ax_main
            df_plot = self.klines_df
            # 移除旧的指标线
            if hasattr(self, 'chart_indicator_lines') and self.chart_indicator_lines:
                for line in self.chart_indicator_lines:
                    try:
                        line.remove()
                    except Exception:
                        pass
                self.chart_indicator_lines = []
            else:
                self.chart_indicator_lines = []
            # 计算指标
            x_values = np.arange(len(df_plot))
            if indicator == "MA":
                ma5 = df_plot['Close'].rolling(window=5).mean()
                ma10 = df_plot['Close'].rolling(window=10).mean()
                ma20 = df_plot['Close'].rolling(window=20).mean()
                line1 = ax.plot(x_values, ma5, 'b-', linewidth=1.0, alpha=0.8, label='MA5')[0]
                line2 = ax.plot(x_values, ma10, 'y-', linewidth=1.0, alpha=0.8, label='MA10')[0]
                line3 = ax.plot(x_values, ma20, 'm-', linewidth=1.0, alpha=0.8, label='MA20')[0]
                self.chart_indicator_lines.extend([line1, line2, line3])
            elif indicator == "BOLL":
                boll_mid = df_plot['Close'].rolling(window=20).mean()
                boll_std = df_plot['Close'].rolling(window=20).std()
                boll_upper = boll_mid + 2 * boll_std
                boll_lower = boll_mid - 2 * boll_std
                line1 = ax.plot(x_values, boll_upper, 'r-', linewidth=1.0, alpha=0.8, label='BOLL上轨')[0]
                line2 = ax.plot(x_values, boll_mid, 'b-', linewidth=1.0, alpha=0.8, label='BOLL中轨')[0]
                line3 = ax.plot(x_values, boll_lower, 'g-', linewidth=1.0, alpha=0.8, label='BOLL下轨')[0]
                self.chart_indicator_lines.extend([line1, line2, line3])
            # None时不画
            if hasattr(self, 'chart_canvas') and self.chart_canvas:
                self.chart_canvas.draw_idle()
        except Exception as e:
            logging.error(f"K线管理界面指标绘制失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def reset_view(self):
        if hasattr(self, 'ax_main') and hasattr(self, 'chart_canvas'):
            if hasattr(self, '_init_xlim') and hasattr(self, '_init_ylim'):
                self.ax_main.set_xlim(self._init_xlim)
                self.ax_main.set_ylim(self._init_ylim)
                self.chart_canvas.draw_idle()
                if hasattr(self, 'toolbar') and hasattr(self.toolbar, 'status_bar'):
                    self.toolbar.status_bar.config(text="视图已重置")
            else:
                # fallback: autoscale
                self.ax_main.autoscale(True)
                self.ax_main.relim()
                self.chart_canvas.draw_idle()

def get_month_str(dt):
    return dt.strftime('%Y-%m')

def get_month_range(start_dt, end_dt):
    """返回[start_dt, end_dt]之间所有月份的YYYY-MM字符串列表"""
    months = []
    cur = start_dt.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    while cur <= end_dt:
        months.append(cur.strftime('%Y-%m'))
        # 下个月
        year = cur.year + (cur.month // 12)
        month = (cur.month % 12) + 1
        cur = cur.replace(year=year, month=month)
    return months

class TradingApp:
    def __init__(self, root, client):
        self.root = root
        self.client = client
        self.root.title("多功能交易软件")
        # self.root.geometry("1000x800")
        self.current_page = None
        self.pages = {}

        # 优化启动逻辑：先创建不需要网络的基础页面
        self.create_basic_pages()
        # 在后台初始化全局组件，然后创建其余页面
        self.root.after(100, self.initialize_global_components)
        self.root.after(300, self.create_remaining_pages)

    def create_basic_pages(self):
        """创建不需要网络访问的基础页面"""
        self.pages["main"] = MainPage(self.root, self.show_page, self.client)
        self.pages["log"] = LogPage(self.root, self.show_page)
        self.show_page("main")

    def initialize_global_components(self):
        """初始化全局组件：时间管理器和交易对管理器"""
        # 创建全局时间管理器实例
        global time_manager
        time_manager = TimeManager()
        time_manager.start()

        # 创建全局交易对管理器实例并初始化
        global trading_symbols_manager
        trading_symbols_manager = TradingSymbolsManager(self.client)
        logging.info("正在初始化交易对信息...")
        trading_symbols_manager.refresh_symbols()

        # 启动线程监控
        threading.Thread(target=print_thread_info, name="ThreadMonitor", daemon=True).start()

    def create_remaining_pages(self):
        """创建需要全局组件的页面"""
        self.pages["orders"] = OrdersPage(self.root, self.show_page, self.client)
        self.show_page("main")
        self.pages["positions"] = PositionsPage(self.root, self.show_page, self.client)
        self.show_page("main")
        self.pages["kline"] = KlinePage(self.root, self.show_page, self.client, default_symbol="BTCUSDT")
        self.show_page("main")
        self.pages["kline_data"] = KlineDataManagerPage(self.root, self.show_page, self.client)
        self.show_page("main")
        
    def show_page(self, page_name):
        # 检查页面是否存在，如果不存在且不是基础页面，则等待初始化完成
        if page_name not in self.pages:
            if page_name in ["main", "log"]:
                # 基础页面应该已经创建，如果没有则有问题
                logging.error(f"基础页面 {page_name} 未找到")
                return
            else:
                # 其他页面可能还在初始化中，延迟执行
                logging.info(f"页面 {page_name} 还未创建，等待初始化完成...")
                self.root.after(500, lambda: self.show_page(page_name))
                return

        # 获取所有页面
        main_page = self.pages.get("main")
        kline_page = self.pages.get("kline")
        orders_page = self.pages.get("orders")
        positions_page = self.pages.get("positions")
        # 先全部停止
        if kline_page:
            kline_page.stop_price_and_countdown_refresh()
            kline_page.stop_kline_data_update()
            kline_page.stop_balance_update()
        if orders_page:
            orders_page.stop_orders_update()
        if positions_page:
            positions_page.stop_positions_update()
        # 再根据页面启动
        if page_name == "main":
            # 启动时间更新
            if main_page and hasattr(main_page, "time_label"):
                start_time_update(main_page.time_label)
        elif page_name == "kline":
            if kline_page:
                
                # 第一次打开时自动修复图表显示问题
                if hasattr(kline_page, 'first_time_opened') and kline_page.first_time_opened:
                    # 延迟执行，确保图表完全加载
                    kline_page.after(100, kline_page._auto_fix_chart_display)
                    kline_page.first_time_opened = False
                
                kline_page.start_price_and_countdown_refresh()
                kline_page.toggle_kline_update()
                kline_page.start_balance_update()
                kline_page.update_layout
        elif page_name == "orders":
            if orders_page:
                orders_page.start_orders_update()
        elif page_name == "positions":
            if positions_page:
                positions_page.start_positions_update()

        # 切换页面
        for page in self.pages.values():
            page.pack_forget()
        self.current_page = self.pages.get(page_name)
        if self.current_page:
            self.current_page.pack(fill=BOTH, expand=True)

def sync_time_with_ntplib(client=None):
    """使用ntplib同步系统时间，并更新客户端时间偏移"""
    try:
        # 创建NTP客户端
        ntp_client = ntplib.NTPClient()
        # 从国内NTP服务器池获取时间
        response = ntp_client.request('pool.ntp.org')
        # 获取NTP时间戳
        ntp_time = response.tx_time
        
        # 转换为本地日期和时间字符串
        date_str = time.strftime('%Y-%m-%d', time.localtime(ntp_time))
        time_str = time.strftime('%H:%M:%S', time.localtime(ntp_time))
        
        # 使用系统命令设置日期和时间（适用于Windows）
        os.system(f'date {date_str.replace("-", "/")}')  # Windows使用/分隔日期
        os.system(f'time {time_str}')
        
        logging.info(f"使用ntplib同步系统时间成功：{date_str} {time_str}")
        
        # 如果提供了API客户端，也同步交易所时间
        if client:
            server_time = int(client.time()["serverTime"])
            local_time = int(time.time() * 1000)
            offset = server_time - local_time
            client.timestamp_offset = offset
            logging.info(f"同步交易所时间：服务器时间：{server_time}，本地时间：{local_time}，时间偏移：{offset}ms")
        
        return True
    except ImportError:
        error_msg = "未安装ntplib库，请使用pip安装：pip install ntplib"
        logging.error(error_msg)
        return False
    except Exception as e:
        error_msg = f"使用ntplib同步时间出错：{str(e)}"
        logging.error(error_msg)
        return False

def print_thread_info():
    """定期打印当前活动的线程信息"""
    while True:
        print("\n=== 当前活动线程信息 ===")
        for thread in threading.enumerate():
            print(f"线程名: {thread.name}, ID: {thread.ident}, 活动状态: {thread.is_alive()}")
        print("=======================\n")
        time.sleep(10)  # 每分钟打印一次

def main():
    # 开启进程DPI感知，获取Windows系统的显示缩放系数，然后缩放字体和界面

    # 设置Windows缩放系统API，启用dpi感知
    try:
        ctypes.windll.shcore.SetProcessDpiAwareness(1)
    except:
        pass

    # 获取Windows当前的显示缩放系数
    try:
        scale_factor = ctypes.windll.shcore.GetScaleFactorForDevice(0) / 100
    except:
        scale_factor = 1.0

    # 创建主窗口
    root = Tk()

    # 缩放字体
    # default_font = tkfont.nametofont("TkDefaultFont")
    # font_size = math.ceil(10 * scale_factor)
    # default_font.configure(family='Microsoft YaHei', size=font_size)
    # root.option_add('*Font', default_font)

    # 缩放界面
    window_width = int(800 * scale_factor)
    window_height = int(640 * scale_factor)

    # 获取屏幕尺寸
    screen_width = root.winfo_screenwidth() * scale_factor
    screen_height = root.winfo_screenheight() * scale_factor

    # 计算居中的坐标位置
    x = (screen_width - window_width) // 2
    y = (screen_height - window_height) // 2

    # 设置窗口大小和位置
    root.geometry(f"{window_width}x{window_height}+{int(x)}+{int(y)}")

    # 加载环境变量，并初始化 API 客户端
    load_dotenv('1.env')
    API_KEY = os.getenv('API_KEY')
    SECRET_KEY = os.getenv('SECRET_KEY')

    um_futures_client = UMFutures(key=API_KEY, secret=SECRET_KEY)

    # 设置窗口图标和任务栏图标
    try:
        # 设置窗口图标（左上角）
        root.iconbitmap(default='')  # 使用默认Tkinter图标
        # 设置任务栏图标
        root.wm_iconbitmap(default='')  # 确保任务栏也使用正确图标
    except:
        pass

    # 在关闭应用时清理资源
    def on_closing():
        """应用关闭时的清理工作"""
        try:
            # 停止时间管理器
            if 'time_manager' in globals():
                time_manager.stop()
                
        except Exception as e:
            logging.error(f"应用关闭时清理失败: {str(e)}")
            traceback.print_exc()
            
        # 销毁主窗口
        root.destroy()
    
    # 设置关闭窗口的回调
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    app = TradingApp(root, um_futures_client)
    root.mainloop()

if __name__ == '__main__':
    main()



