import ccxt
from dotenv import load_dotenv
import os

# 加载密钥
load_dotenv('1.env')
API_KEY = os.getenv('API_KEY')
SECRET_KEY = os.getenv('SECRET_KEY')

# 初始化交易所
exchange = ccxt.binance({
    'apiKey': API_KEY,
    'secret': SECRET_KEY,
    'enableRateLimit': True,  # 遵守速率限制
    # 'options': {'adjustForTimeDifference': True}  # 可选的时差校准
    'proxies': {  # 如需代理 ↓
        'http': 'http://localhost:7890',
        'https': 'http://localhost:7890'}
})

try:
    # 创建限价买单
    order = exchange.create_order(
        symbol='BTCUSDT',   # 交易对
        type='limit',        # 市价单用 'market'
        side='buy',          # 买单
        amount=0.002,        # 购买数量
        price=30000,         # 限价单需要价格（市价单可省略）
        params={
            # 'clientOrderId': 'CUSTOM_ID_001',  # 自定义订单ID
            # 'test': True                      # 测试单模式
        }

    )
    print(f"订单创建成功：{order}")

except ccxt.InsufficientFunds as e:
    print(f"保证金不足: {str(e)}")
except ccxt.NetworkError as e:
    print(f"网络错误: {str(e)}")
except ccxt.ExchangeError as e:
    print(f"交易所错误: {str(e)}")
except Exception as e:
    print(f"未知错误: {str(e)}")


