#!/usr/bin/env python3
"""
测试独立窗口解决方案
将对话框设置为独立的任务栏窗口，类似Windows文件夹属性窗口
"""

import tkinter as tk
from tkinter import ttk, StringVar, IntVar, Frame, Label, Entry, Button, Toplevel, LEFT, RIGHT, X, BOTH, W

class TestIndependentWindows:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("主程序 - 独立窗口测试")
        self.root.geometry("600x400")
        
        # 用于跟踪独立窗口
        self._martin_dialog = None
        self._test_dialog = None
        self._config_dialog = None
        
        self.create_ui()
        
    def create_ui(self):
        main_frame = Frame(self.root)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        Label(main_frame, text="独立窗口解决方案测试", font=("微软雅黑", 16, "bold")).pack(pady=20)
        
        Label(main_frame, text="测试说明：", font=("微软雅黑", 12, "bold")).pack(anchor=W, pady=(20, 5))
        Label(main_frame, text="• 每个对话框都是独立的任务栏窗口", font=("微软雅黑", 10)).pack(anchor=W, padx=20)
        Label(main_frame, text="• 可以单独点击任务栏图标切换", font=("微软雅黑", 10)).pack(anchor=W, padx=20)
        Label(main_frame, text="• 不会阻塞主程序的操作", font=("微软雅黑", 10)).pack(anchor=W, padx=20)
        Label(main_frame, text="• 类似Windows文件夹属性窗口的行为", font=("微软雅黑", 10)).pack(anchor=W, padx=20)
        
        # 测试步骤
        Label(main_frame, text="测试步骤：", font=("微软雅黑", 12, "bold")).pack(anchor=W, pady=(20, 5))
        Label(main_frame, text="1. 点击下面的按钮打开独立窗口", font=("微软雅黑", 10)).pack(anchor=W, padx=20)
        Label(main_frame, text="2. 观察任务栏是否出现新的窗口图标", font=("微软雅黑", 10)).pack(anchor=W, padx=20)
        Label(main_frame, text="3. 点击'显示桌面'最小化所有窗口", font=("微软雅黑", 10)).pack(anchor=W, padx=20)
        Label(main_frame, text="4. 分别点击任务栏中的不同图标", font=("微软雅黑", 10)).pack(anchor=W, padx=20)
        Label(main_frame, text="5. 检查是否可以独立控制每个窗口", font=("微软雅黑", 10)).pack(anchor=W, padx=20)
        
        # 测试按钮
        button_frame = Frame(main_frame)
        button_frame.pack(pady=30)
        
        Button(button_frame, text="打开马丁策略窗口", 
               command=self.open_martin_window,
               font=("微软雅黑", 12),
               bg="#4CAF50", fg="white",
               width=20, height=2).pack(pady=5)
        
        Button(button_frame, text="打开测试参数窗口", 
               command=self.open_test_window,
               font=("微软雅黑", 12),
               bg="#2196F3", fg="white",
               width=20, height=2).pack(pady=5)
        
        Button(button_frame, text="打开配置窗口", 
               command=self.open_config_window,
               font=("微软雅黑", 12),
               bg="#FF9800", fg="white",
               width=20, height=2).pack(pady=5)
        
        # 状态显示
        self.status_frame = Frame(main_frame)
        self.status_frame.pack(pady=20)
        
        self.status_label = Label(self.status_frame, text="窗口状态：", font=("微软雅黑", 10, "bold"))
        self.status_label.pack()
        
        # 定时更新状态
        self.update_status()
        
    def update_status(self):
        """更新窗口状态显示"""
        status_text = "窗口状态：\n"
        
        # 检查马丁策略窗口
        if self._martin_dialog and self._martin_dialog.winfo_exists():
            status_text += "✅ 马丁策略窗口已打开\n"
        else:
            status_text += "❌ 马丁策略窗口已关闭\n"
            
        # 检查测试窗口
        if self._test_dialog and self._test_dialog.winfo_exists():
            status_text += "✅ 测试参数窗口已打开\n"
        else:
            status_text += "❌ 测试参数窗口已关闭\n"
            
        # 检查配置窗口
        if self._config_dialog and self._config_dialog.winfo_exists():
            status_text += "✅ 配置窗口已打开\n"
        else:
            status_text += "❌ 配置窗口已关闭\n"
            
        self.status_label.config(text=status_text)
        self.root.after(1000, self.update_status)
        
    def center_window_on_screen(self, window):
        """将窗口居中显示在屏幕中央"""
        window.update_idletasks()
        
        # 获取屏幕尺寸
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        # 获取窗口尺寸
        window_width = window.winfo_reqwidth()
        window_height = window.winfo_reqheight()
        
        # 计算居中位置
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        # 设置窗口位置
        window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
    def open_martin_window(self):
        """打开马丁策略独立窗口"""
        # 检查是否已经有窗口打开
        if self._martin_dialog and self._martin_dialog.winfo_exists():
            self._martin_dialog.lift()
            self._martin_dialog.focus_set()
            return
            
        # 创建独立窗口
        window = Toplevel()  # 不传入parent
        window.title("马丁策略参数设置")
        window.geometry("500x600")
        
        # 设置为独立的任务栏窗口
        try:
            window.iconbitmap(default='')
        except:
            pass
        window.attributes('-topmost', False)
        window.resizable(True, True)
        
        # 保存引用
        self._martin_dialog = window
        
        # 设置关闭回调
        def on_close():
            self._martin_dialog = None
            window.destroy()
        window.protocol("WM_DELETE_WINDOW", on_close)
        
        # 居中显示
        self.center_window_on_screen(window)
        
        # 创建内容
        main_frame = Frame(window)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        Label(main_frame, text="马丁策略参数设置", font=("微软雅黑", 16, "bold"), fg="green").pack(pady=20)
        Label(main_frame, text="这是一个独立的任务栏窗口", font=("微软雅黑", 12)).pack(pady=10)
        Label(main_frame, text="可以在任务栏中单独点击切换", font=("微软雅黑", 12)).pack(pady=5)
        
        # 添加一些参数输入框
        for i in range(8):
            frame = Frame(main_frame)
            frame.pack(fill=X, pady=5)
            Label(frame, text=f"参数 {i+1}:", width=10, anchor=W).pack(side=LEFT)
            Entry(frame, width=20).pack(side=LEFT, padx=10)
        
        # 按钮
        button_frame = Frame(main_frame)
        button_frame.pack(pady=20)
        Button(button_frame, text="确定", command=on_close, 
               bg="#4CAF50", fg="white", width=10).pack(side=LEFT, padx=5)
        Button(button_frame, text="取消", command=on_close, 
               bg="#f44336", fg="white", width=10).pack(side=LEFT, padx=5)
        
    def open_test_window(self):
        """打开测试参数独立窗口"""
        if self._test_dialog and self._test_dialog.winfo_exists():
            self._test_dialog.lift()
            self._test_dialog.focus_set()
            return
            
        window = Toplevel()
        window.title("测试参数设置")
        window.geometry("350x200")
        
        try:
            window.iconbitmap(default='')
        except:
            pass
        window.attributes('-topmost', False)
        window.resizable(True, True)
        
        self._test_dialog = window
        
        def on_close():
            self._test_dialog = None
            window.destroy()
        window.protocol("WM_DELETE_WINDOW", on_close)
        
        self.center_window_on_screen(window)
        
        # 创建内容
        Label(window, text="测试参数设置", font=("微软雅黑", 14, "bold"), fg="blue").pack(pady=20)
        Label(window, text="请输入反弹/回调百分比（如5表示5%）:").pack(pady=10)
        
        entry_frame = Frame(window)
        entry_frame.pack(pady=10)
        percent_var = StringVar(value="5")
        entry = Entry(entry_frame, textvariable=percent_var, width=10)
        entry.pack(side=LEFT, padx=5)
        Label(entry_frame, text="%").pack(side=LEFT)
        
        Button(window, text="开始测试", command=on_close, 
               bg="#2196F3", fg="white", width=15).pack(pady=20)
        
    def open_config_window(self):
        """打开配置独立窗口"""
        if self._config_dialog and self._config_dialog.winfo_exists():
            self._config_dialog.lift()
            self._config_dialog.focus_set()
            return
            
        window = Toplevel()
        window.title("交易配置")
        window.geometry("400x300")
        
        try:
            window.iconbitmap(default='')
        except:
            pass
        window.attributes('-topmost', False)
        window.resizable(True, True)
        
        self._config_dialog = window
        
        def on_close():
            self._config_dialog = None
            window.destroy()
        window.protocol("WM_DELETE_WINDOW", on_close)
        
        self.center_window_on_screen(window)
        
        # 创建内容
        Label(window, text="交易配置", font=("微软雅黑", 14, "bold"), fg="orange").pack(pady=20)
        Label(window, text="这是一个独立的配置窗口", font=("微软雅黑", 12)).pack(pady=10)
        
        # 配置选项
        config_frame = Frame(window)
        config_frame.pack(pady=20)
        
        Label(config_frame, text="杠杆倍数:").pack(anchor=W)
        leverage_var = StringVar(value="10")
        Entry(config_frame, textvariable=leverage_var, width=10).pack(pady=5)
        
        Label(config_frame, text="保证金模式:").pack(anchor=W, pady=(10, 0))
        margin_var = StringVar(value="全仓")
        ttk.Combobox(config_frame, textvariable=margin_var, 
                     values=["全仓", "逐仓"], width=10).pack(pady=5)
        
        Button(window, text="保存配置", command=on_close, 
               bg="#FF9800", fg="white", width=15).pack(pady=20)
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = TestIndependentWindows()
    app.run()
