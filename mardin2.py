import sys
import time
import os
import json
import threading
import traceback
import logging
from datetime import datetime
from decimal import Decimal, ROUND_HALF_EVEN
from tkinter import Tk, Frame, Button, Label, Text, END, Scrollbar, VERTICAL, RIGHT, Y, LEFT, BOTH
from binance.um_futures import UMFutures
from dotenv import load_dotenv

# ===================== 参数配置 ======================
SYMBOL = 'BTCUSDT'               # 交易对
DEFAULT_INIT_QUANTITY = 0.002    # 默认初始下单数量
DEFAULT_INIT_PRICE = 60000       # 默认初始下单价格
MARTINGALE_MULTIPLIER = 1.2      # 马丁加倍因子
MAX_ATTEMPTS = 5               # 最多尝试挂单次数
ORDER_WAIT_TIME = 60           # 单个订单挂单等待成交时间（秒）
PROFIT_PERCENT = 0.05          # 平仓目标（成交价格上浮 5%）
LOOP_DELAY = 5                 # 每次循环延时（秒）

# ===================== 文件目录与初始化 ======================
ROOT_DIR = os.getcwd()
CONFIG_FILE = os.path.join(ROOT_DIR, 'config.json')
CURRENT_ORDERS_FILE = os.path.join(ROOT_DIR, 'current_orders.json')
POSITIONS_FILE = os.path.join(ROOT_DIR, 'positions.json')
HISTORY_ORDERS_FILE = os.path.join(ROOT_DIR, 'history_orders.json')

def init_json_file(file_path, default_data):
    """如果文件不存在或无法解析，则初始化为默认数据"""
    try:
        with open(file_path, 'r') as f:
            json.load(f)
    except Exception:
        with open(file_path, 'w') as f:
            json.dump(default_data, f, indent=4, ensure_ascii=False)

# 默认配置（可手动修改）
default_config = {
    "enable_strategy": True,
    "preset_orders": [  # 预设多组订单，下单时一键下单多个
        {"price": 60000, "quantity": 0.002},
        {"price": 55000, "quantity": 0.004},
        {"price": 50000, "quantity": 0.008},
        {"price": 45000, "quantity": 0.016}
    ]
}

init_json_file(CONFIG_FILE, default_config)
init_json_file(CURRENT_ORDERS_FILE, {})        # 当前挂单记录
init_json_file(POSITIONS_FILE, {})             # 当前仓位（可扩展保存具体数据）
init_json_file(HISTORY_ORDERS_FILE, [])        # 历史订单记录

# ===================== 日志设置 ======================
logger = logging.getLogger()
logger.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler = logging.FileHandler('martingale_log.txt', mode='a')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

# 同时设置 StreamHandler 输出到控制台
stream_handler = logging.StreamHandler(sys.stdout)
stream_handler.setFormatter(formatter)
logger.addHandler(stream_handler)

# ===================== API 相关函数 ======================
def sync_time(client):
    """同步服务器时间，设置客户端时间偏移"""
    try:
        server_time = int(client.time()["serverTime"])
        local_time = int(time.time() * 1000)
        offset = server_time - local_time
        client.timestamp_offset = offset
        logging.info(f"服务器时间：{server_time}，本地时间：{local_time}，时间偏移：{offset}ms")
    except Exception as e:
        logging.error(f"同步时间出错：{e}")

def place_order(client, symbol, side, quantity, price, extra_params=None):
    """下限价订单（开仓）"""
    params = {
        'symbol': symbol,
        'side': side,
        'type': 'LIMIT',
        'timeInForce': 'GTC',
        'quantity': quantity,
        'price': price
    }
    if extra_params:
        params.update(extra_params)
    try:
        result = client.new_order(**params)
        logging.info(f"下单成功：{params}，返回：{result}")
        # 将挂单保存到本地 JSON
        order_id = result.get('orderId')
        update_current_orders(order_id, params, result)
        return order_id
    except Exception as e:
        logging.error("下单错误：" + str(e))
        return None

def monitor_order(client, symbol, order_id, wait_time=ORDER_WAIT_TIME):
    """轮询检测订单是否成交"""
    start_time = time.time()
    while time.time() - start_time < wait_time:
        try:
            order_info = client.query_order(symbol=symbol, orderId=order_id)
            status = order_info.get('status', '')
            logging.info(f"订单 {order_id} 状态：{status}")
            if status == 'FILLED':
                update_history_orders(order_info)
                return order_info
        except Exception as e:
            logging.error("查询订单状态出错：" + str(e))
        time.sleep(1)
    logging.info(f"订单 {order_id} 在 {wait_time} 秒内未成交")
    return None

def cancel_order(client, symbol, order_id):
    """取消未成交订单，并更新本地记录"""
    try:
        result = client.cancel_order(symbol=symbol, orderId=order_id)
        logging.info(f"取消订单 {order_id} 成功：{result}")
        remove_current_order(order_id)
    except Exception as e:
        logging.error("取消订单出错：" + str(e))

def place_close_order(client, symbol, side, quantity, close_price):
    """下平仓单（反向下单实现平仓）"""
    params = {
        'symbol': symbol,
        'side': side,
        'type': 'LIMIT',
        'timeInForce': 'GTC',
        'quantity': quantity,
        'price': close_price
    }
    try:
        result = client.new_order(**params)
        logging.info(f"平仓单下单成功：{params}，返回：{result}")
        update_history_orders(result)
        return result
    except Exception as e:
        logging.error("平仓单下单出错：" + str(e))
        return None

# ===================== 本地数据读写函数 ======================
def update_current_orders(order_id, order_params, result):
    """将新挂单记录写入 current_orders.json"""
    try:
        with open(CURRENT_ORDERS_FILE, 'r') as f:
            orders = json.load(f)
    except Exception:
        orders = {}
    orders[str(order_id)] = {
        "order_params": order_params,
        "result": result,
        "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    with open(CURRENT_ORDERS_FILE, 'w') as f:
        json.dump(orders, f, indent=4, ensure_ascii=False)

def remove_current_order(order_id):
    """将已经取消或成交的订单从 current_orders.json 移除"""
    try:
        with open(CURRENT_ORDERS_FILE, 'r') as f:
            orders = json.load(f)
    except Exception:
        orders = {}
    orders.pop(str(order_id), None)
    with open(CURRENT_ORDERS_FILE, 'w') as f:
        json.dump(orders, f, indent=4, ensure_ascii=False)

def update_history_orders(order_info):
    """将成交订单追加到历史订单记录中"""
    try:
        with open(HISTORY_ORDERS_FILE, 'r') as f:
            history = json.load(f)
    except Exception:
        history = []
    history.append({
        "order_info": order_info,
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    })
    with open(HISTORY_ORDERS_FILE, 'w') as f:
        json.dump(history, f, indent=4, ensure_ascii=False)

def load_config():
    """读取最新的配置文件"""
    try:
        with open(CONFIG_FILE, 'r') as f:
            return json.load(f)
    except Exception:
        return default_config

# ===================== 策略核心 ======================
def martingale_strategy(client, preset_order):
    """
    按照指定预设订单下单：
      preset_order 为 dict 包含 price、quantity 两个键；
      如果订单未成交则尝试多次(马丁加倍后修改)
    """
    attempt = 1
    quantity = preset_order.get("quantity", DEFAULT_INIT_QUANTITY)
    price = preset_order.get("price", DEFAULT_INIT_PRICE)
    order_filled = None

    while attempt <= MAX_ATTEMPTS and order_filled is None:
        logging.info(f"预设订单 {preset_order} 第 {attempt} 次挂单：数量={quantity}, 价格={price}")
        # 这里假设开多，下单时传入 positionSide 参数（根据账户模式可调整）
        order_id = place_order(client, SYMBOL, side='BUY', quantity=quantity, price=price, extra_params={'positionSide': 'LONG'})
        if order_id is None:
            logging.error("下单失败，尝试下一次")
            attempt += 1
            continue

        order_info = monitor_order(client, SYMBOL, order_id)
        if order_info:
            logging.info(f"订单成交：{order_info}")
            order_filled = order_info
            break
        else:
            cancel_order(client, SYMBOL, order_id)
            # 马丁格尔：修改数量和价格（如：加倍数量，价格降低0.5%）
            quantity *= MARTINGALE_MULTIPLIER
            price = round(price * 0.995, 2)
            logging.info(f"订单未成交，更新参数：新数量={quantity}, 新价格={price}")
            attempt += 1

    if order_filled:
        filled_price = float(order_filled.get('avgPrice', price))
        target_price = round(filled_price * (1 + PROFIT_PERCENT), 2)
        logging.info(f"成交价为 {filled_price}，目标平仓价：{target_price}")
        close_result = place_close_order(client, SYMBOL, side='SELL', quantity=order_filled.get('origQty', quantity), close_price=target_price)
        return order_filled, close_result
    else:
        logging.info("多次尝试后订单均未成交")
        return None, None

# ===================== 主循环与线程 ======================
def main_loop(um_futures_client):
    """主循环：读取配置文件、执行策略；始终循环，即使报错也继续运行"""
    sync_time(um_futures_client)
    while True:
        try:
            config = load_config()
            if config.get("enable_strategy", False):
                # 对每个预设订单均执行一次马丁策略，注意你可以按自己的需求组合多组下单
                for preset in config.get("preset_orders", []):
                    logging.info(f"开始执行预设策略：{preset}")
                    order_filled, close_result = martingale_strategy(um_futures_client, preset)
                    # 这里你可以将成交信息写入到 POSITIONS_FILE 或其他地方
                    time.sleep(5)
            else:
                logging.info("策略开关未开启，暂停自动下单")
            time.sleep(LOOP_DELAY)
        except Exception as e:
            logging.error("主循环发生错误：" + traceback.format_exc())
            time.sleep(5)
            continue

# ===================== 图形界面 ======================
class TradingApp:
    def __init__(self, um_futures_client):
        self.um_futures_client = um_futures_client
        self.root = Tk()
        self.root.title("多功能交易软件")
        self.root.geometry("800x600")
        self.create_widgets()
        # 在独立线程中运行主策略循环
        threading.Thread(target=main_loop, args=(um_futures_client,), daemon=True).start()

    def create_widgets(self):
        frame = Frame(self.root)
        frame.pack(fill=BOTH, expand=True)

        # 日志显示文本区域
        self.log_text = Text(frame, wrap='word')
        self.log_text.pack(side=LEFT, fill=BOTH, expand=True)

        scrollbar = Scrollbar(frame, orient=VERTICAL, command=self.log_text.yview)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.log_text.config(yscrollcommand=scrollbar.set)

        # 操作按钮
        btn_frame = Frame(self.root)
        btn_frame.pack(fill="x")
        Button(btn_frame, text="取消所有挂单", command=self.cancel_all_orders).pack(side="left", padx=5, pady=5)
        Button(btn_frame, text="手动开仓", command=self.manual_open).pack(side="left", padx=5, pady=5)
        Button(btn_frame, text="手动平仓", command=self.manual_close).pack(side="left", padx=5, pady=5)
        Button(btn_frame, text="刷新配置", command=self.refresh_config).pack(side="left", padx=5, pady=5)

        # 定时刷新日志区域（把日志文件内容加载到界面）
        self.refresh_log()

    def refresh_log(self):
        try:
            with open('martingale_log.txt', 'r') as f:
                data = f.read()
            self.log_text.delete("1.0", END)
            self.log_text.insert(END, data)
        except Exception as e:
            print("刷新日志出错:", e)
        self.root.after(3000, self.refresh_log)

    def cancel_all_orders(self):
        """调用接口取消所有挂单"""
        try:
            result = self.um_futures_client.cancel_open_orders(symbol=SYMBOL)
            logging.info(f"取消所有挂单结果：{result}")
        except Exception as e:
            logging.error("取消所有挂单时出错：" + str(e))

    def manual_open(self):
        """手动下单开仓（示例功能：以默认配置下单，可扩展为弹窗获取参数）"""
        try:
            order_id = place_order(self.um_futures_client, SYMBOL, side='BUY', quantity=DEFAULT_INIT_QUANTITY, price=DEFAULT_INIT_PRICE, extra_params={'positionSide': 'LONG'})
            logging.info(f"手动开仓，下单返回 orderId={order_id}")
        except Exception as e:
            logging.error("手动开仓时出错：" + str(e))

    def manual_close(self):
        """手动平仓（示例功能：以默认参数平仓，可扩展为弹窗获取参数）"""
        try:
            # 这里简单以默认价格平仓，实际应根据持仓情况调整参数
            result = place_close_order(self.um_futures_client, SYMBOL, side='SELL', quantity=DEFAULT_INIT_QUANTITY, close_price=round(DEFAULT_INIT_PRICE*(1+PROFIT_PERCENT),2))
            logging.info(f"手动平仓，下单返回：{result}")
        except Exception as e:
            logging.error("手动平仓时出错：" + str(e))

    def refresh_config(self):
        """手动刷新配置（例如从 config.json 读取最新策略配置）"""
        config = load_config()
        logging.info(f"刷新配置：{config}")

    def run(self):
        self.root.mainloop()

# ===================== 主程序入口 ======================
def main():
    load_dotenv('1.env')
    API_KEY = os.getenv('API_KEY')
    SECRET_KEY = os.getenv('SECRET_KEY')
    um_futures_client = UMFutures(key=API_KEY, secret=SECRET_KEY)
    # 启动图形界面，同时策略主循环在后台线程运行
    app = TradingApp(um_futures_client)
    app.run()

if __name__ == '__main__':
    main()
