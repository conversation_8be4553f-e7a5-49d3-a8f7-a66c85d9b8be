#!/usr/bin/env python3
"""
测试对话框居中功能的简单脚本
"""

import tkinter as tk
from tkinter import ttk, StringVar, IntVar, Frame, Label, Entry, Button, Toplevel, LEFT, RIGHT, X, BOTH, W

class TestCenterDialog:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("测试对话框居中")
        self.root.geometry("800x600")
        
        # 创建测试按钮
        btn_frame = Frame(self.root)
        btn_frame.pack(pady=50)
        
        But<PERSON>(btn_frame, text="测试小对话框 (300x120)", 
               command=lambda: self.test_dialog(300, 120)).pack(pady=10)
        Button(btn_frame, text="测试大对话框 (500x600)", 
               command=lambda: self.test_dialog(500, 600)).pack(pady=10)
        
    def center_dialog(self, dialog):
        """将对话框居中显示在主窗口中间"""
        dialog.update_idletasks()  # 确保窗口尺寸已计算
        
        # 获取主窗口的位置和尺寸
        main_x = self.root.winfo_rootx()
        main_y = self.root.winfo_rooty()
        main_width = self.root.winfo_width()
        main_height = self.root.winfo_height()
        
        # 从geometry字符串中解析对话框的设置尺寸
        geometry = dialog.geometry()
        print(f"对话框geometry: {geometry}")
        
        if 'x' in geometry and '+' in geometry:
            # 解析 "widthxheight+x+y" 格式
            size_part = geometry.split('+')[0]
            if 'x' in size_part:
                dialog_width, dialog_height = map(int, size_part.split('x'))
                print(f"解析得到尺寸: {dialog_width}x{dialog_height}")
            else:
                # 如果解析失败，使用请求的尺寸作为备选
                dialog_width = dialog.winfo_reqwidth()
                dialog_height = dialog.winfo_reqheight()
                print(f"解析失败，使用请求尺寸: {dialog_width}x{dialog_height}")
        else:
            # 如果解析失败，使用请求的尺寸作为备选
            dialog_width = dialog.winfo_reqwidth()
            dialog_height = dialog.winfo_reqheight()
            print(f"geometry格式不正确，使用请求尺寸: {dialog_width}x{dialog_height}")
        
        # 计算居中位置
        x = main_x + (main_width - dialog_width) // 2
        y = main_y + (main_height - dialog_height) // 2
        
        print(f"主窗口位置: {main_x}, {main_y}, 尺寸: {main_width}x{main_height}")
        print(f"计算的居中位置: {x}, {y}")
        
        # 设置对话框位置，保持原有尺寸
        final_geometry = f"{dialog_width}x{dialog_height}+{x}+{y}"
        print(f"最终设置geometry: {final_geometry}")
        dialog.geometry(final_geometry)
        
    def test_dialog(self, width, height):
        """创建测试对话框"""
        dialog = Toplevel(self.root)
        dialog.title(f"测试对话框 {width}x{height}")
        dialog.geometry(f"{width}x{height}")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 添加一些内容
        Label(dialog, text=f"这是一个 {width}x{height} 的对话框", 
              font=("微软雅黑", 12)).pack(pady=20)
        Label(dialog, text="测试居中功能是否正常工作").pack(pady=10)
        
        Button(dialog, text="关闭", command=dialog.destroy).pack(pady=20)
        
        # 将窗口居中显示在主窗口中间
        self.center_dialog(dialog)
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = TestCenterDialog()
    app.run()
