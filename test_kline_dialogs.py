#!/usr/bin/env python3
"""
测试K线界面对话框的独立窗口修复
"""

import tkinter as tk
from tkinter import ttk, StringVar, IntVar, Frame, Label, Entry, Button, Toplevel, LEFT, RIGHT, X, BOTH, W
import threading
import time

class MockClient:
    """模拟客户端，用于测试"""
    def change_margin_type(self, **kwargs):
        time.sleep(0.5)  # 模拟网络延迟
        return True
        
    def change_leverage(self, **kwargs):
        time.sleep(0.5)  # 模拟网络延迟
        return True

class TestKlineDialogs:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("K线界面对话框测试")
        self.root.geometry("700x500")
        
        # 模拟KlinePage的属性
        self.client = MockClient()
        self.current_margin_type = "CROSSED"
        self.current_leverage = 10
        self.current_valid_symbol = "BTCUSDT"
        
        # 用于跟踪独立窗口
        self._margin_dialog = None
        self._leverage_dialog = None
        self._config_dialog = None
        
        # 模拟后台任务
        self.counter = 0
        self.is_running = True
        
        self.create_ui()
        self.start_background_task()
        
    def create_ui(self):
        main_frame = Frame(self.root, bg='#f5f5f5')
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        Label(main_frame, text="🔧 K线界面对话框修复测试", 
              font=("微软雅黑", 18, "bold"), bg='#f5f5f5', fg='#2c3e50').pack(pady=(0, 20))
        
        # 后台任务状态
        self.status_frame = Frame(main_frame, bg='#ecf0f1', relief='raised', bd=1)
        self.status_frame.pack(fill=X, pady=(0, 20))
        
        Label(self.status_frame, text="📊 后台任务状态（模拟K线更新）", 
              font=("微软雅黑", 12, "bold"), bg='#ecf0f1').pack(pady=(10, 5))
        
        self.counter_label = Label(self.status_frame, text="计数器: 0", 
                                  font=("微软雅黑", 14), bg='#ecf0f1', fg='#27ae60')
        self.counter_label.pack(pady=(0, 10))
        
        # 测试说明
        info_frame = Frame(main_frame, bg='white', relief='raised', bd=1)
        info_frame.pack(fill=X, pady=(0, 20))
        
        Label(info_frame, text="✅ 修复验证项目：", 
              font=("微软雅黑", 12, "bold"), bg='white', fg='#e74c3c').pack(anchor=W, padx=15, pady=(10, 5))
        
        checks = [
            "• 对话框是独立的任务栏窗口",
            "• 主程序在对话框打开时不可操控",
            "• 后台任务继续运行（K线更新等）",
            "• 可以通过任务栏单独切换窗口",
            "• 解决'显示桌面'问题"
        ]
        
        for check in checks:
            Label(info_frame, text=check, font=("微软雅黑", 10), 
                  bg='white', fg='#2c3e50').pack(anchor=W, padx=30, pady=1)
        
        Label(info_frame, text="", bg='white').pack(pady=5)
        
        # 测试按钮
        button_frame = Frame(main_frame, bg='#f5f5f5')
        button_frame.pack(fill=X, pady=20)
        
        buttons = [
            ("切换保证金模式", self.change_margin_mode, "#9b59b6"),
            ("调整杠杆倍数", self.change_leverage, "#e67e22"),
            ("显示交易配置", self.display_symbol_config, "#f39c12")
        ]
        
        for text, command, color in buttons:
            btn = Button(button_frame, text=text, command=command,
                        font=("微软雅黑", 11, "bold"), bg=color, fg="white",
                        width=20, height=2, relief='flat')
            btn.pack(pady=5)
        
    def start_background_task(self):
        """启动后台任务，模拟K线更新"""
        def background_worker():
            while self.is_running:
                try:
                    self.counter += 1
                    self.root.after(0, lambda: self.counter_label.config(
                        text=f"计数器: {self.counter} (K线更新正常运行)"))
                    time.sleep(1)
                except:
                    break
        
        threading.Thread(target=background_worker, daemon=True).start()
        
    def center_dialog_on_screen(self, dialog):
        """将对话框居中显示在屏幕中央"""
        dialog.update_idletasks()
        screen_width = dialog.winfo_screenwidth()
        screen_height = dialog.winfo_screenheight()
        
        # 从geometry获取尺寸
        geometry = dialog.geometry()
        if 'x' in geometry and '+' in geometry:
            size_part = geometry.split('+')[0]
            if 'x' in size_part:
                dialog_width, dialog_height = map(int, size_part.split('x'))
            else:
                dialog_width = dialog.winfo_reqwidth()
                dialog_height = dialog.winfo_reqheight()
        else:
            dialog_width = dialog.winfo_reqwidth()
            dialog_height = dialog.winfo_reqheight()
        
        x = (screen_width - dialog_width) // 2
        y = (screen_height - dialog_height) // 2
        dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
        
    def change_margin_mode(self):
        """切换保证金模式（模拟KlinePage的方法）"""
        symbol = self.current_valid_symbol
        
        # 检查是否已经有窗口打开
        if self._margin_dialog and self._margin_dialog.winfo_exists():
            self._margin_dialog.lift()
            self._margin_dialog.focus_set()
            return
            
        # 创建独立窗口
        dialog = Toplevel()
        dialog.title("切换保证金模式")
        dialog.geometry("300x150")
        
        try:
            dialog.iconbitmap(default='')
        except:
            pass
        dialog.attributes('-topmost', False)
        dialog.resizable(True, True)
        
        self._margin_dialog = dialog
        
        def on_dialog_close():
            self._margin_dialog = None
            # 恢复主程序操控
            self.root.grab_set()
            self.root.after(100, lambda: self.root.grab_release())
            dialog.destroy()
            
        dialog.protocol("WM_DELETE_WINDOW", on_dialog_close)
        self.center_dialog_on_screen(dialog)
        
        # 使主程序不可操控
        self.root.grab_release()
        dialog.grab_set()
        
        # 创建内容
        Label(dialog, text="💰 保证金模式", 
              font=("微软雅黑", 14, "bold"), fg="#9b59b6").pack(pady=20)
        
        mode_var = StringVar(value=self.current_margin_type)
        ttk.Radiobutton(dialog, text="全仓", value="CROSSED", variable=mode_var).pack(pady=5)
        ttk.Radiobutton(dialog, text="逐仓", value="ISOLATED", variable=mode_var).pack(pady=5)
        
        def apply_change():
            print(f"切换保证金模式到: {mode_var.get()}")
            self.current_margin_type = mode_var.get()
            on_dialog_close()
            
        Button(dialog, text="确定", command=apply_change, 
               bg="#9b59b6", fg="white", width=10).pack(pady=15)
        
    def change_leverage(self):
        """调整杠杆倍数（模拟KlinePage的方法）"""
        if self._leverage_dialog and self._leverage_dialog.winfo_exists():
            self._leverage_dialog.lift()
            self._leverage_dialog.focus_set()
            return
            
        dialog = Toplevel()
        dialog.title("调整杠杆倍数")
        dialog.geometry("300x200")
        
        try:
            dialog.iconbitmap(default='')
        except:
            pass
        dialog.attributes('-topmost', False)
        dialog.resizable(True, True)
        
        self._leverage_dialog = dialog
        
        def on_dialog_close():
            self._leverage_dialog = None
            self.root.grab_set()
            self.root.after(100, lambda: self.root.grab_release())
            dialog.destroy()
            
        dialog.protocol("WM_DELETE_WINDOW", on_dialog_close)
        self.center_dialog_on_screen(dialog)
        
        # 使主程序不可操控
        self.root.grab_release()
        dialog.grab_set()
        
        Label(dialog, text="⚡ 杠杆倍数", 
              font=("微软雅黑", 14, "bold"), fg="#e67e22").pack(pady=15)
        Label(dialog, text=f"当前杠杆: {self.current_leverage}x").pack(pady=5)
        
        leverage_var = IntVar(value=self.current_leverage)
        scale = ttk.Scale(dialog, from_=1, to=125, variable=leverage_var, orient='horizontal')
        scale.pack(fill=X, padx=20, pady=10)
        
        leverage_label = Label(dialog, text=f"选择杠杆: {self.current_leverage}x")
        leverage_label.pack(pady=5)
        
        def update_label(*args):
            leverage_label.config(text=f"选择杠杆: {leverage_var.get()}x")
        leverage_var.trace('w', update_label)
        
        def apply_leverage():
            print(f"调整杠杆倍数到: {leverage_var.get()}x")
            self.current_leverage = leverage_var.get()
            on_dialog_close()
            
        Button(dialog, text="确定", command=apply_leverage, 
               bg="#e67e22", fg="white", width=10).pack(pady=15)
        
    def display_symbol_config(self):
        """显示交易配置（模拟KlinePage的方法）"""
        if self._config_dialog and self._config_dialog.winfo_exists():
            self._config_dialog.lift()
            self._config_dialog.focus_set()
            return
            
        dialog = Toplevel()
        dialog.title(f"{self.current_valid_symbol} 交易配置")
        dialog.geometry("400x400")
        
        try:
            dialog.iconbitmap(default='')
        except:
            pass
        dialog.attributes('-topmost', False)
        dialog.resizable(True, True)
        
        self._config_dialog = dialog
        
        def on_dialog_close():
            self._config_dialog = None
            self.root.grab_set()
            self.root.after(100, lambda: self.root.grab_release())
            dialog.destroy()
            
        dialog.protocol("WM_DELETE_WINDOW", on_dialog_close)
        self.center_dialog_on_screen(dialog)
        
        # 使主程序不可操控
        self.root.grab_release()
        dialog.grab_set()
        
        # 创建内容
        Label(dialog, text=f"⚙️ {self.current_valid_symbol} 交易配置", 
              font=("微软雅黑", 14, "bold"), fg="#f39c12").pack(pady=20)
        
        config_info = [
            f"交易对: {self.current_valid_symbol}",
            f"当前杠杆: {self.current_leverage}x",
            f"保证金模式: {'全仓' if self.current_margin_type == 'CROSSED' else '逐仓'}",
            "价格步长: 0.01",
            "数量步长: 0.001",
            "最小下单数量: 0.001",
            "最小名义价值: 5.0"
        ]
        
        for info in config_info:
            Label(dialog, text=info, font=("微软雅黑", 10)).pack(pady=3)
        
        Button(dialog, text="关闭", command=on_dialog_close, 
               bg="#f39c12", fg="white", width=15).pack(pady=20)
    
    def run(self):
        try:
            self.root.mainloop()
        finally:
            self.is_running = False

if __name__ == "__main__":
    print("启动K线界面对话框测试...")
    print("验证独立窗口解决方案的效果")
    app = TestKlineDialogs()
    app.run()
