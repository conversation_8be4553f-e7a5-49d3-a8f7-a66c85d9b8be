import sys
import time
import os
import json
import threading
import traceback
import logging
from datetime import datetime
from decimal import Decimal, ROUND_HALF_EVEN
from tkinter import Tk, Frame, Button, Text, END, Scrollbar, VERTICAL, RIGHT, Y, LEFT, BOTH, TOP, Label, Entry, StringVar, ttk, messagebox, X, Toplevel, IntVar, Listbox
from binance.um_futures import UMFutures
from dotenv import load_dotenv
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import mplfinance as mpf
import pandas as pd
import matplotlib.font_manager as fm

# ===================== 参数配置 ======================
SYMBOL = 'BTCUSDT'               # 交易对
INIT_QUANTITY = 0.002            # 初始下单数量（保留，下单功能暂未集成）
INIT_PRICE = 60000               # 初始下单价格
# 以下参数主要用于 API 数据更新循环
LOOP_DELAY = 5                 # 每次循环延时（秒）

# ===================== 文件目录与初始化 ======================
ROOT_DIR = os.getcwd()
CONFIG_FILE = os.path.join(ROOT_DIR, 'config.json')
CURRENT_ORDERS_FILE = os.path.join(ROOT_DIR, 'current_orders.json')
POSITIONS_FILE = os.path.join(ROOT_DIR, 'positions.json')
HISTORY_ORDERS_FILE = os.path.join(ROOT_DIR, 'history_orders.json')
LOG_FILE = os.path.join(ROOT_DIR, 'martingale_log.txt')

def init_json_file(file_path, default_data):
    """如果文件不存在或解析出错，则初始化为默认数据"""
    try:
        with open(file_path, 'r') as f:
            json.load(f)
    except Exception:
        with open(file_path, 'w') as f:
            json.dump(default_data, f, indent=4, ensure_ascii=False)

# 默认配置（这里仅作为示例，现阶段不使用策略下单）
default_config = {
    "enable_strategy": False,
    "preset_orders": []
}

init_json_file(CONFIG_FILE, default_config)
init_json_file(CURRENT_ORDERS_FILE, [])        # 当前挂单记录（设为列表）
init_json_file(POSITIONS_FILE, [])             # 当前仓位记录（列表）
init_json_file(HISTORY_ORDERS_FILE, [])        # 历史订单记录

# ===================== 日志设置 ======================
logger = logging.getLogger()
logger.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler = logging.FileHandler(LOG_FILE, mode='a', encoding='utf-8')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)
stream_handler = logging.StreamHandler(sys.stdout)
stream_handler.setFormatter(formatter)
logger.addHandler(stream_handler)

# ===================== API 相关函数 ======================
def sync_time(client):
    """同步服务器时间，设置客户端时间偏移"""
    try:
        server_time = int(client.time()["serverTime"])
        local_time = int(time.time() * 1000)
        offset = server_time - local_time
        client.timestamp_offset = offset
        logging.info(f"服务器时间：{server_time}，本地时间：{local_time}，时间偏移：{offset}ms")
    except Exception as e:
        logging.error(f"同步时间出错：{e}")

def place_order(client, symbol, side, quantity, price, extra_params=None):
    """下限价订单（开仓）——保留接口，暂未在后台调用"""
    params = {
        'symbol': symbol,
        'side': side,
        'type': 'LIMIT',
        'timeInForce': 'GTC',
        'quantity': quantity,
        'price': price
    }
    if extra_params:
        params.update(extra_params)
    try:
        result = client.new_order(**params)
        logging.info(f"下单成功：{params}，返回：{result}")
        return result.get('orderId')
    except Exception as e:
        logging.error("下单错误：" + str(e))
        return None

def monitor_order(client, symbol, order_id, wait_time=60):
    """轮询检测订单是否成交（接口示例）"""
    start_time = time.time()
    while time.time() - start_time < wait_time:
        try:
            order_info = client.query_order(symbol=symbol, orderId=order_id)
            status = order_info.get('status', '')
            logging.info(f"订单 {order_id} 状态：{status}")
            if status == 'FILLED':
                return order_info
        except Exception as e:
            logging.error("查询订单状态出错：" + str(e))
        time.sleep(1)
    logging.info(f"订单 {order_id} 在 {wait_time} 秒内未成交")
    return None

def cancel_order(client, symbol, order_id):
    """取消未成交订单"""
    try:
        result = client.cancel_order(symbol=symbol, orderId=order_id)
        logging.info(f"取消订单 {order_id} 成功：{result}")
    except Exception as e:
        logging.error("取消订单出错：" + str(e))

def place_close_order(client, symbol, side, quantity, close_price):
    """下平仓单（平仓接口示例）"""
    params = {
        'symbol': symbol,
        'side': side,
        'type': 'LIMIT',
        'timeInForce': 'GTC',
        'quantity': quantity,
        'price': close_price
    }
    try:
        result = client.new_order(**params)
        logging.info(f"平仓单下单成功：{params}，返回：{result}")
        return result
    except Exception as e:
        logging.error("平仓单下单出错：" + str(e))
        return None

# ===================== 本地数据读写函数 ======================
def update_current_orders_from_api(client):
    """调用 API 获取当前挂单并写入 CURRENT_ORDERS_FILE"""
    try:
        # 调用 API 获取当前挂单，使用 get_open_orders 接口
        orders = client.get_orders()  # 改为使用 get_open_orders 并传入 SYMBOL
        with open(CURRENT_ORDERS_FILE, 'w', encoding='utf-8') as f:
            json.dump(orders, f, indent=4, ensure_ascii=False)
        logging.info("当前挂单数据已更新。")
    except Exception as e:
        logging.error("更新当前挂单数据出错: " + str(e))

def update_positions_from_api(client):
    """调用 API 获取账户信息，并写入 POSITIONS_FILE（取 positions 字段）"""
    try:
        account_info = client.account()
        positions = account_info.get("positions", [])
        with open(POSITIONS_FILE, 'w', encoding='utf-8') as f:
            json.dump(positions, f, indent=4, ensure_ascii=False)
        logging.info("账户仓位数据已更新。")
    except Exception as e:
        logging.error("更新仓位数据出错: " + str(e))

def data_update_loop(client):
    """后台循环更新当前挂单和仓位数据到 JSON 文件"""
    sync_time(client)
    while True:
        try:
            update_current_orders_from_api(client)
            update_positions_from_api(client)
        except Exception as e:
            logging.error("数据更新循环出错: " + str(e))
        time.sleep(LOOP_DELAY)

# ===================== 图形界面 ======================
class MainPage(Frame):
    """主界面：包含四个按钮切换到各个功能页面"""
    def __init__(self, master, switch_page_callback, client):
        super().__init__(master)
        self.switch_page_callback = switch_page_callback
        self.client = client
        self.create_widgets()
        self.start_price_update()
        
    def create_widgets(self):
        self.pack(fill=BOTH, expand=True)
        Label(self, text="主菜单", font=("宋体", 18)).pack(pady=20)
        
        # 实时价格显示
        self.price_label = Label(self, text="BTCUSDT: 加载中...", font=("Arial", 16), fg="green")
        self.price_label.pack(pady=10)
        
        # 功能按钮
        Button(self, text="查看日志", width=20,
               command=lambda: self.switch_page_callback("log")).pack(pady=10)
        Button(self, text="查看当前挂单", width=20,
               command=lambda: self.switch_page_callback("orders")).pack(pady=10)
        Button(self, text="查看当前仓位", width=20,
               command=lambda: self.switch_page_callback("positions")).pack(pady=10)
        Button(self, text="K线图表", width=20,
               command=lambda: self.switch_page_callback("kline")).pack(pady=10)

    def start_price_update(self):
        def update_loop():
            while True:
                try:
                    ticker = self.client.ticker_price(SYMBOL)
                    price = float(ticker['price'])
                    self.price_label.config(text=f"{SYMBOL}: {price:.2f}")
                except Exception as e:
                    logging.error(f"价格更新失败: {str(e)}")
                time.sleep(0.5)
        
        threading.Thread(target=update_loop, daemon=True).start()

class LogPage(Frame):
    """日志显示页面：显示 martingale_log.txt 文件内容，提供手动刷新"""
    def __init__(self, master, switch_page_callback):
        super().__init__(master)
        self.switch_page_callback = switch_page_callback
        self.create_widgets()
        self.refresh_log()
        
    def create_widgets(self):
        self.pack(fill=BOTH, expand=True)
        top_frame = Frame(self)
        top_frame.pack(fill="x", pady=5)
        Button(top_frame, text="返回主菜单", command=lambda: self.switch_page_callback("main")).pack(side="left", padx=5)
        Button(top_frame, text="刷新", command=self.refresh_log).pack(side="left", padx=5)
        self.text = Text(self, wrap='word')
        self.text.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar = Scrollbar(self, orient=VERTICAL, command=self.text.yview)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.text.config(yscrollcommand=scrollbar.set)
        
    def refresh_log(self):
        try:
            with open(LOG_FILE, 'r', encoding='utf-8') as f:
                data = f.read()
        except Exception as e:
            data = f"读取日志出错: {e}"
        self.text.delete("1.0", END)
        self.text.insert(END, data)

class OrdersPage(Frame):
    """当前挂单页面：将 current_orders.json 格式化后显示易读信息"""
    def __init__(self, master, switch_page_callback):
        super().__init__(master)
        self.switch_page_callback = switch_page_callback
        self.create_widgets()
        self.refresh_orders()
        
    def create_widgets(self):
        self.pack(fill=BOTH, expand=True)
        top_frame = Frame(self)
        top_frame.pack(fill="x", pady=5)
        Button(top_frame, text="返回主菜单", command=lambda: self.switch_page_callback("main")).pack(side="left", padx=5)
        Button(top_frame, text="刷新", command=self.refresh_orders).pack(side="left", padx=5)
        self.text = Text(self, wrap='word')
        self.text.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar = Scrollbar(self, orient=VERTICAL, command=self.text.yview)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.text.config(yscrollcommand=scrollbar.set)
        
    def refresh_orders(self):
        try:
            with open(CURRENT_ORDERS_FILE, 'r', encoding='utf-8') as f:
                orders = json.load(f)
            display_text = ""
            if orders:
                # 假设 orders 为列表或字典格式，下面分别处理
                if isinstance(orders, list):
                    for order in orders:
                        # 这里按订单内部字段格式化输出
                        order_id = order.get("orderId", "未知")
                        symbol = order.get("symbol", "")
                        side = order.get("side", "")
                        order_type = order.get("type", "")
                        quantity = order.get("quantity", "")
                        price = order.get("price", "")
                        update_time = order.get("update_time", "")
                        display_text += (f"订单编号: {order_id}\n"
                                         f"  交易对: {symbol}\n"
                                         f"  类型: {order_type} / {side}\n"
                                         f"  数量: {quantity}\n"
                                         f"  价格: {price}\n"
                                         f"  更新时间: {update_time}\n"
                                         "--------------------------\n")
                elif isinstance(orders, dict):
                    for order_id, info in orders.items():
                        params = info.get("order_params", {})
                        symbol = params.get("symbol", "")
                        side = params.get("side", "")
                        order_type = params.get("type", "")
                        quantity = params.get("quantity", "")
                        price = params.get("price", "")
                        update_time = info.get("update_time", "")
                        display_text += (f"订单编号: {order_id}\n"
                                         f"  交易对: {symbol}\n"
                                         f"  类型: {order_type} / {side}\n"
                                         f"  数量: {quantity}\n"
                                         f"  价格: {price}\n"
                                         f"  更新时间: {update_time}\n"
                                         "--------------------------\n")
            else:
                display_text = "当前无挂单记录。"
        except Exception as e:
            display_text = f"读取挂单数据出错: {e}"
        self.text.delete("1.0", END)
        self.text.insert(END, display_text)

class PositionsPage(Frame):
    """当前仓位页面：格式化显示 positions.json 中的数据"""
    def __init__(self, master, switch_page_callback):
        super().__init__(master)
        self.switch_page_callback = switch_page_callback
        self.create_widgets()
        self.refresh_positions()
        
    def create_widgets(self):
        self.pack(fill=BOTH, expand=True)
        top_frame = Frame(self)
        top_frame.pack(fill="x", pady=5)
        Button(top_frame, text="返回主菜单", command=lambda: self.switch_page_callback("main")).pack(side="left", padx=5)
        Button(top_frame, text="刷新", command=self.refresh_positions).pack(side="left", padx=5)
        self.text = Text(self, wrap='word')
        self.text.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar = Scrollbar(self, orient=VERTICAL, command=self.text.yview)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.text.config(yscrollcommand=scrollbar.set)
    
    def refresh_positions(self):
        try:
            with open(POSITIONS_FILE, 'r', encoding='utf-8') as f:
                positions = json.load(f)
            display_text = ""
            if positions:
                # 假设 positions 为列表，按每个仓位条目格式化输出
                for pos in positions:
                    symbol = pos.get("symbol", "")
                    positionAmt = pos.get("positionAmt", "")
                    entryPrice = pos.get("entryPrice", "")
                    unrealizedProfit = pos.get("unrealizedProfit", "")
                    display_text += (f"交易对: {symbol}\n"
                                     f"  持仓量: {positionAmt}\n"
                                     f"  未实现盈亏: {unrealizedProfit}\n"
                                     "--------------------------\n")
            else:
                display_text = "当前无仓位记录。"
        except Exception as e:
            display_text = f"读取仓位数据出错: {e}"
        self.text.delete("1.0", END)
        self.text.insert(END, display_text)

class KlinePage(Frame):
    """K线界面"""
    def __init__(self, master, switch_page_callback, client):
        super().__init__(master)
        self.client = client
        self.switch_page_callback = switch_page_callback
        self.symbols = []  # 存储所有交易对
        
        # 先加载交易对
        self.load_trading_symbols()
        if not self.symbols:  # 如果加载失败，使用默认值
            self.symbols = [SYMBOL]
            
        # 创建界面
        self.create_widgets()
        
        # 更新保证金和杠杆信息
        self.update_margin_leverage_info()
        self.load_kline_data()

    def load_trading_symbols(self):
        """加载所有可交易的交易对"""
        try:
            # 获取交易所信息
            info = self.client.exchange_info()
            # 获取所有可交易的交易对
            all_symbols = []
            for symbol_info in info['symbols']:
                if (symbol_info['status'] == 'TRADING' and 
                    symbol_info['contractType'] == 'PERPETUAL' and
                    symbol_info['symbol'].endswith('USDT')):  # 只获取USDT合约
                    all_symbols.append(symbol_info['symbol'])
            
            # 对交易对进行排序，优先显示主流币种
            priority_coins = ['BTC', 'ETH', 'SOL', 'BNB', 'XRP', 'DOT', 'ADA']
            
            def get_sort_key(symbol):
                # 移除USDT后缀
                base_coin = symbol.replace('USDT', '')
                # 如果是优先币种，返回其在列表中的索引
                if base_coin in priority_coins:
                    return (0, priority_coins.index(base_coin))
                # 其他币种按字母顺序排序
                return (1, base_coin)
            
            self.symbols = sorted(all_symbols, key=get_sort_key)
            logging.info(f"加载了 {len(self.symbols)} 个交易对")
            
        except Exception as e:
            logging.error(f"加载交易对失败: {str(e)}")
            self.symbols = []  # 加载失败时设为空列表

    def filter_symbols(self, pattern):
        """根据输入过滤交易对"""
        pattern = pattern.upper()
        self.filtered_symbols = [s for s in self.symbols if pattern in s]
        self.update_symbol_listbox()

    def update_symbol_listbox(self):
        """更新交易对列表显示"""
        self.symbol_listbox.delete(0, 'end')
        for symbol in self.filtered_symbols[:10]:  # 最多显示10个选项
            self.symbol_listbox.insert('end', symbol)
        
        # 如果有匹配项，显示列表框
        if self.filtered_symbols:
            self.symbol_listbox.place(x=self.symbol_entry.winfo_x(),
                                    y=self.symbol_entry.winfo_y() + self.symbol_entry.winfo_height(),
                                    width=self.symbol_entry.winfo_width())
        else:
            self.symbol_listbox.place_forget()

    def on_symbol_select(self, event=None):
        """当用户从下拉列表中选择一个交易对时"""
        self.on_symbol_change()
        self.symbol_combobox.selection_clear()  # 清除选择，避免重复触发
        
    def on_symbol_focus(self, event=None):
        """当交易对选择框获得焦点时"""
        # 优先显示主流币种
        priority_coins = ['BTC', 'ETH', 'SOL', 'BNB', 'XRP', 'DOT', 'ADA']
        def get_sort_key(symbol):
            base_coin = symbol.replace('USDT', '')
            if base_coin in priority_coins:
                return (0, priority_coins.index(base_coin))
            return (1, base_coin)
        
        sorted_symbols = sorted(self.symbols, key=get_sort_key)
        self.symbol_combobox['values'] = sorted_symbols
        
        # 展开下拉列表
        if not self.symbol_combobox.winfo_ismapped():
            self.symbol_combobox.event_generate('<Down>')  # 展开下拉列表
            
    def on_symbol_var_change(self, *args):
        """当输入内容变化时触发"""
        current_text = self.symbol_var.get().upper()
        
        # 如果输入框为空，显示所有交易对（常用在前）
        if not current_text:
            # 优先显示主流币种
            priority_coins = ['BTC', 'ETH', 'SOL', 'BNB', 'XRP', 'DOT', 'ADA']
            def get_sort_key(symbol):
                base_coin = symbol.replace('USDT', '')
                if base_coin in priority_coins:
                    return (0, priority_coins.index(base_coin))
                return (1, base_coin)
            
            sorted_symbols = sorted(self.symbols, key=get_sort_key)
            self.symbol_combobox['values'] = sorted_symbols
        else:
            # 有输入内容时，只显示匹配的交易对（不区分大小写）
            filtered_symbols = [s for s in self.symbols if current_text in s.upper()]
            self.symbol_combobox['values'] = filtered_symbols
        
        # 如果有匹配结果且列表未展开，则展开列表
        if self.symbol_combobox['values'] and not self.symbol_combobox.winfo_ismapped():
            self.symbol_combobox.event_generate('<Down>')
        
        # 保持输入框焦点，光标在末尾
        self.after(10, lambda: self.symbol_combobox.icursor('end'))
        self.after(10, lambda: self.symbol_combobox.focus_set())

    def on_symbol_change(self, event=None):
        """交易对变化时更新币种单位显示和保证金信息"""
        try:
            symbol = self.symbol_var.get().strip().upper()
            if not symbol.endswith('USDT'):
                symbol = f"{symbol}USDT"
            self.symbol_var.set(symbol)  # 更新为标准格式
            
            # 更新币种单位显示
            coin = symbol.replace("USDT", "")
            self.coin_unit_label.config(text=coin)
            
            # 更新保证金和杠杆信息
            self.update_margin_leverage_info()
            
            # 刷新K线图
            self.load_kline_data()
        except Exception as e:
            logging.error(f"更新交易对信息失败: {str(e)}")

    def on_symbol_return(self, event=None):
        """当用户按下回车键时"""
        if self.symbol_combobox['values']:
            # 如果没有选中项但有匹配结果，选择第一个
            if not self.symbol_combobox.get() in self.symbol_combobox['values']:
                self.symbol_var.set(self.symbol_combobox['values'][0])
            self.on_symbol_change()
        return 'break'

    def create_widgets(self):
        self.pack(fill=BOTH, expand=True)
        
        # 顶部控制栏
        top_frame = Frame(self)
        top_frame.pack(fill="x", pady=5)
        Button(top_frame, text="返回主菜单", 
               command=lambda: self.switch_page_callback("main")).pack(side="left", padx=5)
        
        # 指标选择
        self.indicator_var = StringVar(value="MA")
        indicator_menu = ttk.Combobox(top_frame, textvariable=self.indicator_var, 
                                    values=["None", "MA", "BOLL"], width=6)
        indicator_menu.pack(side="left", padx=5)
        
        # 交易对选择框
        self.symbol_var = StringVar(value=SYMBOL)
        self.symbol_combobox = ttk.Combobox(top_frame, textvariable=self.symbol_var, 
                                          values=self.symbols, width=12)
        self.symbol_combobox.pack(side="left", padx=5)
        
        # 绑定交易对选择框事件
        self.symbol_var.trace('w', self.on_symbol_var_change)  # 监听变量变化
        self.symbol_combobox.bind('<<ComboboxSelected>>', self.on_symbol_select)
        self.symbol_combobox.bind('<FocusIn>', self.on_symbol_focus)
        self.symbol_combobox.bind('<Return>', self.on_symbol_return)
        
        # K线周期选择
        self.interval_var = StringVar(value="15m")
        interval_options = ["1m", "5m", "15m", "30m", "1h", "4h", "1d"]
        interval_menu = ttk.Combobox(top_frame, textvariable=self.interval_var, 
                                   values=interval_options, width=5)
        interval_menu.pack(side="left", padx=5)
        
        Button(top_frame, text="刷新图表", command=self.load_kline_data).pack(side="left", padx=5)

        # 主体部分使用水平分割
        main_frame = Frame(self)
        main_frame.pack(fill=BOTH, expand=True)

        # 左侧K线图区域
        self.chart_frame = Frame(main_frame)
        self.chart_frame.pack(side=LEFT, fill=BOTH, expand=True)

        # 右侧交易面板
        trade_frame = Frame(main_frame, width=300, bg='#f0f0f0')
        trade_frame.pack(side=RIGHT, fill=Y, padx=10, pady=5)
        trade_frame.pack_propagate(False)  # 固定宽度

        # 保证金模式和杠杆设置
        margin_frame = Frame(trade_frame, bg='#f0f0f0')
        margin_frame.pack(fill=X, pady=5)
        self.margin_button = Button(margin_frame, text="加载中...", width=15,
               command=self.change_margin_type)
        self.margin_button.pack(side=LEFT, padx=5)
        self.leverage_button = Button(margin_frame, text="加载中...", width=15,
               command=self.change_leverage)
        self.leverage_button.pack(side=LEFT, padx=5)
        self.update_margin_leverage_info()  # 初始化状态显示

        # 可用资金显示
        balance_frame = Frame(trade_frame, bg='#f0f0f0')
        balance_frame.pack(fill=X, pady=5)
        Label(balance_frame, text="可用资金:", bg='#f0f0f0').pack(side=LEFT, padx=5)
        self.balance_label = Label(balance_frame, text="加载中...", bg='#f0f0f0')
        self.balance_label.pack(side=LEFT, padx=5)
        self.start_balance_update()

        # 开平仓选择
        position_frame = Frame(trade_frame, bg='#f0f0f0')
        position_frame.pack(fill=X, pady=5)
        Label(position_frame, text="持仓方向:", bg='#f0f0f0').pack(side=LEFT, padx=5)
        self.position_type_var = StringVar(value="开仓")
        ttk.Radiobutton(position_frame, text="开仓", value="开仓", 
                       variable=self.position_type_var, command=self.on_position_type_change).pack(side=LEFT, padx=5)
        ttk.Radiobutton(position_frame, text="平仓", value="平仓", 
                       variable=self.position_type_var, command=self.on_position_type_change).pack(side=LEFT, padx=5)

        # 交易类型选择
        type_frame = Frame(trade_frame, bg='#f0f0f0')
        type_frame.pack(fill=X, pady=5)
        Label(type_frame, text="订单类型:", bg='#f0f0f0').pack(side=LEFT, padx=5)
        self.order_type_var = StringVar(value="LIMIT")
        ttk.Radiobutton(type_frame, text="限价单", value="LIMIT", 
                       variable=self.order_type_var, command=self.on_order_type_change).pack(side=LEFT, padx=10)
        ttk.Radiobutton(type_frame, text="市价单", value="MARKET", 
                       variable=self.order_type_var, command=self.on_order_type_change).pack(side=LEFT, padx=10)

        # 价格输入区域
        price_frame = Frame(trade_frame, bg='#f0f0f0')
        price_frame.pack(fill=X, pady=5)
        Label(price_frame, text="价格(USDT):", bg='#f0f0f0').pack(side=LEFT, padx=5)
        self.price_var = StringVar()
        self.price_entry = Entry(price_frame, textvariable=self.price_var)
        self.price_entry.pack(side=LEFT, fill=X, expand=True, padx=5)

        # 数量输入区域
        amount_frame = Frame(trade_frame, bg='#f0f0f0')
        amount_frame.pack(fill=X, pady=5)
        Label(amount_frame, text="数量:", bg='#f0f0f0').pack(side=LEFT, padx=5)
        self.amount_var = StringVar()
        self.amount_entry = Entry(amount_frame, textvariable=self.amount_var)
        self.amount_entry.pack(side=LEFT, fill=X, expand=True, padx=5)

        # 修改数量单位选择部分
        unit_frame = Frame(amount_frame, bg='#f0f0f0')
        unit_frame.pack(side=RIGHT)
        self.amount_unit_var = StringVar(value="COIN")
        self.coin_unit_label = ttk.Radiobutton(unit_frame, text="BTC", value="COIN", 
                       variable=self.amount_unit_var, command=self.on_unit_change)
        self.coin_unit_label.pack(side=LEFT)
        ttk.Radiobutton(unit_frame, text="USDT", value="USDT", 
                       variable=self.amount_unit_var, command=self.on_unit_change).pack(side=LEFT)

        # 快捷比例按钮
        ratio_frame = Frame(trade_frame, bg='#f0f0f0')
        ratio_frame.pack(fill=X, pady=5)
        for ratio in ["25%", "50%", "75%", "100%"]:
            Button(ratio_frame, text=ratio, width=8,
                   command=lambda r=ratio: self.set_amount_ratio(r)).pack(side=LEFT, padx=2)

        # 交易按钮区域
        self.trade_buttons_frame = Frame(trade_frame, bg='#f0f0f0')
        self.trade_buttons_frame.pack(fill=X, pady=10)
        self.update_trade_buttons()

    def on_position_type_change(self):
        """开平仓类型改变时的处理"""
        self.update_trade_buttons()

    def update_trade_buttons(self):
        """根据开平仓选择更新交易按钮"""
        # 清除现有按钮
        for widget in self.trade_buttons_frame.winfo_children():
            widget.destroy()

        if self.position_type_var.get() == "开仓":
            # 开仓按钮
            Button(self.trade_buttons_frame, text="开多", width=15, height=2, bg='#77d879', fg='white',
                   command=lambda: self.place_order("LONG", "BUY")).pack(side=LEFT, padx=5, pady=5)
            Button(self.trade_buttons_frame, text="开空", width=15, height=2, bg='#d16d6d', fg='white',
                   command=lambda: self.place_order("SHORT", "SELL")).pack(side=LEFT, padx=5, pady=5)
        else:
            # 平仓按钮
            Button(self.trade_buttons_frame, text="平多", width=15, height=2, bg='#d16d6d', fg='white',
                   command=lambda: self.place_order("LONG", "SELL")).pack(side=LEFT, padx=5, pady=5)
            Button(self.trade_buttons_frame, text="平空", width=15, height=2, bg='#77d879', fg='white',
                   command=lambda: self.place_order("SHORT", "BUY")).pack(side=LEFT, padx=5, pady=5)

    def place_order(self, position_side, side):
        """下单处理"""
        try:
            symbol = self.symbol_var.get()
            order_type = self.order_type_var.get()
            quantity = float(self.amount_var.get())
            
            # 基本参数
            params = {
                'symbol': symbol,
                'side': side,
                'type': order_type,
                'positionSide': position_side,  # 设置持仓方向：LONG或SHORT
            }
            
            # 如果是USDT数量，需要转换为币数量
            if self.amount_unit_var.get() == "USDT":
                ticker = self.client.ticker_price(symbol)
                current_price = float(ticker['price'])
                quantity = quantity / current_price
                quantity = round(quantity, 3)  # 四位小数
            
            if order_type == 'LIMIT':
                try:
                    price = float(self.price_var.get())
                except ValueError:
                    messagebox.showerror("错误", "请输入有效的价格")
                    return
                    
                params.update({
                    'timeInForce': 'GTC',
                    'price': price,
                    'quantity': quantity
                })
            else:  # MARKET
                params['quantity'] = quantity

            # 调用API下单
            result = self.client.new_order(**params)
            messagebox.showinfo("下单成功", f"订单ID: {result['orderId']}\n方向: {position_side}\n数量: {quantity}")
            
        except Exception as e:
            messagebox.showerror("下单失败", str(e))

    def on_order_type_change(self):
        """订单类型改变时的处理"""
        if self.order_type_var.get() == "MARKET":
            self.price_entry.config(state='disabled')
        else:
            self.price_entry.config(state='normal')

    def set_amount_ratio(self, ratio):
        """设置数量比例"""
        try:
            # 获取账户信息
            account_info = self.client.account()
            available_balance = float(account_info.get('availableBalance', 0))
            percentage = float(ratio.strip('%')) / 100
            
            # 获取当前价格
            ticker = self.client.ticker_price(self.symbol_var.get())
            current_price = float(ticker['price'])
            
            # 获取当前杠杆倍数
            position_info = self.client.get_position_risk(symbol=self.symbol_var.get())
            leverage = float(position_info[0].get('leverage', 1))
            
            # 计算可用数量（考虑杠杆）
            if self.amount_unit_var.get() == "USDT":
                max_amount = available_balance * leverage
            else:  # COIN
                max_amount = (available_balance * leverage) / current_price
                
            self.amount_var.set(f"{max_amount * percentage:.4f}")
        except Exception as e:
            messagebox.showerror("错误", f"计算数量失败: {str(e)}")

    def load_kline_data(self):
        try:
            symbol = self.symbol_var.get()
            interval = self.interval_var.get()
            indicator = self.indicator_var.get()

            klines = self.client.klines(
                symbol=symbol,
                interval=interval,
                limit=100
            )

            # 设置微软雅黑字体
            font_path = 'C:/Windows/Fonts/msyh.ttc'  # 微软雅黑路径
            font_prop = fm.FontProperties(fname=font_path)
            font_name = font_prop.get_name()

            df = pd.DataFrame(klines, columns=[
                'Open time', 'Open', 'High', 'Low', 'Close', 'Volume',
                'Close time', 'Quote asset volume', 'Number of trades',
                'Taker buy base asset volume', 'Taker buy quote asset volume', 'Ignore'
            ])
            df['Open time'] = pd.to_datetime(df['Open time'], unit='ms')
            df = df.astype({
                'Open': float, 'High': float, 'Low': float,
                'Close': float, 'Volume': float
            })
            df.set_index('Open time', inplace=True)

            add_plots = []
            mav = None

            if indicator == "MA":
                mav = (5, 10, 20)
            elif indicator == "BOLL":
                df['BOLL_MID'] = df['Close'].rolling(window=20).mean()
                df['BOLL_STD'] = df['Close'].rolling(window=20).std()
                df['BOLL_UPPER'] = df['BOLL_MID'] + 2 * df['BOLL_STD']
                df['BOLL_LOWER'] = df['BOLL_MID'] - 2 * df['BOLL_STD']

                add_plots = [
                    mpf.make_addplot(df['BOLL_UPPER'], color='green'),
                    mpf.make_addplot(df['BOLL_MID'], color='blue'),
                    mpf.make_addplot(df['BOLL_LOWER'], color='red')
                ]

            # 生成新图
            my_style = mpf.make_mpf_style(
                base_mpf_style='charles',
                rc={'font.family': font_name}
            )
            fig, _ = mpf.plot(
                df,
                type='candle',
                style=my_style,
                title=f"{symbol} {interval} K线 - {indicator}",
                ylabel='价格',
                volume=False,
                mav=mav,
                addplot=add_plots,
                returnfig=True,
            )

            # 清理旧图
            if hasattr(self, 'canvas'):
                self.canvas.get_tk_widget().destroy()

            # 将图表放在左侧frame中
            self.canvas = FigureCanvasTkAgg(fig, master=self.chart_frame)
            self.canvas.get_tk_widget().pack(fill=BOTH, expand=True)
            self.canvas.draw()

        except Exception as e:
            logging.error(f"加载K线数据失败: {str(e)}")

    def start_balance_update(self):
        """启动可用资金更新线程"""
        def update_loop():
            while True:
                try:
                    account_info = self.client.account()
                    available_balance = float(account_info.get('availableBalance', 0))
                    self.balance_label.config(text=f"{available_balance:.2f} USDT")
                except Exception as e:
                    logging.error(f"更新可用资金失败: {str(e)}")
                time.sleep(2)  # 每2秒更新一次
        
        threading.Thread(target=update_loop, daemon=True).start()

    def update_margin_leverage_info(self):
        """更新保证金模式和杠杆倍数信息"""
        try:
            symbol = self.symbol_var.get()
            # 获取当前保证金模式和杠杆倍数
            position_info = self.client.get_position_risk(symbol=symbol)
            if not position_info:
                raise Exception("未获取到持仓信息")
                
            # 更新保证金模式按钮文字
            margin_type = position_info[0].get('marginType', '').upper()
            margin_text = "全仓" if margin_type == "CROSSED" else "逐仓"
            self.margin_button.config(text=f"{margin_text}")
            
            # 更新杠杆倍数按钮文字
            leverage = int(position_info[0].get('leverage', 1))
            self.leverage_button.config(text=f"{leverage}x")
            
            # 保存当前状态
            self.current_margin_type = margin_type
            self.current_leverage = leverage
            
            # 启动定时更新
            if not hasattr(self, '_update_thread'):
                self._update_thread = threading.Thread(target=self._auto_update_margin_info, daemon=True)
                self._update_thread.start()
                
        except Exception as e:
            logging.error(f"更新保证金和杠杆信息失败: {str(e)}")
            self.margin_button.config(text="获取失败")
            self.leverage_button.config(text="获取失败")

    def _auto_update_margin_info(self):
        """自动更新保证金和杠杆信息的后台线程"""
        while True:
            try:
                symbol = self.symbol_var.get()
                position_info = self.client.get_position_risk(symbol=symbol)
                if position_info:
                    margin_type = position_info[0].get('marginType', '').upper()
                    leverage = int(position_info[0].get('leverage', 1))
                    
                    # 只有当状态发生变化时才更新
                    if (not hasattr(self, 'current_margin_type') or 
                        margin_type != self.current_margin_type or 
                        leverage != self.current_leverage):
                        
                        margin_text = "全仓" if margin_type == "CROSSED" else "逐仓"
                        self.margin_button.config(text=f"{margin_text}")
                        self.leverage_button.config(text=f"{leverage}x")
                        self.current_margin_type = margin_type
                        self.current_leverage = leverage
            except Exception as e:
                logging.error(f"自动更新保证金和杠杆信息失败: {str(e)}")
            time.sleep(2)  # 每2秒更新一次

    def change_margin_type(self):
        """切换保证金模式"""
        try:
            symbol = self.symbol_var.get()
            if not hasattr(self, 'current_margin_type'):
                self.update_margin_leverage_info()
            
            # 创建对话框
            dialog = Toplevel(self)
            dialog.title("切换保证金模式")
            dialog.geometry("300x150")
            dialog.transient(self)  # 设置为主窗口的临时窗口
            dialog.grab_set()  # 模态对话框
            
            # 使用Frame包装单选按钮，方便设置背景色
            radio_frame = Frame(dialog, bg='white')
            radio_frame.pack(fill='both', expand=True, padx=20, pady=10)
            
            # 设置当前模式
            mode_var = StringVar(value=self.current_margin_type)
            
            # 创建单选按钮组
            style = ttk.Style()
            style.configure('Selected.TRadiobutton', background='#e6e6e6')
            
            crossed_radio = ttk.Radiobutton(radio_frame, text="全仓", value="CROSSED",
                          variable=mode_var, style='Selected.TRadiobutton' if self.current_margin_type == "CROSSED" else '')
            crossed_radio.pack(pady=5)
            
            isolated_radio = ttk.Radiobutton(radio_frame, text="逐仓", value="ISOLATED",
                          variable=mode_var, style='Selected.TRadiobutton' if self.current_margin_type == "ISOLATED" else '')
            isolated_radio.pack(pady=5)
            
            def apply_change():
                try:
                    new_mode = mode_var.get()
                    if new_mode != self.current_margin_type:  # 只在发生改变时发送请求
                        self.client.change_margin_type(symbol=symbol, marginType=new_mode)
                        messagebox.showinfo("成功", f"已切换到{'全仓' if new_mode == 'CROSSED' else '逐仓'}模式")
                        self.update_margin_leverage_info()  # 更新按钮显示
                    dialog.destroy()
                except Exception as e:
                    messagebox.showerror("错误", str(e))
                    dialog.destroy()
            
            Button(dialog, text="确定", command=apply_change).pack(pady=10)
            
        except Exception as e:
            messagebox.showerror("错误", str(e))

    def change_leverage(self):
        """调整杠杆倍数"""
        try:
            symbol = self.symbol_var.get()
            if not hasattr(self, 'current_leverage'):
                self.update_margin_leverage_info()
            
            # 获取最大杠杆倍数
            brackets = self.client.leverage_brackets(symbol=symbol)
            max_leverage = brackets[0]['brackets'][0]['initialLeverage']
            
            # 创建对话框
            dialog = Toplevel(self)
            dialog.title("调整杠杆倍数")
            dialog.geometry("300x200")
            dialog.transient(self)  # 设置为主窗口的临时窗口
            dialog.grab_set()  # 模态对话框
            
            # 使用Frame包装内容，设置统一背景色
            content_frame = Frame(dialog, bg='white')
            content_frame.pack(fill='both', expand=True, padx=20, pady=10)
            
            Label(content_frame, text=f"当前杠杆: {self.current_leverage}x", bg='white').pack(pady=5)
            Label(content_frame, text=f"最大杠杆: {max_leverage}x", bg='white').pack(pady=5)
            
            leverage_var = IntVar(value=self.current_leverage)
            scale = ttk.Scale(content_frame, from_=1, to=max_leverage, 
                            variable=leverage_var, orient='horizontal')
            scale.set(self.current_leverage)  # 设置当前值
            scale.pack(fill='x', pady=10)
            
            leverage_label = Label(content_frame, text=f"选择杠杆: {self.current_leverage}x", bg='white')
            leverage_label.pack(pady=5)
            
            def update_label(*args):
                leverage_label.config(text=f"选择杠杆: {leverage_var.get()}x")
            
            leverage_var.trace('w', update_label)
            
            def apply_leverage():
                try:
                    new_leverage = leverage_var.get()
                    if new_leverage != self.current_leverage:  # 只在发生改变时发送请求
                        self.client.change_leverage(
                            symbol=symbol, leverage=new_leverage)
                        messagebox.showinfo("成功", f"杠杆已调整为{new_leverage}x")
                        self.update_margin_leverage_info()  # 更新按钮显示
                    dialog.destroy()
                except Exception as e:
                    messagebox.showerror("错误", str(e))
                    dialog.destroy()
            
            Button(dialog, text="确定", command=apply_leverage).pack(pady=10)
            
        except Exception as e:
            messagebox.showerror("错误", str(e))

    def on_unit_change(self):
        """数量单位切换时自动转换数值"""
        try:
            if not self.amount_var.get():
                return
                
            amount = float(self.amount_var.get())
            symbol = self.symbol_var.get()
            
            if self.order_type_var.get() == "LIMIT":
                try:
                    price = float(self.price_var.get())
                except ValueError:
                    return
            else:
                # 获取当前市价
                ticker = self.client.ticker_price(symbol)
                price = float(ticker['price'])
            
            # 转换数量
            if self.amount_unit_var.get() == "USDT":
                # 从币种转换为USDT
                new_amount = amount * price
            else:
                # 从USDT转换为币种
                new_amount = amount / price
            
            self.amount_var.set(f"{new_amount:.4f}")
            
        except Exception as e:
            logging.error(f"转换数量单位失败: {str(e)}")

class TradingApp:
    def __init__(self, root, client):
        self.root = root
        self.client = client
        self.root.title("多功能交易软件")
        self.root.geometry("1000x800")
        self.current_page = None
        self.pages = {}
        self.create_pages()
        self.show_page("main")

    def create_pages(self):
        self.pages["main"] = MainPage(self.root, self.show_page, self.client)
        self.pages["log"] = LogPage(self.root, self.show_page)
        self.pages["orders"] = OrdersPage(self.root, self.show_page)
        self.pages["positions"] = PositionsPage(self.root, self.show_page)
        self.pages["kline"] = KlinePage(self.root, self.show_page, self.client)
        
    def show_page(self, page_name):
        for page in self.pages.values():
            page.pack_forget()
        self.current_page = self.pages.get(page_name)
        if self.current_page:
            self.current_page.pack(fill=BOTH, expand=True)

def start_data_update_loop(client):
    """启动后台线程更新数据"""
    t = threading.Thread(target=data_update_loop, args=(client,), daemon=True)
    t.start()

def main():
    # 加载环境变量，并初始化 API 客户端
    load_dotenv('1.env')
    API_KEY = os.getenv('API_KEY')
    SECRET_KEY = os.getenv('SECRET_KEY')
    um_futures_client = UMFutures(key=API_KEY, secret=SECRET_KEY)
    
    # 启动后台数据更新线程
    start_data_update_loop(um_futures_client)
    
    # 启动图形界面
    root = Tk()
    app = TradingApp(root, um_futures_client)  # 注意这里传入client参数
    root.mainloop()

if __name__ == '__main__':
    main()
