#!/usr/bin/env python3
"""
测试马丁策略按钮框架的简单脚本
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_martin_button():
    """测试马丁策略按钮是否正确添加"""
    try:
        # 导入主程序模块
        import ma19
        
        print("✅ 成功导入ma19模块")
        
        # 检查KlineDataPage类是否有马丁策略相关的方法
        kline_data_page_class = None
        for name in dir(ma19):
            obj = getattr(ma19, name)
            if hasattr(obj, '__name__') and 'KlineDataPage' in str(obj):
                kline_data_page_class = obj
                break
        
        if kline_data_page_class is None:
            print("❌ 未找到KlineDataPage类")
            return False
        
        # 检查是否有马丁策略相关的方法
        methods = dir(kline_data_page_class)
        martin_methods = [m for m in methods if 'martin' in m.lower()]
        
        print(f"✅ 找到KlineDataPage类")
        print(f"✅ 马丁策略相关方法: {martin_methods}")
        
        # 检查必要的方法是否存在
        required_methods = ['open_martin_test_dialog', 'test_martin_strategy']
        missing_methods = []
        for method in required_methods:
            if method not in methods:
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 缺少必要的方法: {missing_methods}")
            return False
        else:
            print("✅ 所有必要的马丁策略方法都已添加")
        
        print("\n🎉 马丁策略按钮框架测试通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    print("开始测试马丁策略按钮框架...")
    print("=" * 50)
    
    success = test_martin_button()
    
    print("=" * 50)
    if success:
        print("✅ 测试完成：马丁策略按钮框架已成功添加")
    else:
        print("❌ 测试失败：马丁策略按钮框架存在问题")
    
    return success

if __name__ == "__main__":
    main()
