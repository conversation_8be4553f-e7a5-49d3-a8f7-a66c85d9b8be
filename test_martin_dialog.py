#!/usr/bin/env python3
"""
测试马丁策略参数设置对话框的简单脚本
"""

import tkinter as tk
from tkinter import ttk, StringVar, IntVar, Frame, Label, Entry, Button, Toplevel, LEFT, RIGHT, X, BOTH, W

class TestDialog:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("测试马丁策略对话框")
        self.root.geometry("400x300")
        
        # 创建测试按钮
        test_btn = Button(self.root, text="打开马丁策略参数设置", command=self.open_martin_test_dialog)
        test_btn.pack(pady=50)
        
    def center_dialog(self, dialog):
        """将对话框居中显示在主窗口中间"""
        dialog.update_idletasks()  # 确保窗口尺寸已计算
        
        # 获取主窗口的位置和尺寸
        main_x = self.root.winfo_rootx()
        main_y = self.root.winfo_rooty()
        main_width = self.root.winfo_width()
        main_height = self.root.winfo_height()
        
        # 获取对话框的尺寸
        dialog_width = dialog.winfo_reqwidth()
        dialog_height = dialog.winfo_reqheight()
        
        # 计算居中位置
        x = main_x + (main_width - dialog_width) // 2
        y = main_y + (main_height - dialog_height) // 2
        
        # 设置对话框位置
        dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
        
    def open_martin_test_dialog(self):
        """弹出马丁策略测试参数设置窗口"""
        dialog = Toplevel(self.root)
        dialog.title("马丁策略测试参数设置")
        dialog.geometry("500x600")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 将窗口居中显示在主窗口中间
        self.center_dialog(dialog)
        
        # 创建滚动框架
        main_frame = Frame(dialog)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # 初始资金设置
        capital_frame = Frame(main_frame)
        capital_frame.pack(fill=X, pady=5)
        Label(capital_frame, text="初始资金:", font=("微软雅黑", 10, "bold")).pack(side=LEFT)
        initial_capital_var = StringVar(value="10000")
        Entry(capital_frame, textvariable=initial_capital_var, width=15).pack(side=LEFT, padx=10)
        
        # 分隔线
        ttk.Separator(main_frame, orient='horizontal').pack(fill=X, pady=10)
        
        # 马丁策略参数设置
        Label(main_frame, text="马丁策略参数设置:", font=("微软雅黑", 10, "bold")).pack(anchor=W, pady=(0, 5))
        
        # 存储各级别参数的变量
        martin_params = {}
        
        # 创建1%-15%的参数设置
        for percent in range(1, 16):
            param_frame = Frame(main_frame)
            param_frame.pack(fill=X, pady=2)
            
            # 跌幅标签
            Label(param_frame, text=f"{percent}%跌幅:", width=8, anchor=W).pack(side=LEFT)
            
            # 加仓金额
            Label(param_frame, text="加仓金额:").pack(side=LEFT, padx=(10, 5))
            add_amount_var = StringVar(value=str(1000 * percent))  # 默认值递增
            Entry(param_frame, textvariable=add_amount_var, width=10).pack(side=LEFT, padx=(0, 10))
            
            # 止盈涨幅
            Label(param_frame, text="止盈涨幅:").pack(side=LEFT, padx=(10, 5))
            profit_percent_var = StringVar(value="1.0")  # 默认1%止盈
            Entry(param_frame, textvariable=profit_percent_var, width=8).pack(side=LEFT, padx=(0, 5))
            Label(param_frame, text="%").pack(side=LEFT)
            
            # 保存变量引用
            martin_params[percent] = {
                'add_amount': add_amount_var,
                'profit_percent': profit_percent_var
            }
        
        # 分隔线
        ttk.Separator(main_frame, orient='horizontal').pack(fill=X, pady=10)
        
        # 爆仓停止选项
        stop_on_liquidation_var = IntVar(value=1)  # 默认选中
        stop_frame = Frame(main_frame)
        stop_frame.pack(fill=X, pady=5)
        ttk.Checkbutton(stop_frame, text="爆仓时停止测试", variable=stop_on_liquidation_var).pack(side=LEFT)
        
        # 按钮框架
        button_frame = Frame(main_frame)
        button_frame.pack(fill=X, pady=10)
        
        def on_confirm():
            try:
                initial_capital = float(initial_capital_var.get())
                
                # 收集所有马丁参数
                martin_config = {}
                for percent, vars_dict in martin_params.items():
                    add_amount = float(vars_dict['add_amount'].get())
                    profit_percent = float(vars_dict['profit_percent'].get())
                    martin_config[percent] = {
                        'add_amount': add_amount,
                        'profit_percent': profit_percent
                    }
                
                stop_on_liquidation = bool(stop_on_liquidation_var.get())
                
                print(f"初始资金: {initial_capital}")
                print(f"爆仓停止: {stop_on_liquidation}")
                print("马丁参数:")
                for percent, config in martin_config.items():
                    print(f"  {percent}%跌幅: 加仓{config['add_amount']}, 止盈{config['profit_percent']}%")
                
                dialog.destroy()
            except Exception as e:
                print(f"输入无效: {e}")
        
        def on_cancel():
            dialog.destroy()
        
        Button(button_frame, text="确定", command=on_confirm).pack(side=LEFT, padx=5)
        Button(button_frame, text="取消", command=on_cancel).pack(side=LEFT, padx=5)
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = TestDialog()
    app.run()
