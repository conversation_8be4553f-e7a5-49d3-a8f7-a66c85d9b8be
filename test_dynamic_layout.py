#!/usr/bin/env python3
"""
测试K线界面动态布局功能
验证窗口宽度变化时的布局响应
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_dynamic_layout():
    """测试动态布局功能"""
    try:
        # 导入必要的模块
        from ma18 import KlinePage
        from binance.client import Client
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("K线界面动态布局测试")
        root.geometry("800x600")
        
        # 创建一个简单的客户端（用于测试）
        class MockClient:
            def klines(self, **kwargs):
                return []
            def ticker_price(self, symbol):
                return {'price': '50000.0'}
            def account(self):
                return {'availableBalance': '1000.0'}
        
        client = MockClient()
        
        # 创建K线页面
        def switch_page_callback(page_name):
            print(f"切换到页面: {page_name}")
        
        kline_page = KlinePage(root, switch_page_callback, client, default_symbol="BTCUSDT")
        kline_page.pack(fill=tk.BOTH, expand=True)
        
        # 添加测试说明和控制面板
        info_frame = tk.Frame(root, bg='lightblue', height=120)
        info_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)
        info_frame.pack_propagate(False)
        
        info_label = tk.Label(info_frame, 
                             text="动态布局测试说明：\n"
                                  "• 窗口宽度 ≤ 700px：图表充满整个区域，交易面板隐藏\n"
                                  "• 窗口宽度 700-1000px：图表固定700px，交易面板逐渐显示\n"
                                  "• 窗口宽度 > 1000px：交易面板固定300px，图表区域继续扩展\n"
                                  "拖动窗口边缘测试布局变化效果",
                             bg='lightblue', font=('Arial', 9),
                             justify=tk.LEFT)
        info_label.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加测试按钮
        test_frame = tk.Frame(root, bg='lightgray', height=60)
        test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=5, pady=5)
        test_frame.pack_propagate(False)
        
        def set_window_size(width, height):
            """设置窗口大小"""
            root.geometry(f"{width}x{height}")
            root.update()
            # 手动触发布局更新
            kline_page.update_layout()
        
        def show_layout_info():
            """显示当前布局信息"""
            try:
                main_width = kline_page.main_frame.winfo_width()
                chart_width = kline_page.chart_frame.winfo_width()
                trade_width = kline_page.trade_frame.winfo_width() if kline_page.trade_frame.winfo_viewable() else 0
                
                info = f"窗口宽度: {main_width}px\n图表宽度: {chart_width}px\n交易面板宽度: {trade_width}px"
                messagebox.showinfo("布局信息", info)
            except Exception as e:
                messagebox.showerror("错误", f"获取布局信息失败: {str(e)}")
        
        # 测试按钮
        tk.Button(test_frame, text="600px宽", 
                 command=lambda: set_window_size(600, 600),
                 bg='lightcoral', font=('Arial', 9)).pack(side=tk.LEFT, padx=5, pady=10)
        
        tk.Button(test_frame, text="800px宽", 
                 command=lambda: set_window_size(800, 600),
                 bg='lightyellow', font=('Arial', 9)).pack(side=tk.LEFT, padx=5, pady=10)
        
        tk.Button(test_frame, text="1200px宽", 
                 command=lambda: set_window_size(1200, 600),
                 bg='lightgreen', font=('Arial', 9)).pack(side=tk.LEFT, padx=5, pady=10)
        
        tk.Button(test_frame, text="显示布局信息", 
                 command=show_layout_info,
                 bg='lightblue', font=('Arial', 9)).pack(side=tk.LEFT, padx=5, pady=10)
        
        tk.Button(test_frame, text="手动更新布局", 
                 command=kline_page.update_layout,
                 bg='plum', font=('Arial', 9)).pack(side=tk.LEFT, padx=5, pady=10)
        
        # 显示当前窗口大小
        size_label = tk.Label(test_frame, text="当前窗口大小: 800x600", 
                             bg='lightgray', font=('Arial', 9))
        size_label.pack(side=tk.RIGHT, padx=10, pady=10)
        
        def update_size_label():
            """更新窗口大小显示"""
            try:
                width = root.winfo_width()
                height = root.winfo_height()
                size_label.config(text=f"当前窗口大小: {width}x{height}")
            except:
                pass
            root.after(500, update_size_label)  # 每500ms更新一次
        
        # 启动大小显示更新
        update_size_label()
        
        # 运行测试
        print("开始K线界面动态布局测试...")
        print("请拖动窗口边缘测试布局变化效果")
        root.mainloop()
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_dynamic_layout()
